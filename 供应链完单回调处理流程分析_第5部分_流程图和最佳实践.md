# 供应链完单回调处理流程分析 - 第5部分：流程图和最佳实践

## 1. 完整流程图

### 1.1 主流程时序图
```mermaid
sequenceDiagram
    participant Supplier as 供应商系统
    participant Gateway as 网关层
    participant Facade as CallbackFacade
    participant Val<PERSON><PERSON> as 参数校验器
    participant Invoker as SupplierOrderFinishCallbackInvoker
    participant Redis as Redis缓存
    participant OrderService as 订单服务
    participant Builder as 数据转换器
    participant TripService as 行程结束服务
    participant Processor as 业务处理器
    participant DB as 数据库
    participant MQ as 消息队列

    Supplier->>Gateway: HTTP POST /callback/orderFinishCallback
    Gateway->>Facade: orderFinishCallback(request)
    Facade->>Validator: validate(request)
    Validator-->>Facade: 校验通过
    Facade->>Invoker: invoke(context)
    
    Note over Invoker: 第一层锁：订单处理锁
    Invoker->>Redis: tryLock(order:processing:lock:{orderNo})
    Redis-->>Invoker: 获取锁成功
    
    Note over Invoker: 第二层锁：完单回调锁
    Invoker->>Redis: setnx(order:finish:callback:Lock:{orderNo})
    Redis-->>Invoker: 设置成功
    
    Invoker->>OrderService: queryOrder(orderSerialNo)
    OrderService->>DB: 查询订单及关联信息
    DB-->>OrderService: 返回订单数据
    OrderService-->>Invoker: OrderVO
    
    Invoker->>Builder: build(request)
    Builder-->>Invoker: OrderFinishedSupplierBill
    
    Invoker->>TripService: finished(context)
    TripService->>Processor: 根据订单类型选择处理器
    Processor->>DB: 更新订单状态和信息
    Processor->>MQ: 发送异步通知
    Processor-->>TripService: 处理完成
    TripService-->>Invoker: 处理完成
    
    Invoker->>Redis: remove(order:finish:callback:Lock:{orderNo})
    Invoker->>Redis: unlock(order:processing:lock:{orderNo})
    Invoker-->>Facade: 成功响应
    Facade-->>Gateway: OrderFinishCallbackResponseDTO
    Gateway-->>Supplier: HTTP 200 OK
```

### 1.2 业务处理器选择流程图
```mermaid
flowchart TD
    A[开始处理] --> B{订单状态检查}
    B -->|已关闭/已取消| C[记录监控日志并返回]
    B -->|正常状态| D[记录操作日志]
    
    D --> E{订单类型判断}
    
    E -->|网约车| F[CarTripFinishedProcessorProxy]
    E -->|顺风车| G[SfcTripFinishedProcessor]
    E -->|上门接送| H[XCarTripFinishedProcessorProxy]
    
    F --> I{网约车子类型}
    I -->|API分销| J[DistributionCarTripFinishedProcessor]
    I -->|支付分支付| K[PointPayCarTripFinishedProcessor]
    I -->|在线支付| L[OnlinePayCarTripFinishedProcessor]
    
    G --> M[顺风车处理逻辑]
    
    H --> N{上门接送支付类型}
    N -->|支付分支付| O[XCarPointPayCarTripFinishedProcessor]
    N -->|在线支付| P[XCarOnlinePayCarTripFinishedProcessor]
    
    J --> Q[价格计算]
    K --> Q
    L --> Q
    M --> Q
    O --> Q
    P --> Q
    
    Q --> R[订单更新]
    R --> S[后续通知]
    S --> T[结束]
```

### 1.3 双重锁机制流程图
```mermaid
flowchart TD
    A[接收回调请求] --> B[尝试获取订单处理锁]
    B --> C{获取成功?}
    C -->|否| D[返回并发异常]
    C -->|是| E[尝试获取完单回调锁]
    E --> F{获取成功?}
    F -->|否| G[释放订单处理锁]
    G --> H[返回并发异常]
    F -->|是| I[执行业务逻辑]
    I --> J[释放完单回调锁]
    J --> K[释放订单处理锁]
    K --> L[返回处理结果]
```

## 2. 关键业务场景处理细节

### 2.1 网约车在线支付完单场景
```mermaid
flowchart TD
    A[网约车在线支付完单] --> B[价格计算服务]
    B --> C[更新订单信息]
    C --> D[推送财务结算信息]
    D --> E{需要补差?}
    E -->|是| F[补差支付流程]
    E -->|否| G{需要退差价?}
    F --> G
    G -->|是| H[退差价流程]
    G -->|否| I[更新订单状态为已关闭]
    H --> I
    I --> J[里程后返]
    J --> K[推送通知]
    K --> L[推送灵动岛]
    L --> M[完成]
```

### 2.2 顺风车拼单成功场景
```mermaid
flowchart TD
    A[顺风车完单] --> B[价格计算]
    B --> C[更新订单信息]
    C --> D{是否拼单成功?}
    D -->|是| E[拼单成功处理]
    D -->|否| F[普通完单处理]
    E --> G{订单是否已支付?}
    F --> G
    G -->|是| H[已支付完单流程]
    G -->|否| I{是否延后支付?}
    H --> J[更新为已关闭状态]
    I -->|是| K[延后支付处理]
    I -->|否| L[未支付订单处理]
    J --> M[里程后返和推送]
    K --> N[完成]
    L --> N
    M --> N
```

### 2.3 支付分扣款场景
```mermaid
flowchart TD
    A[支付分支付完单] --> B[价格计算]
    B --> C[更新订单信息]
    C --> D[获取支付分类型]
    D --> E[调用支付分扣款]
    E --> F{扣款成功?}
    F -->|是| G[添加扣款成功标签]
    F -->|否| H[扣款失败处理]
    G --> I[更新订单状态为已关闭]
    H --> J[记录失败日志]
    I --> K[里程后返和推送]
    J --> L[完成]
    K --> L
```

## 3. 异常场景和边界条件处理

### 3.1 异常场景分类
```mermaid
mindmap
  root((异常场景))
    并发异常
      订单处理锁获取失败
      完单回调锁获取失败
      重复请求处理
    业务异常
      订单状态异常
      订单不存在
      金额差异过大
      支付失败
    系统异常
      数据库连接异常
      Redis连接异常
      外部服务调用失败
      网络超时
    数据异常
      请求参数格式错误
      必填字段缺失
      数据类型转换错误
```

### 3.2 异常处理策略
| 异常类型 | 处理策略 | 响应方式 | 是否重试 |
|---------|---------|---------|---------|
| 并发异常 | 直接返回失败 | 返回并发异常错误码 | 否 |
| 订单状态异常 | 记录监控日志 | 返回成功(幂等) | 否 |
| 数据库异常 | 记录错误日志 | 返回系统错误 | 是 |
| 参数校验失败 | 参数校验器拦截 | 返回参数错误 | 否 |
| 外部服务异常 | 降级处理 | 继续核心流程 | 是 |

### 3.3 边界条件处理
```java
// 1. 订单已完成状态处理
if (order.isClosed() || order.isCanceled()) {
    LoggerUtils.warn(log, "存在交易侧已取消 | 已完结 状态的订单，供应链请求完单");
    DataMonitorUtils.dataMonitorLog(order, "行程结束", SUPPLIER_STATE_MISMATCH);
    return; // 直接返回，保证幂等性
}

// 2. 金额差异过大处理
if (MoneyUtils.isDifferenceExceedsPercentage(orderAmount, supplierAmount, threshold)) {
    log.warn("订单金额和供应商账单金额差异超过{}%", threshold);
    // 记录监控日志，但不阻断流程
}

// 3. 零元订单处理
if (order.isFreeMadaOrder()) {
    refundForFree(context); // 特殊退款逻辑
}

// 4. 风险订单处理
if (supplierBill.isDistributionHitRisk(order)) {
    newCarPrice = new Money(); // 风险订单金额清零
    newRealPrice = new Money();
}
```

## 4. 性能指标和监控告警

### 4.1 关键性能指标 (KPI)
| 指标类型 | 指标名称 | 目标值 | 监控方式 |
|---------|---------|--------|---------|
| 响应时间 | 接口平均响应时间 | < 500ms | 实时监控 |
| 吞吐量 | 每秒处理请求数 | > 1000 TPS | 实时监控 |
| 成功率 | 接口成功率 | > 99.9% | 实时监控 |
| 并发控制 | 锁获取成功率 | > 95% | 实时监控 |
| 数据一致性 | 金额差异告警率 | < 1% | 定时检查 |

### 4.2 监控告警配置
```yaml
# 告警规则配置示例
alerts:
  - name: "完单回调响应时间过长"
    condition: "avg_response_time > 1000ms"
    duration: "5m"
    severity: "warning"
    
  - name: "完单回调成功率过低"
    condition: "success_rate < 99%"
    duration: "2m"
    severity: "critical"
    
  - name: "订单金额差异过大"
    condition: "amount_diff_rate > 5%"
    duration: "1m"
    severity: "warning"
    
  - name: "并发锁获取失败率过高"
    condition: "lock_failure_rate > 10%"
    duration: "3m"
    severity: "warning"
```

### 4.3 监控大盘指标
```mermaid
graph LR
    A[监控大盘] --> B[实时指标]
    A --> C[业务指标]
    A --> D[技术指标]
    
    B --> B1[QPS]
    B --> B2[响应时间]
    B --> B3[错误率]
    
    C --> C1[完单成功率]
    C --> C2[金额差异率]
    C --> C3[订单状态分布]
    
    D --> D1[锁获取耗时]
    D --> D2[数据库查询耗时]
    D --> D3[缓存命中率]
```

## 5. 开发最佳实践

### 5.1 代码规范
```java
// 1. 统一的异常处理
try {
    // 业务逻辑
} catch (LYException e) {
    // 业务异常处理
    return buildFailureResponse(request, e.getError());
} catch (Exception e) {
    // 系统异常处理
    LoggerUtils.error(log, "[{}] error msg:{}", e, serviceName, e.getMessage());
    return buildFailureResponse(request, ERROR.sysErr(e.getMessage()));
}

// 2. 统一的日志记录
LoggerUtils.info(log, "[{}][{}] 开始处理, orderNo:{}", className, methodName, orderNo);
LoggerUtils.info(log, "[{}][{}] 处理完成, orderNo:{}, cost:{}ms", className, methodName, orderNo, cost);

// 3. 统一的参数校验
dtoNotNullValidate(request, "request");
dtoNotStringEmptyValidate(request.getOrderSerialNo(), "orderSerialNo");
dtoNotStringEmptyValidate(request.getSupplierCode(), "supplierCode");
```

### 5.2 数据库操作规范
```java
// 1. 事务管理
@Transactional(rollbackFor = Exception.class)
public void updateOrderForTripFinished(OrderVO order, OrderBillWrapper details) {
    // 多表更新操作
}

// 2. 批量查询优化
List<OrderInfoDO> orders = orderInfoDAO.queryByOrderSerialNos(orderSerialNos, env);

// 3. 索引优化
// 确保orderSerialNo字段有索引
// 查询条件尽量使用索引字段
```

### 5.3 缓存使用规范
```java
// 1. 缓存键命名规范
String cacheKey = String.format("order:info:%s", orderSerialNo);

// 2. 缓存过期时间设置
redisTemplate.setex(cacheKey, 3600, orderInfo); // 1小时过期

// 3. 缓存穿透防护
if (orderInfo == null) {
    redisTemplate.setex(cacheKey, 300, "NULL"); // 空值缓存5分钟
}
```

## 6. 运维最佳实践

### 6.1 部署策略
```yaml
# 滚动部署配置
deployment:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  
# 健康检查配置
healthCheck:
  path: /health
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3
```

### 6.2 容量规划
| 资源类型 | 配置建议 | 扩容阈值 |
|---------|---------|---------|
| CPU | 4核起步 | CPU使用率 > 70% |
| 内存 | 8GB起步 | 内存使用率 > 80% |
| 数据库连接池 | 50个连接 | 连接使用率 > 80% |
| Redis连接池 | 20个连接 | 连接使用率 > 80% |
| 线程池 | 200个线程 | 队列长度 > 1000 |

### 6.3 故障处理流程
```mermaid
flowchart TD
    A[故障告警] --> B[故障确认]
    B --> C{故障级别}
    C -->|P0| D[立即处理]
    C -->|P1| E[1小时内处理]
    C -->|P2| F[4小时内处理]
    
    D --> G[故障定位]
    E --> G
    F --> G
    
    G --> H[应急处理]
    H --> I[根因分析]
    I --> J[永久修复]
    J --> K[复盘总结]
```

## 7. 新人上手指南

### 7.1 环境搭建
```bash
# 1. 克隆代码
git clone https://github.com/company/shared-mobility-trade-core.git

# 2. 安装依赖
mvn clean install

# 3. 配置数据库
# 修改application.properties中的数据库配置

# 4. 启动Redis
docker run -d -p 6379:6379 redis:latest

# 5. 启动应用
mvn spring-boot:run
```

### 7.2 调试技巧
```java
// 1. 本地调试断点设置
// 在SupplierOrderFinishCallbackInvoker.invoke()方法设置断点

// 2. 日志级别调整
logging.level.com.ly.travel.car.tradecore=DEBUG

// 3. 模拟请求工具
// 使用Postman或curl发送测试请求
curl -X POST http://localhost:8080/callback/orderFinishCallback \
  -H "Content-Type: application/json" \
  -d @test_data.json
```

### 7.3 常见问题排查
| 问题现象 | 可能原因 | 排查方法 |
|---------|---------|---------|
| 接口超时 | 数据库查询慢 | 检查SQL执行计划 |
| 锁获取失败 | 并发量过大 | 检查Redis连接和锁超时时间 |
| 订单状态异常 | 数据不一致 | 检查订单状态变更日志 |
| 金额计算错误 | 价格规则变更 | 检查价格计算逻辑 |

## 8. 总结

### 8.1 核心设计亮点
1. **双重锁机制**：保证并发安全和幂等性
2. **策略模式**：支持多种订单类型和支付方式
3. **异步处理**：提高系统响应速度
4. **监控完善**：全方位的监控和告警
5. **容错设计**：优雅的异常处理和降级策略

### 8.2 技术栈总结
- **框架**：Spring Boot + Dubbo
- **数据库**：MySQL + MyBatis
- **缓存**：Redis + Redisson
- **消息队列**：RocketMQ
- **监控**：自研监控系统
- **配置中心**：自研配置中心

### 8.3 业务价值
1. **高可用性**：99.9%的服务可用性
2. **高性能**：支持千级TPS的并发处理
3. **数据一致性**：保证订单状态和金额的准确性
4. **可扩展性**：支持新的订单类型和支付方式
5. **可维护性**：清晰的代码结构和完善的文档

---
*本文档是供应链完单回调处理流程分析的第5部分，完整分析了整个系统的流程图和最佳实践。至此，整个分析文档已完成。*
