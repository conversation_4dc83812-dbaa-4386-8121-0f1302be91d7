<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE table SYSTEM "http://sources.alipay.net/svn/dtd/table-config-1.0.dtd">

<table sqlname="car_passenger_info">
    <!-- 不需要配置的列请删除 -->

    <sql id="PassengerInfo.columns">
        id, passenger_id, order_serial_no, name, link_phone, area_code,
        backup_phone,passenger_count,backup_phone_area_code,adult_count,child_count,luggage_count,wechat, trace_id,
        member_id, is_delete, CreateTime, UpdateTime, env ,virtual_phone
    </sql>

    <operation name="insert" paramtype="object">
        <sql>
            INSERT INTO car_passenger_info (
            passenger_id ,order_serial_no ,name ,link_phone ,area_code ,passenger_count
            <isNotEmpty prepend=", " property="backupPhone">
                backup_phone
            </isNotEmpty>
            <isNotEmpty prepend=", " property="backupPhoneAreaCode">
                backup_phone_area_code
            </isNotEmpty>
            <isNotEmpty prepend=", " property="adultCount">
                adult_count
            </isNotEmpty>
            <isNotEmpty prepend=", " property="childCount">
                child_count
            </isNotEmpty>
            <isNotEmpty prepend=", " property="luggageCount">
                luggage_count
            </isNotEmpty>
            <isNotEmpty prepend=", " property="wechat">
                wechat
            </isNotEmpty>
            ,trace_id ,member_id ,is_delete ,CreateTime ,UpdateTime ,env,virtual_phone,phone_suffix,modify_times
            ) VALUES (
            ? ,? ,? ,? ,? , ?
            <isNotEmpty prepend=", " property="backupPhone">
                ?
            </isNotEmpty>
            <isNotEmpty prepend=", " property="backupPhoneAreaCode">
                ?
            </isNotEmpty>
            <isNotEmpty prepend=", " property="adultCount">
                ?
            </isNotEmpty>
            <isNotEmpty prepend=", " property="childCount">
                ?
            </isNotEmpty>
            <isNotEmpty prepend=", " property="luggageCount">
                ?
            </isNotEmpty>
            <isNotEmpty prepend=", " property="wechat">
                ?
            </isNotEmpty>
            ,? ,? ,? ,now() ,now() ,?, ?, ?, ?
            )
        </sql>
    </operation>

    <operation name="updateLinkPhone" paramtype="parameterObject">
        <sql>
            UPDATE car_passenger_info SET
            UpdateTime = now()
            <isNotEmpty prepend=", " property="linkPhone">
                link_phone = #linkPhone#
            </isNotEmpty>
            <isNotEmpty prepend=", " property="areaCode">
                area_code = #areaCode#
            </isNotEmpty>
            <isNotNull prepend=", " property="backupPhoneAreaCode">
                backup_phone_area_code = #backupPhoneAreaCode#
            </isNotNull>
            <isNotNull prepend=", " property="backupPhone">
                backup_phone = #backupPhone#
            </isNotNull>
            <isNotNull prepend=", " property="wechat">
                wechat = #wechat#
            </isNotNull>
            <isNotEmpty prepend=", " property="virtualPhone">
                virtual_phone = #virtualPhone#
            </isNotEmpty>
            <isNotEmpty prepend=", " property="name">
                name = #name#
            </isNotEmpty>
            <isNotEmpty prepend=", " property="modifyTimes">
                modify_times = #modifyTimes#
            </isNotEmpty>
            WHERE
            order_serial_no = #orderSerialNo#
        </sql>
    </operation>

    <!--根据订单号查询-->
    <operation name="queryByOrderSerialNo" multiplicity="one">
        <sql>
            SELECT
            <include refid="PassengerInfo.columns"/>
            FROM car_passenger_info
            WHERE
            order_serial_no = #orderSerialNo# and is_delete = 0
        </sql>
    </operation>

    <!--根据订单号查询-->
    <operation name="queryByOrderNos" multiplicity="many">
        <sql>
            SELECT
            <include refid="PassengerInfo.columns"/>
            FROM car_passenger_info
            WHERE
            <iterate prepend="order_serial_no in " property="orderNos" open="("
                     close=")" conjunction=",">
                #orderNos[]#
            </iterate>
            and is_delete = #isDelete#
        </sql>
    </operation>
</table>
