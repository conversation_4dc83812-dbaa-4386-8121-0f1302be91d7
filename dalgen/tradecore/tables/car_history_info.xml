<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE table SYSTEM "http://sources.alipay.net/svn/dtd/table-config-1.0.dtd">

<table sqlname="car_history_info">
	<!-- 不需要配置的列请删除 -->
	
	<sql id="HistoryInfo.columns">
		id, order_serial_no, member_id, is_delete, CreateTime, UpdateTime, env, history_type, history_scene, history_value 
	</sql>
	
	<operation name="insert" paramtype="object">
		<sql>
			INSERT INTO car_history_info (
	        	order_serial_no ,member_id ,is_delete ,CreateTime ,UpdateTime ,env ,history_type ,history_scene ,history_value
	        ) VALUES (
	        	? ,? ,? ,now() ,now() ,? ,? ,? ,?
	        )
		</sql>		
	</operation>

	<!-- 废弃指定订单指定类型历史 -->
	<operation name="discard" paramtype="primitive">
		<sql>
			UPDATE car_history_info
			SET UpdateTime = now() ,is_delete = 1
			WHERE order_serial_no = ? AND history_type = ? AND history_scene = ? AND env = ? AND is_delete = 0
		</sql>
	</operation>


	<!-- 查询历史 -->
	<operation name="queryHistoryInfo" multiplicity="many">
		<sql>
			SELECT
			<include refid="HistoryInfo.columns"/>
			FROM car_history_info
			WHERE
			order_serial_no = ? and is_delete = 0 AND env = ?
			<isNotEmpty prepend="AND" property="historyType">
				history_type = ?
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="historyScene">
				history_scene = ?
			</isNotEmpty>
			ORDER BY CreateTime DESC
		</sql>
	</operation>

</table>
