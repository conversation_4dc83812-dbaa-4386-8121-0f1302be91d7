# 供应链完单回调处理流程分析 - 第3部分：业务处理服务

## 1. OrderTripFinishedService 核心业务逻辑

### 1.1 OrderTripFinishedServiceImpl.finished() 主流程
```java
@Override
public void finished(OrderTripFinishedContext context) {
    OrderVO order = context.getOrder();

    // 订单已关闭或者已取消
    if (order.isClosed() || order.isCanceled()) {
        LoggerUtils.warn(log, "存在交易侧已取消 | 已完结 状态的订单，供应链请求完单，请检查!!，订单号:{}", order.getOrderSerialNo());
        DataMonitorUtils.dataMonitorLog(order,
                "行程结束",
                DataMonitorUtils.DataMonitorEnum.SUPPLIER_STATE_MISMATCH);
        return;
    }

    // 行程结束 执行业务逻辑前记录日志, 目的：区分用户操作/供应链通知
    logBeforeProcess(BEFORE_TRIP_END_LOG, order);

    // 网约车行程结束
    if (order.isCar()) {
        carTripFinishedProcessor.finished(context);
    }

    // 顺风车行程结束
    if (order.isSfc()) {
        sfcTripFinishedProcessor.finished(context);
    }

    // 上门接送行程结束
    if (order.isXCar()) {
        xCarTripFinishedProcessorProxy.finished(context);
    }
    
    amountErrorLog(context.getOrder());
    // 火车票保底票通知
    notifyGuaranteeTrain(context.getOrder());
    // 通知供应链订单支付成功
    supplierService.syncPaid(context.getOrder(), PaySceneTypeEnum.ORDER);
    // 行程结束通知
    tracerService.notify(context.getOrder(), UserSceneEnum.TRIP_END_NOTICE);
}
```

### 1.2 处理流程详细分析

#### 步骤1：订单状态预检查
```java
if (order.isClosed() || order.isCanceled()) {
    LoggerUtils.warn(log, "存在交易侧已取消 | 已完结 状态的订单，供应链请求完单，请检查!!");
    DataMonitorUtils.dataMonitorLog(order, "行程结束", DataMonitorUtils.DataMonitorEnum.SUPPLIER_STATE_MISMATCH);
    return;
}
```

**检查逻辑**：
- 如果订单已关闭(`isClosed()`)或已取消(`isCanceled()`)，直接返回
- 记录状态不匹配的监控日志
- 这种情况表明供应链状态与交易侧状态不一致

#### 步骤2：操作日志记录
```java
logBeforeProcess(BEFORE_TRIP_END_LOG, order);
```
- 记录行程结束前的操作日志
- 用于区分是用户主动操作还是供应链通知

#### 步骤3：按订单类型分发处理
系统根据订单类型选择不同的处理器：

**网约车订单**：
```java
if (order.isCar()) {
    carTripFinishedProcessor.finished(context);
}
```

**顺风车订单**：
```java
if (order.isSfc()) {
    sfcTripFinishedProcessor.finished(context);
}
```

**上门接送订单**：
```java
if (order.isXCar()) {
    xCarTripFinishedProcessorProxy.finished(context);
}
```

#### 步骤4：后续通知处理
```java
// 金额异常日志
amountErrorLog(context.getOrder());

// 火车票保底票通知
notifyGuaranteeTrain(context.getOrder());

// 通知供应链订单支付成功
supplierService.syncPaid(context.getOrder(), PaySceneTypeEnum.ORDER);

// 行程结束通知
tracerService.notify(context.getOrder(), UserSceneEnum.TRIP_END_NOTICE);
```

## 2. 网约车处理器 - CarTripFinishedProcessorProxy

### 2.1 处理器选择逻辑
```java
@Override
public void finished(OrderTripFinishedContext context) {
    OrderVO order = context.getOrder();

    // API分销订单
    if (order.isDistributionOrder()){
        distributionCarTripFinishedProcessor.finished(context);
        return;
    }

    // 支付分支付
    if (order.isPaymentPoint()) {
        pointPayCarTripFinishedProcessor.finished(context);
        return;
    }

    // 在线支付
    if (order.isOnlinePay()) {
        onlinePayCarTripFinishedProcessor.finished(context);
        return;
    }
}
```

### 2.2 处理器类型说明

#### 2.2.1 DistributionCarTripFinishedProcessor (API分销订单)
```java
@Override
public void finished(OrderTripFinishedContext context) {
    OrderVO order = context.getOrder();

    // 计算最新的订单乘客应付金额
    CarPriceRet ret = priceCalService.pricingForTripFinished(context, context.getDetails());
    OrderBillWrapper priceWrapper = ret.getBillWrapper();

    // 行程结束更新订单信息
    orderService.updateOrderForTripFinished(order, priceWrapper);

    // 通知分销方订单状态变更
    distributionService.notifyOrderStateForApiDistribution(order, OrderState.TRIP_FINISHED, false);

    if (order.isPaid()){
        // 更新订单状态为已关闭
        orderService.updateOrderState(order, UserState.CLOSED, OrderState.TRIP_FINISHED, OrderState.ORDER_CLOSED);
        distributionService.notifyOrderStateForApiDistribution(order, OrderState.ORDER_CLOSED, false);
    }

    // 订单搜索报价日志链路追踪
    if (order.isSendTracePriceLog()) {
        orderPriceTraceLogProducer.sendMessage(OrderPriceTraceLogSource.buildFinishedLog(order));
    }
}
```

#### 2.2.2 OnlinePayCarTripFinishedProcessor (在线支付)
```java
@Override
public void finished(OrderTripFinishedContext context) {
    OrderVO order = context.getOrder();

    // 计算最新的订单乘客应付金额
    CarPriceRet ret = priceCalService.pricingForTripFinished(context, context.getDetails());
    OrderBillWrapper priceWrapper = ret.getBillWrapper();

    // 行程结束更新订单信息
    orderService.updateOrderForTripFinished(order, priceWrapper);

    // 行程结束推送财务结算信息
    payService.settlement(context);

    // 需要补差
    if (priceWrapper.isNeedBalancePay()) {
        LoggerUtils.info(log, "[OnlinePayCarTripFinishedProcessor][finished] 网约车在线支付行程结束需要补差, orderNo:{}", order.getOrderSerialNo());
        balancePayProcess(context, priceWrapper);
    }

    // 需要退差价
    if (priceWrapper.isNeedRefundDiff()) {
        LoggerUtils.info(log, "[OnlinePayCarTripFinishedProcessor][finished] 网约车在线支付行程结束需要退差价, orderNo:{}", order.getOrderSerialNo());
        refundDiffProcess(context, priceWrapper);
    }

    // 已经支付了，变更为结束状态
    orderService.forceUpdateOrderState(order, UserState.CLOSED, OrderState.ORDER_CLOSED);

    // 里程后返
    mileageService.received(context);

    // 推送
    pushService.push(order, PushSmsScene.SCENE_014);

    // 推送灵动岛
    pushService.pushIsland(order, null, PushIslandScene.DEFAULT);
}
```

#### 2.2.3 PointPayCarTripFinishedProcessor (支付分支付)
```java
@Override
public void finished(OrderTripFinishedContext context) {
    OrderVO order = context.getOrder();

    // 计算最新的订单乘客应付金额
    CarPriceRet ret = priceCalService.pricingForTripFinished(context, context.getDetails());
    OrderBillWrapper priceWrapper = ret.getBillWrapper();

    // 行程结束更新订单信息
    orderService.updateOrderForTripFinished(order, priceWrapper);

    PaymentPointTypeEnum paymentPointType = PayUtils.getPaymentPointType(order.getPayCategory(), order.getOrderTags());
    List<OrderTag> orderTags = PayUtils.initDeductCarFeeOrderTags(paymentPointType);

    // 调用支付分扣款
    boolean success = payService.deductPayOrderForPayScore(context);
    if (success) {
        // 扣款成功，添加扣款成功标签
        orderService.updateOrderTags(order, orderTags);
        // 更新订单状态为已关闭
        orderService.forceUpdateOrderState(order, UserState.CLOSED, OrderState.ORDER_CLOSED);
        // 里程后返
        mileageService.received(context);
        // 推送
        pushService.push(order, PushSmsScene.SCENE_014);
        // 推送灵动岛
        pushService.pushIsland(order, null, PushIslandScene.DEFAULT);
    } else {
        // 扣款失败处理
        deductFailureProcess(context, paymentPointType);
    }
}
```

## 3. 顺风车处理器 - SfcTripFinishedProcessor

### 3.1 顺风车完单处理逻辑
```java
@Override
public void finished(OrderTripFinishedContext context) {
    try {
        OrderVO order = context.getOrder();

        // 计算最新的订单乘客应付金额
        CarPriceRet ret = priceCalService.pricingForTripFinished(context, context.getDetails());
        OrderBillWrapper priceWrapper = ret.getBillWrapper();

        // 行程结束订单更新
        orderService.updateOrderForTripFinished(order, priceWrapper);

        // 拼单成功处理
        if (priceWrapper.isPooledOrder()) {
            pooledOrderProcess(context, priceWrapper);
        }

        // 页面分销通知订单状态
        distributionService.notifyOrderStateForPageDistribution(order, OrderState.TRIP_FINISHED, false);

        // 已支付行程结束
        if (order.isPaid()) {
            // 零元购订单退款处理
            if (order.isFreeMadaOrder()){
                refundForFree(context);
            } else if (priceWrapper.isNeedRefundDiff()) {
                // 退差价
                refundDiffProcess(context, priceWrapper);
            }

            // 已经支付了，变更为结束状态
            orderService.forceUpdateOrderState(order, UserState.CLOSED, OrderState.ORDER_CLOSED);

            // 页面分销通知订单状态
            distributionService.notifyOrderStateForPageDistribution(order, OrderState.ORDER_CLOSED, false);

            // 里程后返
            mileageService.received(context);

            // 推送
            pushService.push(order, PushSmsScene.SCENE_014);

            // 推送灵动岛
            pushService.pushIsland(order, null, PushIslandScene.DEFAULT);
        }

        // 未支付行程结束
        if (!order.isPaid()) {
            // 延后支付处理
            if (AfterPay.isAfterPay(order)) {
                afterPayProcess(context, priceWrapper);
            } else {
                // 普通未支付订单处理
                unPaidOrderProcess(context, priceWrapper);
            }
        }

        // 活动相关处理
        activityProcess(context);

    } catch (Exception e) {
        LoggerUtils.error(log, "[SfcTripFinishedProcessor][finished] error:{}", e, ThrowableUtil.stackTraceToString(e));
        throw e;
    }
}
```

### 3.2 顺风车特殊处理逻辑

#### 3.2.1 拼单成功处理
```java
if (priceWrapper.isPooledOrder()) {
    pooledOrderProcess(context, priceWrapper);
}
```

#### 3.2.2 零元购订单处理
```java
if (order.isFreeMadaOrder()){
    refundForFree(context);
}
```

#### 3.2.3 延后支付处理
```java
if (AfterPay.isAfterPay(order)) {
    afterPayProcess(context, priceWrapper);
}
```

## 4. 上门接送处理器 - XCarTripFinishedProcessorProxy

### 4.1 处理器选择逻辑
```java
@Override
public void finished(OrderTripFinishedContext context) {
    
    if (context.getOrder().isPaymentPoint()) {
        LoggerUtils.info(log, "[XCarTripFinishedProcessorProxy][finished] 万能车支付分支付行程结束, orderNo:{}", context.getOrderSerialNo());
        xCarPointPayCarTripFinishedProcessor.finished(context);
        return;
    }

    if (context.getOrder().isOnlinePay()) {
        LoggerUtils.info(log, "[XCarTripFinishedProcessorProxy][finished] 万能车在线支付行程结束, orderNo:{}", context.getOrderSerialNo());
        xCarOnlinePayCarTripFinishedProcessor.finished(context);
        return;
    }
}
```

### 4.2 上门接送在线支付处理
```java
@Override
public void finished(OrderTripFinishedContext context) {
    OrderVO order = context.getOrder();

    // 计算最新的订单乘客应付金额
    CarPriceRet ret = priceCalService.pricingForTripFinished(context, context.getDetails());
    OrderBillWrapper priceWrapper = ret.getBillWrapper();

    // 行程结束更新订单信息
    orderService.updateOrderForTripFinished(order, priceWrapper);

    // 行程结束推送财务结算信息
    payService.settlement(context);

    // 需要补差
    if (priceWrapper.isNeedBalancePay()) {
        balancePayProcess(context, priceWrapper);
    }

    // 需要退差价
    if (priceWrapper.isNeedRefundDiff()) {
        refundDiffProcess(context, priceWrapper);
    }

    // 已经支付了，变更为结束状态
    orderService.forceUpdateOrderState(order, UserState.CLOSED, OrderState.ORDER_CLOSED);

    // 里程后返
    mileageService.received(context);

    // 推送
    pushService.push(order, PushSmsScene.SCENE_014);

    // 推送灵动岛
    pushService.pushIsland(order, null, PushIslandScene.DEFAULT);
}
```

## 5. 价格计算服务 - PriceCalService

### 5.1 行程结束价格计算
```java
@Override
public CarPriceRet pricingForTripFinished(Context context, OrderFinishedSupplierBill details) {
    OrderBillWrapper wrapper = orderBillService.bill(context, details);
    return CarPriceRet.success(wrapper);
}
```

### 5.2 OrderBillServiceProxy 账单计算分发
```java
@Override
public OrderBillWrapper bill(Context context, OrderFinishedSupplierBill details) {
    OrderVO order = context.getOrder();
    
    // 顺风车订单
    if (order.getOrderType().isSfc()) {
        // 顺风车已支付特惠供应商完单后的账单
        if(isSfcPaidCheapOrder(order)){
            return sfcAcceptedDeductionCheapOrderBillService.bill(context, details);
        }
        // 延后支付
        if (AfterPay.isAfterPay(order)) {
            return sfcAfterOrderBillService.bill(context, details);
        }
        return sfcOrderBillService.bill(context, details);
    }

    // 上门接送
    if (order.isXCar()) {
        return xCarOrderBillService.bill(context, details);
    }

    // 特殊一口价订单
    if( isSpecialYkjOrder(order) ){
        return specialYkjOrderBillService.bill(context, details);
    }

    // 网约车订单
    return carOrderBillService.bill(context, details);
}
```

## 6. 订单更新服务

### 6.1 行程结束订单更新
```java
@Override
public void updateOrderForTripFinished(OrderVO order, OrderBillWrapper details) {
    
    LoggerUtils.info(log, "[updateOrderForTripFinished] rights={}" + details.getUserBill().getRights());
    
    // 更新订单状态
    self.updateOrderState(order, UserState.TRIP_FINISHED, order.getOrderState(), OrderState.TRIP_FINISHED);

    // 更新用车信息
    goodsCmdService.updateTicketForTripFinished(order, details);
    
    // 行程结束处理风控
    riskService.maliciousAdditionFeeRisk(new RiskContext(order,RiskSceneEnum.MALICIOUS_ADDITION_FEE_SCENE));

    LoggerUtils.info(log, "[updateOrderForTripFinished] rights2={}" + details.getUserBill().getRights());
}
```

### 6.2 订单状态更新
- 用户状态：`UserState.TRIP_FINISHED`
- 订单状态：`OrderState.TRIP_FINISHED`

### 6.3 用车信息更新
```java
goodsCmdService.updateTicketForTripFinished(order, details);
```
- 更新实际行驶里程
- 更新实际行驶时长
- 更新结算价格
- 更新费用明细

## 7. 下一步分析重点

在第4部分中，我们将深入分析：
1. 分布式锁机制的详细实现
2. Redis客户端和缓存策略
3. 消息队列和异步通知机制
4. 监控和日志记录体系

---
*本文档是供应链完单回调处理流程分析的第3部分，主要分析了业务处理服务的实现逻辑。*
