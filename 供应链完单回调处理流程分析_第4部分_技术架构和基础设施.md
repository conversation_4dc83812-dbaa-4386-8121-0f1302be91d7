# 供应链完单回调处理流程分析 - 第4部分：技术架构和基础设施

## 1. 分布式锁机制详细分析

### 1.1 LockTemplateProxy 实现原理
```java
@Slf4j
public class LockTemplateProxy implements LockTemplateClient {

    public static final long DEFAULT_LOCK_TIMEOUT = 1000 * 30;  // 默认30秒超时

    private final RedissonClientHA redissonClientHA;

    @Override
    public <T> T getLockAndResult(String key, long millisecond, Function<Boolean, T> consumer) {
        long now = System.currentTimeMillis();
        boolean success = false;
        RLock rLock = redissonClientHA.redissonClient.getLock(key);
        try {
            success = rLock.tryLock(millisecond, TimeUnit.MILLISECONDS);
            return consumer.apply(success);
        } catch (InterruptedException e) {
            return consumer.apply(false);
        } finally {
            unLock(rLock, now, success, key);
        }
    }

    private static void unLock(RLock rLock, long now, boolean success, String key) {
        try {
            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        } catch (Exception ex) {
            log.error("[LockTemplate][unlock] 释放锁异常 {}", key, ex);
        } finally {
            long duration = System.currentTimeMillis() - now;
            if (success) {
                log.info("[LockTemplate][unLock] 获取锁成功 {} 业务耗时{}毫秒", key, duration);
            } else {
                log.error("[LockTemplate][unLock] 获取锁失败 {} 业务耗时{}毫秒", key, duration);
            }
        }
    }
}
```

### 1.2 Redis锁键构建策略
```java
public class RedisKeyBuilder {
    
    /** 订单流程处理中锁 */
    private static final String ORDER_PROCESSING_LOCK = "order:processing:lock:{}";
    
    /** 行程结束锁 */
    private static final String ORDER_FINISH_CALLBACK_LOCK = "order:finish:callback:Lock:{}";
    
    /**
     * 订单进行中锁
     * @param orderSerialNo
     * @return
     */
    public static String orderProcessingLock(String orderSerialNo) {
        return MessageFormatter.format(ORDER_PROCESSING_LOCK, orderSerialNo).getMessage();
    }
    
    /**
     * 行程结束回调锁
     * @param orderSerialNo
     * @return
     */
    public static String orderFinishCallbackLock(String orderSerialNo) {
        return MessageFormatter.format(ORDER_FINISH_CALLBACK_LOCK, orderSerialNo).getMessage();
    }
}
```

### 1.3 双重锁机制设计原理

#### 第一层锁：订单处理锁 (Redisson分布式锁)
- **锁键格式**：`order:processing:lock:{orderSerialNo}`
- **锁类型**：Redisson RLock (可重入锁)
- **等待时间**：3000毫秒
- **作用范围**：防止同一订单在不同业务流程间并发处理
- **使用场景**：支付回调、取消订单、行程结束等所有订单操作

#### 第二层锁：完单回调专用锁 (Redis SETNX)
- **锁键格式**：`order:finish:callback:Lock:{orderSerialNo}`
- **锁类型**：Redis SETNX (原子操作)
- **过期时间**：60秒
- **作用范围**：防止同一完单回调的重复请求
- **使用场景**：仅限于完单回调处理

#### 双重锁的必要性分析
1. **第一层锁**：保证订单级别的操作互斥，防止支付、取消、完单等操作并发执行
2. **第二层锁**：保证完单回调的幂等性，防止供应商重复发送完单通知
3. **锁粒度不同**：第一层锁粒度较粗，第二层锁针对特定场景优化

### 1.4 锁超时和异常处理
```java
// Redisson锁异常处理
try {
    success = rLock.tryLock(millisecond, TimeUnit.MILLISECONDS);
    return consumer.apply(success);
} catch (InterruptedException e) {
    return consumer.apply(false);  // 中断异常返回失败
}

// Redis SETNX锁异常处理
try {
    holdLock = redisClientProxy.setnx(redisLock, orderSerialNo, 60);
    if (!holdLock) {
        return buildFailureResponse(request, ERROR.unsupportedOperation("行程结束并发异常"));
    }
    // 业务处理...
} finally {
    if (holdLock) {
        redisClientProxy.remove(redisLock);  // 确保锁释放
    }
}
```

## 2. Redis客户端和缓存策略

### 2.1 RedisClientProxy 实现
```java
public class RedisClientProxy {
    
    /**
     * 设置键值对，如果键不存在
     * @param key 键
     * @param value 值
     * @param expireSeconds 过期时间(秒)
     * @return 是否设置成功
     */
    public boolean setnx(String key, String value, int expireSeconds) {
        // Redis SETNX + EXPIRE 原子操作实现
    }
    
    /**
     * 删除键
     * @param key 键
     */
    public void remove(String key) {
        // Redis DEL 操作实现
    }
}
```

### 2.2 缓存键命名规范
```java
// 锁相关
"order:processing:lock:{orderSerialNo}"          // 订单处理锁
"order:finish:callback:Lock:{orderSerialNo}"    // 完单回调锁
"cancel:lock:{orderSerialNo}"                    // 取消锁
"refund:appeal:lock:{orderSerialNo}"            // 申诉退款锁

// 业务状态相关
"order:processing:{orderSerialNo}"               // 订单处理中标记
"regulator:pay:lock:{orderSerialNo}"            // 二清锁
```

### 2.3 缓存过期策略
- **短期锁**：60秒 (完单回调锁)
- **中期锁**：6秒 (支付锁)
- **长期锁**：30秒 (默认分布式锁超时)
- **标记缓存**：30天 (业务状态标记)

## 3. 数据库访问层 (DAO) 架构

### 3.1 订单查询DAO调用链
```java
// 主要DAO接口
OrderInfoDAO.queryByOrderSerialNo()              // 查询订单基础信息
PassengerInfoDAO.queryByOrderSerialNo()          // 查询乘客信息
SegmentInfoDAO.queryByOrderSerialNo()            // 查询行程段信息
ItemInfoDAO.queryByOrderSerialNo()               // 查询商品信息
OpsiRelationDAO.queryByOrderSerialNo()           // 查询关联关系
PayInfoDAO.queryByOrderSerialNo()                // 查询支付信息
PreResourceInfoDAO.queryByOrderSerialNo()        // 查询资源信息
RefundInfoDAO.queryByOrderSerialNo()             // 查询退款信息
UserPendingPayInfoDAO.queryByOrderSerialNo()     // 查询待支付信息
ExtraBillInfoDAO.queryByOrderSerialNo()          // 查询额外账单信息
```

### 3.2 数据库操作特点
- **多表关联查询**：一次订单查询涉及10+张表
- **环境隔离**：通过`CfgUtils.getEnv()`区分不同环境
- **异常处理**：统一的`OrderQueryException`异常处理
- **对象映射**：通过`OrderInfoMapper.buildOrder()`构建完整订单对象

### 3.3 订单更新操作
```java
// 订单状态更新
orderService.updateOrderState(order, userState, fromState, toState);

// 订单标签更新
orderService.updateOrderTags(order, newTags);

// 用车信息更新
goodsCmdService.updateTicketForTripFinished(order, details);

// 订单金额更新
orderService.updateOrderAmount(order, historyScene);
```

## 4. 消息队列和异步通知机制

### 4.1 TradeProducer 消息生产者
```java
@Resource
private TradeProducer<OrderPriceTraceLogSource> orderPriceTraceLogProducer;

// 发送订单价格追踪日志
if (order.isSendTracePriceLog()) {
    orderPriceTraceLogProducer.sendMessage(OrderPriceTraceLogSource.buildFinishedLog(order));
}
```

### 4.2 异步通知服务
```java
// 用户通知服务
tracerService.notify(order, UserSceneEnum.TRIP_END_NOTICE);

// 推送服务
pushService.push(order, PushSmsScene.SCENE_014);
pushService.pushIsland(order, null, PushIslandScene.DEFAULT);

// 供应链同步
supplierService.syncPaid(order, PaySceneTypeEnum.ORDER);

// 分销通知
distributionService.notifyOrderStateForApiDistribution(order, OrderState.TRIP_FINISHED, false);
distributionService.notifyOrderStateForPageDistribution(order, OrderState.ORDER_CLOSED, false);
```

### 4.3 消息类型分类
- **价格追踪日志**：`OrderPriceTraceLogSource`
- **用户通知**：`UserSceneEnum.TRIP_END_NOTICE`
- **短信推送**：`PushSmsScene.SCENE_014`
- **灵动岛推送**：`PushIslandScene.DEFAULT`
- **供应链同步**：`PaySceneTypeEnum.ORDER`

## 5. 监控和日志记录体系

### 5.1 DataMonitorUtils 数据监控
```java
// 供应链状态不匹配监控
DataMonitorUtils.dataMonitorLog(order, "行程结束", DataMonitorUtils.DataMonitorEnum.SUPPLIER_STATE_MISMATCH);

// 供应链完单金额差异监控
DataMonitorUtils.dataMonitorLog(order, JacksonUtils.toJSONString(details), DataMonitorUtils.DataMonitorEnum.SUPPLIER_FINISH_AMOUNT_DIFF);
```

### 5.2 日志记录策略
```java
// 链路追踪初始化
LogContextUtils.initTracer(Services.ORDER_FINISH_CALLBACK, param.getTraceId(), param.getOrderSerialNo());

// 关键节点日志
LoggerUtils.warn(log, "[{}] 并发请求，lock={}", Services.ORDER_FINISH_CALLBACK, redisLock);
LoggerUtils.error(log, "[{}] error msg:{}", e, Services.ORDER_FINISH_CALLBACK, e.getMessage());
LoggerUtils.info(log, "[updateOrderForTripFinished] rights={}", details.getUserBill().getRights());
```

### 5.3 监控指标类型
- **业务监控**：订单状态不匹配、金额差异超阈值
- **性能监控**：锁获取耗时、业务处理耗时
- **异常监控**：并发冲突、系统异常、业务异常
- **链路监控**：traceId全链路追踪

### 5.4 金额差异监控机制
```java
private static void amountDiffMonitor(OrderVO order, OrderFinishedSupplierBill details) {
    try {
        String config = StringUtils.defaultIfEmpty(ConfigCenterClient.get("supplier_finish_amount_diff_percent"), "10");
        if (MoneyUtils.isDifferenceExceedsPercentage(order.getAmount(), details.getTotalAmount(), new BigDecimal(config))) {
            log.warn("订单金额和供应商账单金额差异超过{}%," +
                            "订单号:{}," +
                            "订单金额:{}," +
                            "供应商账单金额:{}," +
                            "供应商账单明细:{}",
                    config, order.getOrderSerialNo(), order.getAmount(), 
                    details.getTotalAmount(), JacksonUtils.toJSONString(details));
        }
    } catch (Exception e) {
        log.error("[OrderTripFinishedContext][of] error:{}", e.getMessage());
    }
}
```

## 6. 配置中心和环境管理

### 6.1 ConfigCenterClient 配置管理
```java
// 动态配置获取
String config = ConfigCenterClient.get("supplier_finish_amount_diff_percent");
String defaultValue = StringUtils.defaultIfEmpty(config, "10");
```

### 6.2 环境隔离机制
```java
// 环境获取
String env = CfgUtils.getEnv();

// 数据库查询时的环境隔离
OrderInfoDO order = orderInfoDAO.queryByOrderSerialNo(orderSerialNo, env);
```

### 6.3 可配置参数
- `supplier_finish_amount_diff_percent`：供应商完单金额差异阈值百分比
- 各种超时时间配置
- 开关配置（如是否发送价格追踪日志）

## 7. 异常处理和容错机制

### 7.1 异常分类处理
```java
// 业务异常
if (e instanceof LYException) {
    LYError error = ((LYException) e).getError();
    return OrderFinishCallbackResponseDTO.create(false, traceId, error.getCode(), error.getMessage());
}

// 系统异常
LYError error = ERROR.sysErr(e.getMessage());
return OrderFinishCallbackResponseDTO.create(false, traceId, error.getCode(), error.getMessage());
```

### 7.2 容错策略
- **锁获取失败**：返回并发异常，不影响系统稳定性
- **订单状态异常**：记录监控日志，直接返回成功
- **数据库异常**：统一异常处理，返回系统错误
- **外部服务异常**：降级处理，保证核心流程完成

### 7.3 幂等性保证
- **双重锁机制**：防止重复处理
- **订单状态检查**：已完成状态直接返回
- **业务逻辑幂等**：重复调用不会产生副作用

## 8. 性能优化策略

### 8.1 数据库查询优化
- **批量查询**：一次性查询所有关联表数据
- **索引优化**：订单号作为主要查询条件
- **连接池管理**：合理配置数据库连接池

### 8.2 缓存策略
- **分布式锁缓存**：避免重复获取锁
- **订单状态缓存**：减少数据库查询
- **配置缓存**：配置中心数据本地缓存

### 8.3 异步处理
- **消息队列**：非核心业务异步处理
- **推送通知**：用户通知异步发送
- **日志记录**：监控日志异步写入

## 9. 下一步分析重点

在第5部分中，我们将深入分析：
1. 完整的流程图和时序图
2. 关键业务场景的处理细节
3. 异常场景和边界条件处理
4. 性能指标和监控告警
5. 开发和运维最佳实践

---
*本文档是供应链完单回调处理流程分析的第4部分，主要分析了技术架构和基础设施的实现细节。*
