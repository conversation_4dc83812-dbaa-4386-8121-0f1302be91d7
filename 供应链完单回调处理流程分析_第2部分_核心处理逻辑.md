# 供应链完单回调处理流程分析 - 第2部分：核心处理逻辑

## 1. SupplierOrderFinishCallbackInvoker 核心实现分析

### 1.1 invoke方法完整实现
```java
@Override
public OrderFinishCallbackResponseDTO invoke(GatewayContext<OrderFinishCallbackRequestDTO, OrderFinishCallbackResponseDTO> context) throws ProxyException {

    return lockTemplateProxy.getLockAndResult(RedisKeyBuilder.orderProcessingLock(context.getRequest().getOrderSerialNo()), 3000, (lock) -> {
        if (lock) {
            // 是否持有锁, 此holdLock锁用于防止重复请求
            boolean holdLock = false;
            String redisLock = RedisKeyBuilder.orderFinishCallbackLock(context.getRequest().getOrderSerialNo());
            try {
                holdLock = redisClientProxy.setnx(redisLock, context.getRequest().getOrderSerialNo(), 60);
                if (!holdLock) {
                    LoggerUtils.warn(log, "[{}] 并发请求，lock={}", Services.ORDER_FINISH_CALLBACK, redisLock);
                    return buildFailureResponse(context.getRequest(), ERROR.unsupportedOperation("行程结束并发异常"));
                }
                // 查询订单
                OrderVO order = orderService.queryOrder(context.getRequest().getOrderSerialNo());

                // 行程结束
                OrderFinishedSupplierBill details = supplierTripFinishedBuilder.build(context.getRequest());
                orderTripFinishedService.finished(OrderTripFinishedContext.of(order, details));

                // 构建响应
                return buildSuccessResponse(context.getRequest());
            } catch (Exception e) {
                LoggerUtils.error(log, "[{}] error msg:{}", e, Services.ORDER_FINISH_CALLBACK, e.getMessage());
                return buildFailureResponse(context.getRequest(), e);
            } finally {
                if (holdLock) {
                    redisClientProxy.remove(redisLock);
                }
            }
        } else {
            LoggerUtils.warn(log, "[SupplierOrderFinishCallbackInvoker][lock] 订单{},频繁请求 or 其他流程在处理中", context.getRequest().getOrderSerialNo());
            return buildFailureResponse(context.getRequest(), ERROR.unsupportedOperation("行程结束并发异常"));
        }
    });
}
```

### 1.2 处理步骤详细分析

#### 步骤1：双重锁机制
系统采用双重锁机制确保并发安全：

**第一层锁：订单处理锁**
```java
// Redis Key: "order:processing:lock:{orderSerialNo}"
lockTemplateProxy.getLockAndResult(RedisKeyBuilder.orderProcessingLock(orderSerialNo), 3000, ...)
```
- 使用Redisson分布式锁
- 等待时间：3000毫秒
- 作用：防止订单在不同业务流程间并发处理

**第二层锁：完单回调专用锁**
```java
// Redis Key: "order:finish:callback:Lock:{orderSerialNo}"
redisClientProxy.setnx(RedisKeyBuilder.orderFinishCallbackLock(orderSerialNo), orderSerialNo, 60)
```
- 使用Redis SETNX命令
- 过期时间：60秒
- 作用：防止同一完单回调的重复请求

#### 步骤2：订单查询
```java
OrderVO order = orderService.queryOrder(context.getRequest().getOrderSerialNo());
```

**查询链路**：
```
OrderService.queryOrder()
    ↓
OrderQueryServiceImpl.queryOrder()
    ↓
OrderInfoDAO.queryByOrderSerialNo() (查询正式订单)
    ↓ (如果未找到)
PreOrderInfoDAO.queryByOrderSerialNo() (查询预约订单)
    ↓
OrderInfoMapper.buildOrder() (构建完整订单对象)
```

**订单构建过程**：
```java
// 查询订单基础信息
OrderInfoDO order = orderInfoDAO.queryByOrderSerialNo(orderSerialNo, CfgUtils.getEnv());

// 查询关联信息
List<PassengerInfoDO> passengerInfo = passengerInfoDAO.queryByOrderSerialNo(orderSerialNo, CfgUtils.getEnv());
List<SegmentInfoDO> segmentInfo = segmentInfoDAO.queryByOrderSerialNo(orderSerialNo, CfgUtils.getEnv());
List<ItemInfoDO> itemDetails = itemInfoDAO.queryByOrderSerialNo(orderSerialNo, CfgUtils.getEnv());
List<OpsiRelationDO> opsiRelations = opsiRelationDAO.queryByOrderSerialNo(orderSerialNo, CfgUtils.getEnv());
List<PayInfoDO> payInfos = payInfoDAO.queryByOrderSerialNo(orderSerialNo, CfgUtils.getEnv());
List<PreResourceInfoDO> resourceInfos = preResourceInfoDAO.queryByOrderSerialNo(orderSerialNo, CfgUtils.getEnv());
List<RefundInfoDO> refundInfos = refundInfoDAO.queryByOrderSerialNo(orderSerialNo, CfgUtils.getEnv());
List<UserPendingPayInfoDO> userPendingPayInfos = userPendingPayInfoDAO.queryByOrderSerialNo(orderSerialNo);
List<ExtraBillInfoDO> extBillInfoDOS = extBillInfoDAO.queryByOrderSerialNo(orderSerialNo, CfgUtils.getEnv());

// 构建完整订单对象
return orderInfoMapper.buildOrder(order, passengerInfo, segmentInfo, itemDetails, opsiRelations, payInfos, resourceInfos, refundInfos, userPendingPayInfos, extBillInfoDOS);
```

#### 步骤3：数据转换 - SupplierTripFinishedBuilder
```java
OrderFinishedSupplierBill details = supplierTripFinishedBuilder.build(context.getRequest());
```

**SupplierTripFinishedBuilder.build()实现**：
```java
@Override
public OrderFinishedSupplierBill build(OrderFinishCallbackRequestDTO requestDTO) throws BuilderException {
    OrderFinishedSupplierBill details = new OrderFinishedSupplierBill();
    details.setOrderSerialNo(requestDTO.getOrderSerialNo());
    details.setSupplierCode(requestDTO.getSupplierCode());
    details.setSupplierOrderNo(requestDTO.getSupplierOrderNo());
    details.setExKilos(requestDTO.getExKilos());
    details.setExMinutes(requestDTO.getExMinutes());
    details.setTotalAmount(new Money(requestDTO.getTotalAmount()));
    details.setPoolStatus(requestDTO.getPoolStatus());
    details.setDetails(buildDetails(requestDTO));
    details.setHitRiskType(requestDTO.getHitRiskType());
    return details;
}
```

**账单明细转换逻辑**：
```java
private List<OrderFinishedSupplierBill.BillDetail> buildDetails(OrderFinishCallbackRequestDTO requestDTO) {
    List<OrderFinishedSupplierBill.BillDetail> details = Lists.newArrayList();
    for (OrderFinishCallbackRequestDTO.BillDetailDTO detail : requestDTO.getDetails()) {
        OrderFinishedSupplierBill.BillDetail billDetail = new OrderFinishedSupplierBill.BillDetail();
        SupplierCarFeeType carFeeType = SupplierCarFeeType.getByCode(detail.getType());
        
        // 完单时供应链返回了未知费用项的警告日志
        if (carFeeType == SupplierCarFeeType.OTHER_FEE) {
            log.warn("供应链返回费用类型SupplierCarFeeType转换异常: code={}, feeType={}", detail.getType(), carFeeType);
        }
        
        billDetail.setType(SupplierCarFeeType.getByCode(detail.getType()));
        billDetail.setDesc(detail.getDesc());
        billDetail.setOriginalType(detail.getType());
        
        // 产品要求折扣费改成负数
        if (carFeeType.getCarFeeType() == CarFeeType.DISCOUNT_FEE && NumberUtils.isCreatable(detail.getAmount())) {
            BigDecimal amount = new BigDecimal(detail.getAmount());
            billDetail.setAmount(amount.abs().negate().toString());
        } else {
            billDetail.setAmount(detail.getAmount());
        }
        details.add(billDetail);
    }
    return details;
}
```

**关键转换逻辑**：
1. **费用类型映射**：将供应商返回的字符串类型转换为系统内部的`SupplierCarFeeType`枚举
2. **折扣费处理**：折扣费用强制转换为负数（产品需求）
3. **金额处理**：字符串金额转换为`Money`对象
4. **原始类型保留**：保留供应商原始的费用类型字符串

#### 步骤4：构建上下文对象
```java
orderTripFinishedService.finished(OrderTripFinishedContext.of(order, details));
```

**OrderTripFinishedContext.of()实现**：
```java
public static OrderTripFinishedContext of(OrderVO order, OrderFinishedSupplierBill details) {
    // 交易与供应链完单金额diff日志监控
    amountDiffMonitor(order, details);
    
    return new OrderTripFinishedContext(order, details);
}

private static void amountDiffMonitor(OrderVO order, OrderFinishedSupplierBill details) {
    DataMonitorUtils.dataMonitorLog(order,
            JacksonUtils.toJSONString(details),
            DataMonitorUtils.DataMonitorEnum.SUPPLIER_FINISH_AMOUNT_DIFF);
    try {
        String config = StringUtils.defaultIfEmpty(ConfigCenterClient.get("supplier_finish_amount_diff_percent"), "10");
        if (MoneyUtils.isDifferenceExceedsPercentage(order.getAmount(), details.getTotalAmount(), new BigDecimal(config))) {
            log.warn("订单金额和供应商账单金额差异超过{}%," +
                            "订单号:{}," +
                            "订单金额:{}," +
                            "供应商账单金额:{}," +
                            "供应商账单明细:{}",
                    config,
                    order.getOrderSerialNo(),
                    order.getAmount(),
                    details.getTotalAmount(),
                    JacksonUtils.toJSONString(details));
        }
    } catch (Exception e) {
        log.error("[OrderTripFinishedContext][of] error:{}", e.getMessage());
    }
}
```

**监控功能**：
- 记录完单数据监控日志
- 检查订单金额与供应商账单金额差异
- 默认差异阈值：10%（可配置）
- 超过阈值时记录警告日志

## 2. 响应构建机制

### 2.1 成功响应构建
```java
private OrderFinishCallbackResponseDTO buildSuccessResponse(OrderFinishCallbackRequestDTO request) {
    return OrderFinishCallbackResponseDTO.create(true, request.getTraceId(), "", "");
}
```

### 2.2 失败响应构建
```java
private OrderFinishCallbackResponseDTO buildFailureResponse(OrderFinishCallbackRequestDTO request, Exception e) {
    LYError error = (e instanceof LYException) ? ((LYException) e).getError() : ERROR.sysErr(e.getMessage());
    return OrderFinishCallbackResponseDTO.create(false, request.getTraceId(), error.getCode(), error.getMessage());
}

private OrderFinishCallbackResponseDTO buildFailureResponse(OrderFinishCallbackRequestDTO request, LYError error) {
    return OrderFinishCallbackResponseDTO.create(false, request.getTraceId(), error.getCode(), error.getMessage());
}
```

## 3. 异常处理机制

### 3.1 异常类型分类
1. **业务异常**：`LYException` - 返回具体的错误码和错误信息
2. **系统异常**：其他Exception - 统一包装为系统错误
3. **并发异常**：锁获取失败 - 返回"行程结束并发异常"

### 3.2 锁释放保障
```java
finally {
    if (holdLock) {
        redisClientProxy.remove(redisLock);
    }
}
```
- 无论处理成功还是失败，都会在finally块中释放锁
- 只有成功获取锁的情况下才会释放，避免误删其他线程的锁

## 4. 日志记录策略

### 4.1 关键节点日志
```java
// 并发请求警告
LoggerUtils.warn(log, "[{}] 并发请求，lock={}", Services.ORDER_FINISH_CALLBACK, redisLock);

// 处理异常错误
LoggerUtils.error(log, "[{}] error msg:{}", e, Services.ORDER_FINISH_CALLBACK, e.getMessage());

// 锁获取失败警告
LoggerUtils.warn(log, "[SupplierOrderFinishCallbackInvoker][lock] 订单{},频繁请求 or 其他流程在处理中", orderSerialNo);
```

### 4.2 链路追踪
```java
// 在CallbackFacadeImpl中初始化追踪
LogContextUtils.initTracer(Services.ORDER_FINISH_CALLBACK, param.getTraceId(), param.getOrderSerialNo());
```

## 5. 核心依赖组件

### 5.1 主要依赖注入
```java
@Resource
private OrderTripFinishedService orderTripFinishedService;  // 行程结束业务服务

@Resource(name = "supplierTripFinishedBuilder")
private SupplierTripFinishedBuilder supplierTripFinishedBuilder;  // 数据转换构建器

@Resource(name = "orderService")
private OrderService orderService;  // 订单服务

@Resource
private RedisClientProxy redisClientProxy;  // Redis客户端

@Resource
private LockTemplateProxy lockTemplateProxy;  // 分布式锁模板
```

### 5.2 错误工厂
```java
private final static BizErrorFactory ERROR = BizErrorFactory.getInstance();
```

## 6. 下一步分析重点

在第3部分中，我们将深入分析：
1. OrderTripFinishedService的业务处理逻辑
2. 不同订单类型的处理器选择机制
3. 价格计算和账单处理流程
4. 订单状态更新和后续通知机制

---
*本文档是供应链完单回调处理流程分析的第2部分，主要分析了核心处理逻辑的实现细节。*
