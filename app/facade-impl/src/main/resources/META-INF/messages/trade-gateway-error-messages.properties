#第1-2位是同程旅游网错误码标识，固定位：LY（LY Error）；
#第3位是规范版本为，目前为0；
#第4位是错误级别，1-INFO, 3-WARN, 5-ERROR, 7-FATAL
#第5位是错误类别，0-系统错误, 1-业务错误, 2-第三方错误
#第6-9位是错误场景，全局分配
#第10-12位是业务场景下的具体编码
#！！！！！！！！！！！！！！！！！！重要重要重要！！！！！！！！！！！！！！！！！！！！！
#所有对C端错误码，不是对C端接口的错误码请不要写到这里
#通用异常
#基本参数验证异常统一从LY051000开头，标识ERROR级别，业务错误
LY0510000001=系统异常, detail:{0}
LY0510000002=参数名:{0}, 内容:{1}不合法, 正确的格式:{2}
LY0510000003={0}参数不能为空
LY0510000004={0}参数格式错误，正确格式应该为{1}
LY0510000005={0}参数，值:{1}不合法
