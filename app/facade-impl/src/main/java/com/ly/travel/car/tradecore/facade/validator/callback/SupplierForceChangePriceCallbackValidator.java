package com.ly.travel.car.tradecore.facade.validator.callback;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.facade.request.callback.OrderForceChangePriceRequestDTO;
import com.ly.travel.car.tradecore.facade.validator.BaseValidator;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单强制改价
 *
 * <AUTHOR>
 * @version Id: SupplierForceChangePriceCallbackInvoker  2025/5/9
 */
@Slf4j
@Service
@Proxy(Services.ORDER_FORCE_CHANGE_PRICE_CALLBACK)
public class SupplierForceChangePriceCallbackValidator extends BaseValidator implements Validator<OrderForceChangePriceRequestDTO> {

    @Override
    public void validate(OrderForceChangePriceRequestDTO request) throws ValidationException {
        // 请求参数不能为空
        dtoNotNullValidate(request, "request");
        request.setTraceId(StringUtils.defaultIfBlank(request.getTraceId(), UUID.randomUUID().toString()));
        // traceId 不能为空
        dtoNotStringEmptyValidate(request.getTraceId(), "traceId");
        // orderSerialNo 不能为空
        dtoNotStringEmptyValidate(request.getOrderSerialNo(), "orderSerialNo");
        dtoNotStringEmptyValidate(request.getSupplierCode(), "supplierCode");
    }
}
