package com.ly.travel.car.tradecore.facade.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ly.sof.gateway.AbstractFacade;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.facade.TradeFacade;
import com.ly.travel.car.tradecore.facade.request.trade.*;
import com.ly.travel.car.tradecore.facade.response.trade.*;

/**
 * 交易门面实现
 *
 * <AUTHOR>
 * @version Id: TradeFacadeImpl, v 0.1 2024/2/27 10:55 icanci Exp $
 */
@Service("tradeFacade")
public class TradeFacadeImpl extends AbstractFacade implements TradeFacade {
    /**
     * 下单接口
     *
     * @param param 下单请求参数
     * @return 下单返回结果
     */
    @Override
    public BookResponseDTO book(BookRequestDTO param) {
        LogContextUtils.initTracer(Services.BOOK, param.getTraceId(), StringUtils.EMPTY);
        return execute(param, Services.BOOK, BookResponseDTO.class);
    }

    @Override
    public BookResponseDTO preBook(PreBookRequestDTO param) {
        LogContextUtils.initTracer(Services.PRE_BOOK, param.getTraceId(), StringUtils.EMPTY);
        return execute(param, Services.PRE_BOOK, BookResponseDTO.class);
    }

    /**
     * 万能车下单接口
     * @param param 下单请求参数
     * @return 下单返回结果
     */
    @Override
    public BookResponseDTO xCarBook(XCarBookRequestDTO param) {
        LogContextUtils.initTracer(Services.XCAR_BOOK, param.getTraceId(), StringUtils.EMPTY);
        return execute(param, Services.XCAR_BOOK, BookResponseDTO.class);
    }

    /**
     * 违约金查询
     *
     * @param param 违约金查询请求参数
     * @return 违约金查询返回结果
     */
    @Override
    public PenaltyInquiryResponseDTO penaltyInquiry(PenaltyInquiryRequestDTO param) {
        LogContextUtils.initTracer(Services.PENALTY_INQUIRY, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.PENALTY_INQUIRY, PenaltyInquiryResponseDTO.class);
    }

    /**
     * 用户结束行程
     *
     * @param param 用户结束行程请求参数
     * @return penalty inquiry response dto
     */
    @Override
    public UserFinishTripResponseDTO finishTrip(UserFinishTripRequestDTO param) {
        LogContextUtils.initTracer(Services.FINISH_TRIP, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.FINISH_TRIP, UserFinishTripResponseDTO.class);
    }

    /**
     * 用户确认上车
     *
     * @param param 用户确认上车请求参数
     * @return 用户确认上车返回结果
     */
    @Override
    public UserConfirmGetOnResponseDTO confirmGetOn(UserConfirmGetOnRequestDTO param) {
        LogContextUtils.initTracer(Services.CONFIRM_GET_ON, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.CONFIRM_GET_ON, UserConfirmGetOnResponseDTO.class);
    }

    /**
     * 用户确认下车
     *
     * @param param 用户确认下车请求参数
     * @return 用户确认下车返回结果
     */
    @Override
    public UserConfirmGetOffResponseDTO confirmGetOff(UserConfirmGetOffRequestDTO param) {
        LogContextUtils.initTracer(Services.CONFIRM_GET_OFF, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.CONFIRM_GET_OFF, UserConfirmGetOffResponseDTO.class);
    }

    /**
     * 修改出发时间接口-顺风车
     *
     * @param param 修改出发时间请求参数
     * @return 修改出发时间返回结果
     */
    @Override
    public ModifyDepartureTimeResponseDTO modifyDepartureTime(ModifyDepartureTimeRequestDTO param) {
        LogContextUtils.initTracer(Services.MODIFY_DEPARTURE_TIME, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.MODIFY_DEPARTURE_TIME, ModifyDepartureTimeResponseDTO.class);
    }

    /**
     * 修改备注-顺风车
     *
     * @param param 修改出发时间请求参数
     * @return 修改出发时间返回结果
     */
    @Override
    public ModifyRemarkResponseDTO modifyRemark(ModifyRemarkRequestDTO param) {
        LogContextUtils.initTracer(Services.MODIFY_REMARK, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.MODIFY_REMARK, ModifyRemarkResponseDTO.class);
    }

    /**
     * 校验是否支持修改手机号
     * @param param
     * @return
     */
    @Override
    public CheckModifyPhoneResponseDTO checkModifyPhone(CheckModifyPhoneRequestDTO param) {
        LogContextUtils.initTracer(Services.CHECK_MODIFY_PHONE, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.CHECK_MODIFY_PHONE, CheckModifyPhoneResponseDTO.class);
    }

    /**
     * 取消接口
     *
     * @param param 取消请求参数
     * @return 取消返回结果
     */
    @Override
    public CancelResponseDTO cancel(CancelRequestDTO param) {
        LogContextUtils.initTracer(Services.USER_CANCEL, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.USER_CANCEL, CancelResponseDTO.class);
    }

    /**
     * 删除接口
     *
     * @param param 删除请求参数
     * @return 删除返回结果
     */
    @Override
    public DeleteResponseDTO delete(DeleteRequestDTO param) {
        LogContextUtils.initTracer(Services.USER_DELETE, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.USER_DELETE, DeleteResponseDTO.class);
    }

    /**
     * 修改手机号接口
     *
     * @param param 修改手机号请求参数
     * @return 修改手机号返回结果
     */
    @Override
    public ModifyLinkPhoneResponseDTO modifyLinkPhone(ModifyLinkPhoneRequestDTO param) {
        LogContextUtils.initTracer(Services.MODIFY_LINK_PHONE, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.MODIFY_LINK_PHONE, ModifyLinkPhoneResponseDTO.class);
    }

    /**
     * 网约车、顺风车改派接口
     * 
     * @param param 网约车、顺风车改派请求参数
     * @return 网约车、顺风车改派返回结果
     */
    @Override
    public ReassignResponseDTO reassign(ReassignRequestDTO param) {
        LogContextUtils.initTracer(Services.REASSIGN, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.REASSIGN, ReassignResponseDTO.class);
    }

    /**
     * 判责接口-取消查询-判定责任
     */
    @Override
    public JudgmentResponseDTO judgment(JudgmentRequestDTO param) {
        LogContextUtils.initTracer(Services.JUDGMENT, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.JUDGMENT, JudgmentResponseDTO.class);
    }

    @Override
    public AppealWorkOrderResponseDTO appealWorkOrder(AppealWorkOrderRequestDTO param) {
        LogContextUtils.initTracer(Services.APPEAL_WORK_ORDER, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.APPEAL_WORK_ORDER, AppealWorkOrderResponseDTO.class);
    }

    /**
     * 行后建联
     *
     * @param param
     */
    @Override
    public ContactAfterTripResponseDTO contactAfterTrip(ContactAfterTripRequestDTO param) {
        LogContextUtils.initTracer(Services.CONTACT_AFTER_TRIP, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.CONTACT_AFTER_TRIP, ContactAfterTripResponseDTO.class);
    }

    /**
     * 行后建联查询
     *
     * @param param
     */
    @Override
    public ContactAfterTripQueryResponseDTO contactAfterTripQuery(ContactAfterTripQueryRequestDTO param) {
        LogContextUtils.initTracer(Services.CONTACT_AFTER_TRIP_QUERY, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.CONTACT_AFTER_TRIP_QUERY, ContactAfterTripQueryResponseDTO.class);
    }

    /**
     * 修改行程接口 （暂时分销首汽使用）
     * @param param
     * @return
     */
    @Override
    public ModifyItineraryResponseDTO modifyItinerary(ModifyItineraryRequestDTO param) {
        LogContextUtils.initTracer(Services.MODIFY_ITINERARY, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.MODIFY_ITINERARY, ModifyItineraryResponseDTO.class);
    }

    /**
     * C端修改行程查询
     *
     * @param param
     * @return 修改行程
     */
    @Override
    public ChangeItineraryQueryResponseDTO changeItineraryQuery(ChangeItineraryQueryRequestDTO param) {
        LogContextUtils.initTracer(Services.CHANGE_ITINERARY_QUERY, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.CHANGE_ITINERARY_QUERY, ChangeItineraryQueryResponseDTO.class);
    }

    /**
     * C端修改行程
     *
     * @param param
     * @return 修改行程
     */
    @Override
    public ChangeItineraryResponseDTO changeItinerary(ChangeItineraryBaseRequestDTO param) {
        LogContextUtils.initTracer(Services.CHANGE_ITINERARY, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.CHANGE_ITINERARY, ChangeItineraryResponseDTO.class);
    }

    /**
     * C端修改行程
     *
     * @param param
     * @return 修改行程
     */
    @Override
    public FixDataResponseDTO fixData(FixDataRequestDTO param) {
        LogContextUtils.initTracer(Services.FIX_DATA, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.FIX_DATA, FixDataResponseDTO.class);
    }

    @Override
    public PreOrderDispatchImmediatelyResponseDTO preOrderDispatchImmediately(PreOrderDispatchImmediatelyRequestDTO param) {
        LogContextUtils.initTracer(Services.PRE_ORDER_DISPATCH_IMMEDIATELY, param.getTraceId(), param.getOrderSerialNo());
        return execute(param, Services.PRE_ORDER_DISPATCH_IMMEDIATELY, PreOrderDispatchImmediatelyResponseDTO.class);
    }
}