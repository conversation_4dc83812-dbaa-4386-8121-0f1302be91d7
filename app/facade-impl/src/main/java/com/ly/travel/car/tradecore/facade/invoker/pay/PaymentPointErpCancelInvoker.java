package com.ly.travel.car.tradecore.facade.invoker.pay;

import com.google.common.collect.Lists;
import com.ly.mom.netty.util.internal.ThrowableUtil;
import com.ly.sof.api.error.LYError;
import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.business.pay.ErpPaymentPointService;
import com.ly.travel.car.tradecore.business.pay.model.PaymentPointErpCancelContext;
import com.ly.travel.car.tradecore.error.OrderQueryException;
import com.ly.travel.car.tradecore.facade.request.pay.PayPointErpCancelRequestDTO;
import com.ly.travel.car.tradecore.facade.response.pay.PayPointErpCancelResponseDTO;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.OrderTag;
import com.ly.travel.car.tradecore.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * PaymentPointCreateInvoker
 *
 * <AUTHOR>
 * @version Id: PaymentPointCreateInvoker.java, v 0.1 2024-02-27 18:13 ryan Exp $$
 */
@Slf4j
@Service
@Proxy(Services.PAYMENT_POINT_ERP_CANCEL)
public class PaymentPointErpCancelInvoker implements ProxyInvoker<PayPointErpCancelRequestDTO, PayPointErpCancelResponseDTO> {


    @Resource(name = "erpPaymentPointService")
    private ErpPaymentPointService erpPaymentPointService;

    @Resource(name = "orderService")
    private OrderService              orderService;


    /**
     * 代理执行
     *
     * @param context the gateway context, include request, response, proxyEnum.
     * @return 业务响应 response
     * @throws ProxyException 代理异常
     */
    @Override
    public PayPointErpCancelResponseDTO invoke(GatewayContext<PayPointErpCancelRequestDTO, PayPointErpCancelResponseDTO> context) throws ProxyException {
        try {
            PayPointErpCancelRequestDTO request = context.getRequest();
            OrderVO order = orderService.queryOrder(request.getOrderSerialNo());
            PayPointErpCancelResponseDTO payPointErpCancelResponseDTO = erpPaymentPointService.cancelPayPoint(PaymentPointErpCancelContext.of(order, request));

            if (payPointErpCancelResponseDTO.isSuccess()) {
                // 后台取消支付分成功，打上人工关闭支付分orderTag
                orderService.updateOrderTags(order, Lists.newArrayList(OrderTag.ERP_CLOSE_PAYMENT_POINT));
            }

            return payPointErpCancelResponseDTO;

        } catch (OrderQueryException e) {
            LoggerUtils.warn(log, "[PaymentPointErpCancelInvoker][invoke] error:{}", e, ThrowableUtil.stackTraceToString(e));
            LYError error = e.getError();
            return PayPointErpCancelResponseDTO.fail(context.getRequest().getTraceId(), error.getCode(), error.getMessage());
        }
    }

}
