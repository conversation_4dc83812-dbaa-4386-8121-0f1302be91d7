package com.ly.travel.car.tradecore.facade.invoker.pay;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.ly.mom.netty.util.internal.ThrowableUtil;
import com.ly.sof.api.error.LYError;
import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.tradecore.builder.pay.PaySerialInfoBuilder;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.business.ctx.StandardOrderContext;
import com.ly.travel.car.tradecore.business.pay.OrderPaidService;
import com.ly.travel.car.tradecore.business.pay.model.PaidContext;
import com.ly.travel.car.tradecore.business.pay.model.PaidRet;
import com.ly.travel.car.tradecore.error.BizErrorFactory;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.facade.request.pay.PaidRequestDTO;
import com.ly.travel.car.tradecore.facade.response.pay.PaidResponseDTO;
import com.ly.travel.car.tradecore.integration.client.redis.impl.LockTemplateProxy;
import com.ly.travel.car.tradecore.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.pay.PaySerialInfoVO;
import com.ly.travel.car.tradecore.order.StandardOrderCtx;
import com.ly.travel.car.tradecore.utils.RedisKeyBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * PaidInvoker
 *
 * <AUTHOR>
 * @version Id: PaidInvoker.java, v 0.1 2024-02-27 18:12 ryan Exp $$
 */
@Slf4j
@Service
@Proxy(Services.PAID)
public class PaidInvoker implements ProxyInvoker<PaidRequestDTO, PaidResponseDTO> {

    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();
    @Resource(name = "orderPaidService")
    private OrderPaidService             orderPaidService;

    @Resource
    private PaySerialInfoBuilder         paySerialInfoBuilder;

    @Resource
    protected RedisClientProxy           redisClientProxy;

    @Resource
    private LockTemplateProxy            lockTemplateProxy;

    /**
     * 代理执行
     *
     * @param context the gateway context, include request, response, proxyEnum.
     * @return 业务响应 response
     * @throws ProxyException 代理异常
     */
    @Override
    public PaidResponseDTO invoke(GatewayContext<PaidRequestDTO, PaidResponseDTO> context) throws ProxyException {
        return lockTemplateProxy.getLockAndResult(RedisKeyBuilder.orderProcessingLock(context.getRequest().getOrderSerialNo()), 3000, (lock) -> {
            if (lock) {
                PaidRequestDTO request = context.getRequest();
                try {
                    // 查询订单
                    StandardOrderContext ctx = StandardOrderCtx.of(request.getOrderSerialNo());
                    OrderVO order = ctx.getOrder();
                    LoggerUtils.info(log, "[PaidInvoker][invoke] 是否预约单:{}", order.isPreOrder());


                    // 支付回写业务
                    List<PaySerialInfoVO> paidSerialInfos = paySerialInfoBuilder.build(request);
                    PaidRet ret = orderPaidService.paid(PaidContext.of(order, request, paidSerialInfos));

                    // 返回结果
                    if (!ret.isSuccess()) {
                        return PaidResponseDTO.fail(request.getTraceId(), ret.getErrorCode(), ret.getMessage());
                    }
                    return PaidResponseDTO.success(request.getTraceId());
                } catch (BuilderException e) {
                    LoggerUtils.error(log, "[PaidInvoker][invoke] error:{}", e, ThrowableUtil.stackTraceToString(e));
                    LYError error = e.getError();
                    return PaidResponseDTO.fail(request.getTraceId(), error.getCode(), error.getMessage());
                }
            } else {
                LoggerUtils.warn(log, "[PaidInvoker][invoke] 获取锁失败, orderSerialNo:{}", context.getRequest().getOrderSerialNo());
                return PaidResponseDTO.fail(context.getRequest().getTraceId(), "lockFail", "获取锁失败!");
            }
        });
    }

}
