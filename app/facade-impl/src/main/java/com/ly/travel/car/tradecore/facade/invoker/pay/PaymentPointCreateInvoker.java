package com.ly.travel.car.tradecore.facade.invoker.pay;

import javax.annotation.Resource;

import com.ly.travel.car.tradecore.error.BizErrorFactory;
import org.springframework.stereotype.Service;

import com.ly.mom.netty.util.internal.ThrowableUtil;
import com.ly.sof.api.error.LYError;
import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.business.pay.PaymentPointCreateService;
import com.ly.travel.car.tradecore.business.pay.model.PaymentPointCreateContext;
import com.ly.travel.car.tradecore.error.OrderQueryException;
import com.ly.travel.car.tradecore.facade.request.pay.PaymentPointCreateRequestDTO;
import com.ly.travel.car.tradecore.facade.response.pay.PaymentPointCreateResponseDTO;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.order.OrderService;

import lombok.extern.slf4j.Slf4j;

/**
 * PaymentPointCreateInvoker
 *
 * <AUTHOR>
 * @version Id: PaymentPointCreateInvoker.java, v 0.1 2024-02-27 18:13 ryan Exp $$
 */
@Slf4j
@Service
@Proxy(Services.PAYMENT_POINT_CREATE)
public class PaymentPointCreateInvoker implements ProxyInvoker<PaymentPointCreateRequestDTO, PaymentPointCreateResponseDTO> {

    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();

    @Resource
    private PaymentPointCreateService paymentPointCreateService;

    @Resource(name = "orderService")
    private OrderService              orderService;

    /**
     * 代理执行
     *
     * @param context the gateway context, include request, response, proxyEnum.
     * @return 业务响应 response
     * @throws ProxyException 代理异常
     */
    @Override
    public PaymentPointCreateResponseDTO invoke(GatewayContext<PaymentPointCreateRequestDTO, PaymentPointCreateResponseDTO> context) throws ProxyException {
        try {
            PaymentPointCreateRequestDTO request = context.getRequest();
            OrderVO order = orderService.queryOrder(request.getOrderSerialNo());

            if (order.isNewXCar() && order.isPreOrder()) {
                //若是新上门接送且是预约单（正式单还未insert db），则返回订单不存在
                LoggerUtils.warn(log, "[PaymentPointCreateInvoker][invoke] 创建支付分回写过快，订单还是预约单:{}", request.getOrderSerialNo());
                throw new OrderQueryException(BIZ_ERROR_FACTORY.orderNotFoundError(request.getOrderSerialNo()));
            }

            return paymentPointCreateService.created(PaymentPointCreateContext.of(order, request));
        } catch (OrderQueryException e) {
            LoggerUtils.warn(log, "[PaymentPointCreateInvoker][invoke] error:{}", e, ThrowableUtil.stackTraceToString(e));
            LYError error = e.getError();
            return PaymentPointCreateResponseDTO.fail(context.getRequest().getTraceId(), error.getCode(), error.getMessage());
        }
    }
}
