package com.ly.travel.car.tradecore.facade.impl;

import com.ly.sof.gateway.AbstractFacade;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.tradecore.facade.VoucherFacade;
import com.ly.travel.car.tradecore.facade.request.voucher.VoucherCommitRequest;
import com.ly.travel.car.tradecore.facade.request.voucher.VoucherGetOrderInfoRequest;
import com.ly.travel.car.tradecore.facade.request.voucher.VoucherShortUrlRequestDTO;
import com.ly.travel.car.tradecore.facade.response.voucher.VoucherCommitResponseDTO;
import com.ly.travel.car.tradecore.facade.response.voucher.VoucherGetOrderInfoResponseDTO;
import com.ly.travel.car.tradecore.facade.response.voucher.VoucherShortChainResponseDTO;
import org.springframework.stereotype.Service;
import com.ly.travel.car.tradecore.business.Services;


/**
 * <AUTHOR>
 * @since 2024/4/28
 */
@Service("voucherFacade")
public class VoucherFacadeImpl extends AbstractFacade implements VoucherFacade {

    @Override
    public VoucherGetOrderInfoResponseDTO getOrderInfo(VoucherGetOrderInfoRequest request) {
        LogContextUtils.setFilter1(request.getTraceId());
        return execute(request, Services.VOUCHER_ORDER_INFO, VoucherGetOrderInfoResponseDTO.class);
    }

    @Override
    public VoucherCommitResponseDTO commit(VoucherCommitRequest request) {
        LogContextUtils.setFilter1(request.getTraceId());
        return execute(request, Services.VOUCHER_ORDER_COMMIT, VoucherCommitResponseDTO.class);
    }

    @Override
    public VoucherShortChainResponseDTO generateShortUrl(VoucherShortUrlRequestDTO request) {
        LogContextUtils.setFilter1(request.getTraceId());
        return execute(request, Services.VOUCHER_GENERATE_SHORT_URL, VoucherShortChainResponseDTO.class);
    }
}
