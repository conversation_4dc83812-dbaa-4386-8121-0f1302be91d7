package com.ly.travel.car.tradecore.facade.impl;

import com.ly.travel.car.tradecore.facade.request.workorder.CreateFeeViolationRequestDTO;
import com.ly.travel.car.tradecore.facade.response.workorder.CreateFeeViolationResponseDTO;
import org.springframework.stereotype.Service;

import com.ly.sof.gateway.AbstractFacade;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.facade.WorkOrderFacade;
import com.ly.travel.car.tradecore.facade.request.workorder.CreateDriverViolationRequestDTO;
import com.ly.travel.car.tradecore.facade.request.workorder.ShowDriverViolationRequestDTO;
import com.ly.travel.car.tradecore.facade.response.workorder.CreateDriverViolationResponseDTO;
import com.ly.travel.car.tradecore.facade.response.workorder.ShowDriverViolationResponseDTO;

/**
 * <AUTHOR>
 * @since 2024/10/23
 */
@Service("workOrderFacade")
public class WorkOrderFacadeImpl extends AbstractFacade implements WorkOrderFacade {

    @Override
    public CreateDriverViolationResponseDTO createDriverViolation(CreateDriverViolationRequestDTO request) {
        LogContextUtils.initTracer(Services.CREATE_DRIVER_VIOLATION, request.getTraceId(), request.getOrderSerialNo());
        return execute(request, Services.CREATE_DRIVER_VIOLATION, CreateDriverViolationResponseDTO.class);
    }

    @Override
    public ShowDriverViolationResponseDTO showDriverViolation(ShowDriverViolationRequestDTO request) {
        LogContextUtils.initTracer(Services.SHOW_DRIVER_VIOLATION, request.getTraceId(), request.getOrderSerialNo());
        return execute(request, Services.SHOW_DRIVER_VIOLATION, ShowDriverViolationResponseDTO.class);
    }

    @Override
    public CreateFeeViolationResponseDTO createFeeWorkOrder(CreateFeeViolationRequestDTO request) {
        LogContextUtils.initTracer(Services.CREATE_FEE_VIOLATION, request.getTraceId(), request.getOrderSerialNo());
        return execute(request, Services.CREATE_FEE_VIOLATION, CreateFeeViolationResponseDTO.class);
    }


}
