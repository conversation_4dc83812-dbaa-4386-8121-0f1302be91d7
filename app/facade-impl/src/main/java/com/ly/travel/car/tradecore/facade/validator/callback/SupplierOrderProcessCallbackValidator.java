package com.ly.travel.car.tradecore.facade.validator.callback;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.facade.request.callback.OrderProcessCallbackRequestDTO;
import com.ly.travel.car.tradecore.facade.validator.BaseValidator;

import lombok.extern.slf4j.Slf4j;

/**
 * 供应链订单过程场景通知回调
 *
 * <AUTHOR>
 * @version Id: SupplierOrderProcessCallbackInvoker, v 0.1 2024/3/1 10:36 icanci Exp $
 */
@Slf4j
@Service
@Proxy(Services.ORDER_PROCESS_CALLBACK)
public class SupplierOrderProcessCallbackValidator extends BaseValidator implements Validator<OrderProcessCallbackRequestDTO> {

    /**
     * 参数校验方法
     *
     * @param request 请求
     * @throws ValidationException 异常
     */
    @Override
    public void validate(OrderProcessCallbackRequestDTO request) throws ValidationException {
        // 请求参数不能为空
        dtoNotNullValidate(request, "request");
        request.setTraceId(StringUtils.defaultIfBlank(request.getTraceId(), UUID.randomUUID().toString()));

        // traceId 不能为空
        dtoNotStringEmptyValidate(request.getTraceId(), "traceId");

        // orderSerialNo 不能为空
        dtoNotStringEmptyValidate(request.getOrderSerialNo(), "orderSerialNo");

        // supplierCode 不能为空
        dtoNotStringEmptyValidate(request.getSupplierCode(), "supplierCode");
    }
}