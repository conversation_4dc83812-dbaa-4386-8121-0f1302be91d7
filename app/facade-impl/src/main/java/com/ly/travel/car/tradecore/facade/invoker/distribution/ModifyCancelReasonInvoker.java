package com.ly.travel.car.tradecore.facade.invoker.distribution;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.business.modify.OrderModifyService;
import com.ly.travel.car.tradecore.facade.request.distribution.ModifyCancelReasonRequestDTO;
import com.ly.travel.car.tradecore.facade.response.distribution.ModifyCancelReasonResponseDTO;

/**
 * 修改取消原因
 *
 * <AUTHOR>
 * @version Id: ModifyCancelReasonInvoker  2024/7/15
 */
@Service
@Proxy(Services.MODIFY_CANCEL_REASON)
public class ModifyCancelReasonInvoker implements ProxyInvoker<ModifyCancelReasonRequestDTO, ModifyCancelReasonResponseDTO> {

    @Resource(name = "orderModifyService")
    private OrderModifyService orderModifyService;

    @Override
    public ModifyCancelReasonResponseDTO invoke(GatewayContext<ModifyCancelReasonRequestDTO, ModifyCancelReasonResponseDTO> context) throws ProxyException {
        return orderModifyService.modifyCancelResponse(context.getRequest());
    }
}
