package com.ly.travel.car.tradecore.facade.validator.trade;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.facade.request.trade.ModifyLinkPhoneRequestDTO;
import com.ly.travel.car.tradecore.facade.validator.BaseValidator;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单修改联系人手机号校验器
 *
 * <AUTHOR>
 * @version Id: ModifyLinkPhoneValidator, v 0.1 2024/2/27 13:24 icanci Exp $
 */
@Slf4j
@Service
@Proxy(Services.MODIFY_LINK_PHONE)
public class ModifyLinkPhoneValidator extends BaseValidator implements Validator<ModifyLinkPhoneRequestDTO> {
    /**
     * 参数校验方法
     *
     * @param request 请求
     * @throws ValidationException 异常
     */
    @Override
    public void validate(ModifyLinkPhoneRequestDTO request) throws ValidationException {
        // 请求参数不能为空
        dtoNotNullValidate(request, "request");
        request.setTraceId(StringUtils.defaultIfBlank(request.getTraceId(), UUID.randomUUID().toString()));

        // traceId 不能为空
        dtoNotStringEmptyValidate(request.getTraceId(), "traceId");

        // orderSerialNo 不能为空
        dtoNotStringEmptyValidate(request.getOrderSerialNo(), "orderSerialNo");

        // 会员id不能为空
        dtoNotNullValidate(request.getMemberIds(), "memberIds");
        dtoNotCollectionEmptyValidate(request.getMemberIds(), "memberIds");

        // 手机号不能为空
        dtoNotStringEmptyValidate(request.getLinkPhone(), "linkPhone");
//        phoneIsLegalValidate(request.getLinkPhone(), "linkPhone");
    }
}
