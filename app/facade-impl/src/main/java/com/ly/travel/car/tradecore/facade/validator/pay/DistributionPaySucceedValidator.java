package com.ly.travel.car.tradecore.facade.validator.pay;

import org.springframework.stereotype.Service;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.facade.request.pay.DistributionPaySucceedRequestDTO;
import com.ly.travel.car.tradecore.facade.validator.BaseValidator;

/**
 * LY.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 * 
 * 分销订单支付成功
 * 
 * <AUTHOR>
 * @since 2024/08/13
 */
@Service
@Proxy(Services.DISTRIBUTION_PAY_SUCCEED)
public class DistributionPaySucceedValidator extends BaseValidator implements Validator<DistributionPaySucceedRequestDTO> {

    @Override
    public void validate(DistributionPaySucceedRequestDTO request) throws ValidationException {
        // dto参数为空校验
        dtoNotNullValidate(request, "paidRequest");

        // traceId 不能为空
        dtoNotStringEmptyValidate(request.getTraceId(), "traceId");

        // orderSerialNo 不能为空
        dtoNotStringEmptyValidate(request.getOrderSerialNo(), "orderSerialNo");
    }
}