package com.ly.travel.car.tradecore.facade.invoker.append;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.ly.sof.api.error.LYError;
import com.ly.sof.api.exception.LYException;
import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.business.accepted.UserConfirmSilentAppendService;
import com.ly.travel.car.tradecore.business.accepted.model.OrderUserConfirmContext;
import com.ly.travel.car.tradecore.business.accepted.model.UserConfirmRet;
import com.ly.travel.car.tradecore.error.BizErrorFactory;
import com.ly.travel.car.tradecore.facade.request.append.ConfirmSilentAppendRequestDTO;
import com.ly.travel.car.tradecore.facade.response.append.ConfirmSilentAppendResponseDTO;
import com.ly.travel.car.tradecore.integration.client.redis.impl.LockTemplateProxy;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.order.OrderService;
import com.ly.travel.car.tradecore.utils.RedisKeyBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * ConfirmSilentAppendInvoker
 *
 * <AUTHOR>
 * @version Id: ConfirmSilentAppendInvoker.java, v 0.1 2024-02-29 16:24 ryan Exp $$
 */
@Service
@Slf4j
@Proxy(Services.CONFIRM_SILENT_APPEND)
public class ConfirmSilentAppendInvoker implements ProxyInvoker<ConfirmSilentAppendRequestDTO, ConfirmSilentAppendResponseDTO> {

    private final static BizErrorFactory   ERROR = BizErrorFactory.getInstance();

    @Resource(name = "userConfirmSilentAppendService")
    private UserConfirmSilentAppendService userConfirmSilentAppendService;

    @Resource(name = "orderService")
    private OrderService                   orderService;

    @Resource
    private LockTemplateProxy              lockTemplateProxy;
    
    /**
     * 代理执行
     *
     * @param context the gateway context, include request, response, proxyEnum.
     * @return 业务响应 response
     * @throws ProxyException 代理异常
     */
    @Override
    public ConfirmSilentAppendResponseDTO invoke(GatewayContext<ConfirmSilentAppendRequestDTO, ConfirmSilentAppendResponseDTO> context) throws ProxyException {

        return lockTemplateProxy.getLockAndResult(RedisKeyBuilder.orderProcessingLock(context.getRequest().getOrderSerialNo()), 3000, (lock) -> {
            if (lock) {
                // 查询订单
                try {
                    OrderVO order = orderService.queryOrder(context.getRequest().getOrderSerialNo());
                    // 确认静默追加
                    UserConfirmRet ret = userConfirmSilentAppendService.confirm(OrderUserConfirmContext.of(order, context.getRequest()));
                    // 构建响应
                    return buildResponse(context.getRequest(), ret);
                } catch (Exception e) {
                    return buildResponse(context.getRequest(), e);
                }
            } else {
                LoggerUtils.warn(log, "[ConfirmSilentAppendInvoker][lock] 订单{},频繁请求 or 其他流程在处理中", context.getRequest().getOrderSerialNo());
                return buildResponse(context.getRequest(), ERROR.unsupportedOperation("静默接单确认接单并发错误"));
            }
        });
    }

    /**
     * Build response confirm silent append response dto.
     */
    private ConfirmSilentAppendResponseDTO buildResponse(ConfirmSilentAppendRequestDTO request, Exception e) {
        LYError error = (e instanceof LYException) ? ((LYException) e).getError() : ERROR.sysErr(e.getMessage());
        return ConfirmSilentAppendResponseDTO.create(false, request.getTraceId(), error.getCode(), error.getMessage());
    }

    /**
     * Build response confirm silent append response dto.
     */
    private ConfirmSilentAppendResponseDTO buildResponse(ConfirmSilentAppendRequestDTO request, UserConfirmRet ret) {
        return ConfirmSilentAppendResponseDTO.create(ret.isSuccess(), request.getTraceId(), ret.getErrorCode(), ret.getMessage());
    }

    /**
     * Build response confirm silent append response dto.
     */
    private ConfirmSilentAppendResponseDTO buildResponse(ConfirmSilentAppendRequestDTO request, LYError lyError) {
        return ConfirmSilentAppendResponseDTO.create(false, request.getTraceId(), lyError.getCode(), lyError.getMessage());
    }

}
