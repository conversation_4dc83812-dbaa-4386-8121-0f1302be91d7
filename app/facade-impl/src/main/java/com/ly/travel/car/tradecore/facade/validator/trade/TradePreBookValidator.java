package com.ly.travel.car.tradecore.facade.validator.trade;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.common.model.enums.ServiceType;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.facade.model.TripDTO;
import com.ly.travel.car.tradecore.facade.request.trade.BookRequestDTO;
import com.ly.travel.car.tradecore.model.enums.OrderType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 订单下单校验器
 *
 * <AUTHOR>
 * @version Id: TradeBookValidator, v 0.1 2024/2/23 16:17 icanci Exp $
 */
@Slf4j
@Service
@Proxy(Services.PRE_BOOK)
public class TradePreBookValidator extends TradeBookValidator implements Validator<BookRequestDTO> {

    /**
     * 参数校验方法
     *
     * @param request 请求
     * @throws ValidationException 异常
     */
    @Override
    public void validate(BookRequestDTO request) throws ValidationException {
        super.validate(request);
    }

    @Override
    protected void doTripValidate(TripDTO trip, OrderType orderType) throws ValidationException {
        dtoNotNullValidate(trip, "trip");

        if (StringUtils.isBlank(trip.getCarSign()) && StringUtils.isBlank(trip.getSfcSign()) && StringUtils.isBlank(trip.getSign())) {
            throw new ValidationException(ERROR_FACTORY.paramNull("sfc/car sign"));
        }
        if (StringUtils.isNotBlank(trip.getCarSign()) && CollectionUtils.isEmpty(trip.getSignResourceIds())) {
            throw new ValidationException(ERROR_FACTORY.paramNull("car resources"));
        }
        if (StringUtils.isNotBlank(trip.getSfcSign())) {
            ServiceType serviceType = ServiceType.of(trip.getSfcServiceType());
            if (serviceType == null) {
                throw new ValidationException(ERROR_FACTORY.illegalParam("serviceType", trip.getSfcServiceType()));
            }
        }

        if (StringUtils.isNotBlank(trip.getCarSign())) {
            if (orderType.isCarJs() && trip.getSignResourceIds().size() != 1) {
                throw new ValidationException(ERROR_FACTORY.illegalParam("signResourceIds.size != 1", trip.getSignResourceIds()));
            }
        }

        dtoNotNullValidate(trip.getDepDateTime(), "depDateTime");

        if (trip.getUsageDelay() < 0) {
            throw new ValidationException(ERROR_FACTORY.illegalParam("usageDelay", trip.getUsageDelay()));
        }
    }
}