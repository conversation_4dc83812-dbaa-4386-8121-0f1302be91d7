package com.ly.travel.car.tradecore.facade.invoker.trade;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.business.modify.OrderModifyService;
import com.ly.travel.car.tradecore.facade.request.trade.CheckModifyPhoneRequestDTO;
import com.ly.travel.car.tradecore.facade.response.trade.CheckModifyPhoneResponseDTO;

/**
 * 类名称
 *
 * <AUTHOR>
 * @version Id: CheckModifyPhoneInvoker  2024/6/20
 */
@Service
@Proxy(Services.CHECK_MODIFY_PHONE)
public class CheckModifyPhoneInvoker implements ProxyInvoker<CheckModifyPhoneRequestDTO, CheckModifyPhoneResponseDTO> {

    @Resource
    private OrderModifyService orderModifyService;

    @Override
    public CheckModifyPhoneResponseDTO invoke(GatewayContext<CheckModifyPhoneRequestDTO, CheckModifyPhoneResponseDTO> context) throws ProxyException {
        return orderModifyService.checkModifyPhone(context.getRequest());
    }
}
