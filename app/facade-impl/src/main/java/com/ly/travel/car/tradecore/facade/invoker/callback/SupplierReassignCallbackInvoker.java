package com.ly.travel.car.tradecore.facade.invoker.callback;

import javax.annotation.Resource;

import com.ly.travel.car.tradecore.error.ValidateException;
import com.ly.travel.car.tradecore.integration.client.redis.impl.LockTemplateProxy;
import com.ly.travel.car.tradecore.utils.RedisKeyBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ly.mom.netty.util.internal.ThrowableUtil;
import com.ly.sof.api.error.LYError;
import com.ly.sof.api.exception.LYException;
import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.tradecore.business.Services;
import com.ly.travel.car.tradecore.business.reassigncallback.OrderReassignCallbackService;
import com.ly.travel.car.tradecore.error.BizErrorFactory;
import com.ly.travel.car.tradecore.facade.request.callback.ReassignCallbackRequestDTO;
import com.ly.travel.car.tradecore.facade.response.callback.ReassignCallbackResponseDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * 供应链改派通知
 * 
 * <AUTHOR>
 * @version Id: SupplierReassignCallbackInvoker, v 0.1 2024/3/4 10:56 icanci Exp $
 */
@Slf4j
@Service
@Proxy(Services.REASSIGN_CALLBACK)
public class SupplierReassignCallbackInvoker implements ProxyInvoker<ReassignCallbackRequestDTO, ReassignCallbackResponseDTO> {

    private final static BizErrorFactory ERROR = BizErrorFactory.getInstance();

    @Resource
    private OrderReassignCallbackService reassignCallbackService;
    
    @Resource
    private LockTemplateProxy            lockTemplateProxy;

    @Override
    public ReassignCallbackResponseDTO invoke(GatewayContext<ReassignCallbackRequestDTO, ReassignCallbackResponseDTO> context) throws ProxyException {
        try {
            return lockTemplateProxy.getLockAndResult(RedisKeyBuilder.orderProcessingLock(context.getRequest().getOrderSerialNo()), 3000, (lock) -> {
               if(lock) {
                   try {
                       reassignCallbackService.dealInitiateCallback(context.getRequest());
                       return ReassignCallbackResponseDTO.success(context.getRequest().getTraceId());
                   } catch (ValidateException e) {
                       if (StringUtils.equals(e.getError().getCode(), "LY0510040007")) {
                           LoggerUtils.warn(log, "[SupplierReassignCallbackInvoker][invoke] 分销订单-供应链改派中通知处理异常 error:{}", e, ThrowableUtil.stackTraceToString(e));
                       } else {
                           LoggerUtils.error(log, "[SupplierReassignCallbackInvoker][invoke] 供应链改派中通知处理异常 error:{}", e, ThrowableUtil.stackTraceToString(e));
                       }
                       return buildFailResponse(context.getRequest(), e);
                   } catch (Exception e) {
                       LoggerUtils.error(log, "[SupplierReassignCallbackInvoker][invoke] 供应链改派中通知处理异常 error:{}", e, ThrowableUtil.stackTraceToString(e));
                       return buildFailResponse(context.getRequest(), e);
                   }
               }else{
                    LYError lyError = ERROR.requestFrequentErr(Services.REASSIGN_CALLBACK + "频繁请求，订单处理中");
                    return buildFailResponse(context.getRequest(), lyError);
               }
            });
        } catch (Exception e) {
            return buildFailResponse(context.getRequest(), e);
        }
    }

    /**
     * 构建错误响应
     *
     * @param request 请参
     * @param e 异常
     * @return {@link ReassignCallbackResponseDTO}
     */
    private ReassignCallbackResponseDTO buildFailResponse(ReassignCallbackRequestDTO request, Exception e) {
        LYError error = (e instanceof LYException) ? ((LYException) e).getError() : ERROR.sysErr(e.getMessage());
        return ReassignCallbackResponseDTO.fail(request.getTraceId(), error.getCode(), error.getMessage());
    }

    /**
     * 构建错误响应
     *
     * @param request 请参
     * @param error 异常
     * @return {@link ReassignCallbackResponseDTO}
     */
    private ReassignCallbackResponseDTO buildFailResponse(ReassignCallbackRequestDTO request, LYError error) {
        return ReassignCallbackResponseDTO.fail(request.getTraceId(), error.getCode(), error.getMessage());
    }
}