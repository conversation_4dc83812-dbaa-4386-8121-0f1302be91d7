package com.ly.travel.car.tradecore.facade.response.pay;

import com.ly.sof.facade.base.BaseResponseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付校验返回参数
 * 
 * <AUTHOR>
 * @version Id: PayCheckResponseDTO, v 0.1 2024/2/23 14:17 icanci Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PayCheckResponseDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 5332861463980973239L;

    /**
     * 订单支付详情信息
     */
    private OrderPayDetailDTO orderPayDetail;

    public static PayCheckResponseDTO successResponse(String traceId, OrderPayDetailDTO payDetail) {
        PayCheckResponseDTO response = new PayCheckResponseDTO();
        response.setOrderPayDetail(payDetail);
        response.setTraceId(traceId);
        response.setSuccess(true);
        return response;
    }

    public static PayCheckResponseDTO failureResponse(String traceId, String errorCode, String errorMessage) {
        PayCheckResponseDTO response = new PayCheckResponseDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }
}