package com.ly.travel.car.tradecore.facade.model;

import java.io.Serializable;

import lombok.Data;

/**
 * 乘客信息
 * 
 * <AUTHOR>
 * @version Id: PassengerDTO, v 0.1 2024/2/23 11:23 icanci Exp $
 */
@Data
public class XCarPassengerDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 乘客姓名 非必填 */
    private String            name;
    /** 乘车人手机号 必填 A */
    private String            linkPhone;
    /** 区号 非必填 */
    private String            areaCode;
    /** 乘客人数 必填，并且必须大于0 */
    private int               passengerCount;
}