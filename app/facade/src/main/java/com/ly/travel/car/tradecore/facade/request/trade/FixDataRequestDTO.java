package com.ly.travel.car.tradecore.facade.request.trade;

import java.util.List;

import com.ly.sof.facade.base.BaseRequestDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@EqualsAndHashCode(callSuper = true)
@Data
public class FixDataRequestDTO extends BaseRequestDTO {
    /**
            * 订单号
     */
    private String           orderSerialNo;

    /**
     * 修改场景 6处理多收
     */
    private int              type;

    /**
     * 多收请求体
     */
    private List<MorePayReq> morePayChanges;

    /**
     * token
     */
    private String           token;

    @Getter
    @Setter
    public static class MorePayReq {
        private String  orderSerialNo;
        private String  amount;
        private int     type;
        private boolean change;
    }
}
