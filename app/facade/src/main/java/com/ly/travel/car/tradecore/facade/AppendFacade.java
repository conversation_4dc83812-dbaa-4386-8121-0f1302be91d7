package com.ly.travel.car.tradecore.facade;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import com.ly.travel.car.tradecore.facade.request.append.*;
import com.ly.travel.car.tradecore.facade.response.append.*;

/**
 * 追加服务门面
 *
 * <AUTHOR>
 * @version Id: AppendFacade, v 0.1 2024/2/23 14:02 icanci Exp $
 */
@Path("append")
public interface AppendFacade {
    /**
     * 追加辅营商品[网约车/顺风车]
     *
     * @param param 追加辅营商品参数
     * @return 追加辅营商品返回结果
     */
    @POST
    @Produces({ "application/json; charset=UTF-8" })
    @Consumes({ "application/json; charset=UTF-8" })
    @Path("appendGoods")
    AppendGoodsResponseDTO appendGoods(AppendGoodsRequestDTO param);

    /**
     * 追加车型[网约车/顺风车]
     *
     * @param param 追加车型[网约车/顺风车]参数
     * @return 追加车型[网约车/顺风车]返回结果
     */
    @POST
    @Produces({ "application/json; charset=UTF-8" })
    @Consumes({ "application/json; charset=UTF-8" })
    @Path("appendCar")
    AppendCarResponseDTO appendCar(AppendCarRequestDTO param);

    /**
     * 邀请司机[顺风车]
     *
     * @param param 邀请司机参数
     * @return 邀请司机返回结果
     */
    @POST
    @Produces({ "application/json; charset=UTF-8" })
    @Consumes({ "application/json; charset=UTF-8" })
    @Path("inviteDriver")
    InviteDriverResponseDTO inviteDriver(InviteDriverRequestDTO param);

    /**
     * 用户追加加速匹配服务(感谢金)[顺风车]
     *
     * @param param 用户追加加速匹配服务(感谢金)请参
     * @return {@link AppendThanksMoneyResponseDTO}
     */
    @POST
    @Produces({ "application/json; charset=UTF-8" })
    @Consumes({ "application/json; charset=UTF-8" })
    @Path("appendThanksMoney")
    AppendThanksMoneyResponseDTO appendThanksMoney(AppendThanksMoneyRequestDTO param);

    /**
     * 确认是否可以追加加速匹配服务(感谢金)[顺风车]
     *
     * @return {@link AppendThanksMoneyResponseDTO}
     */
    @POST
    @Produces({ "application/json; charset=UTF-8" })
    @Consumes({ "application/json; charset=UTF-8" })
    @Path("checkCanAppendThanksMoney")
    AppendThanksMoneyResponseDTO checkCanAppendThanksMoney(AppendThanksMoneyRequestDTO param);

    /**
     * 静默追加车型确认-网约车
     *
     * @param param 静默追加车型确认参数
     * @return 静默追加车型确认返回结果
     */
    @POST
    @Produces({ "application/json; charset=UTF-8" })
    @Consumes({ "application/json; charset=UTF-8" })
    @Path("confirmSilentAppend")
    ConfirmSilentAppendResponseDTO confirmSilentAppend(ConfirmSilentAppendRequestDTO param);
}
