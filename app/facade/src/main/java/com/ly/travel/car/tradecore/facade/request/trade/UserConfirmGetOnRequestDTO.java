package com.ly.travel.car.tradecore.facade.request.trade;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import com.ly.sof.facade.base.BaseRequestDTO;

/**
 * 用户确认上车请求参数
 * 
 * <AUTHOR>
 * @version Id: UserConfirmGetOnRequestDTO, v 0.1 2024/2/28 19:40 icanci Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserConfirmGetOnRequestDTO extends BaseRequestDTO {
    /** 会员id 必填 */
    private List<String> memberIds;
    /** 订单流水号 必填 */
    private String       orderSerialNo;
    /** 乘客当前的经度     */
    private String       longitude;
    /** 乘客当前的纬度    */
    private String       latitude;
}