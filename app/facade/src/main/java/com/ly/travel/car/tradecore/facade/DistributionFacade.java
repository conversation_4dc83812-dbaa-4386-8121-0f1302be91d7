package com.ly.travel.car.tradecore.facade;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import com.ly.travel.car.tradecore.facade.request.distribution.ConfirmDaOrderRequestDTO;
import com.ly.travel.car.tradecore.facade.request.distribution.DaBookRequestDTO;
import com.ly.travel.car.tradecore.facade.request.distribution.ModifyCancelReasonRequestDTO;
import com.ly.travel.car.tradecore.facade.response.distribution.ConfirmDaOrderResponseDTO;
import com.ly.travel.car.tradecore.facade.response.distribution.DaBookResponseDTO;
import com.ly.travel.car.tradecore.facade.response.distribution.ModifyCancelReasonResponseDTO;

/**
 * 分销调用接口
 * <AUTHOR>
 * @version Id: DistributionFacade, v 0.1 2024/5/13 15:27 yi6.liu Exp $
 */
@Path("distribution")
public interface DistributionFacade {

    /**
     * 分销-下单接口
     *
     * @param param 下单请求参数
     * @return 下单返回结果
     */
    @POST
    @Produces({ "application/json; charset=UTF-8" })
    @Consumes({ "application/json; charset=UTF-8" })
    @Path("daBook")
    DaBookResponseDTO daBook(DaBookRequestDTO param);

    /**
     * 分销-确认订单接口
     * @param param
     * @return
     */
    @POST
    @Produces({ "application/json; charset=UTF-8" })
    @Consumes({ "application/json; charset=UTF-8" })
    @Path("confirmDaOrder")
    ConfirmDaOrderResponseDTO confirmDaOrder(ConfirmDaOrderRequestDTO param);

    /**
     * 分销-修改取消原因
     * @param param
     * @return
     */
    @POST
    @Produces({ "application/json; charset=UTF-8" })
    @Consumes({ "application/json; charset=UTF-8" })
    @Path("modifyCancelReason")
    ModifyCancelReasonResponseDTO modifyCancelReason(ModifyCancelReasonRequestDTO param);
}
