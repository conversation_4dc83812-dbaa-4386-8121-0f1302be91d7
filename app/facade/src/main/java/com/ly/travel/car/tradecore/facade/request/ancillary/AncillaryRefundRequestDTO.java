package com.ly.travel.car.tradecore.facade.request.ancillary;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AncillaryRefundRequestDTO extends BaseRequestDTO {

    /** 会员id 必填 */
    private List<String> memberIds;
    /** 订单 必填 */
    private String       orderSerialNo;
    /** 指定要退的辅营 itemId集合  必填 */
    private List<String> itemIds;
    /** 辅营单退的subRefundType 非必填 */
    private String       subRefundType;
}
