package com.ly.travel.car.tradecore.facade.response.trade;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.ly.sof.facade.base.BaseResponseDTO;

/**
 * 用户确认上车返回结果
 *
 * <AUTHOR>
 * @version Id: UserConfirmGetOnResponseDTO, v 0.1 2024/2/28 20:05 icanci Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserConfirmGetOnResponseDTO extends BaseResponseDTO {
    /**
     * 用户确认上车返回结果
     *
     * @param traceId traceId
     * @return UserConfirmGetOnResponseDTO
     */
    public static UserConfirmGetOnResponseDTO successResponse(String traceId) {
        UserConfirmGetOnResponseDTO response = new UserConfirmGetOnResponseDTO();
        response.setTraceId(traceId);
        response.setSuccess(true);
        return response;
    }

    /**
     * 用户确认上车返回结果
     * 
     * @param traceId traceId
     * @param errorCode errorCode
     * @param errorMessage errorMessage
     * @return UserConfirmGetOnResponseDTO
     */
    public static UserConfirmGetOnResponseDTO failureResponse(String traceId, String errorCode, String errorMessage) {
        UserConfirmGetOnResponseDTO response = new UserConfirmGetOnResponseDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

}