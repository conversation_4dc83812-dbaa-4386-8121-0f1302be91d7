package com.ly.travel.car.tradecore.facade.request.trade;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ContactAfterTripRequestDTO extends BaseRequestDTO {

    /** 会员id 必填 */
    private List<String> memberIds;
    /** 订单 必填 */
    private String       orderSerialNo;

    /** 联系人手机号 */
    private String       linkPhone;

    /**
     * 操作人
     */
    private String       operator;

    /**
     * 备注
     */
    private String       remark;
}
