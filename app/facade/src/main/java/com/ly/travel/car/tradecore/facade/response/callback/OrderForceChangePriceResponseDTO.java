package com.ly.travel.car.tradecore.facade.response.callback;

import lombok.Data;

/**
 * 强制改价接口返回
 *
 * <AUTHOR>
 * @version Id: OrderForceChangePriceRequestDTO  2025/5/9
 */
@Data
public class OrderForceChangePriceResponseDTO extends OrderOperateCallBackResponseDTO {

    private static final long serialVersionUID = -1355678136363667927L;

    /**
     * 成功场景
     *
     * @param traceId traceId
     * @return OrderOperateCallBackResponseDTO
     */
    public static OrderForceChangePriceResponseDTO success(String traceId) {
        OrderForceChangePriceResponseDTO response = new OrderForceChangePriceResponseDTO();
        response.setSuccess(true);
        response.setTraceId(traceId);
        return response;
    }

    /**
     * 失败场景
     *
     * @param traceId traceId
     * @param errorCode errorCode
     * @param errorMessage errorMessage
     * @return OrderOperateCallBackResponseDTO
     */
    public static OrderForceChangePriceResponseDTO fail(String traceId, String errorCode, String errorMessage) {
        OrderForceChangePriceResponseDTO response = new OrderForceChangePriceResponseDTO();
        response.setSuccess(false);
        response.setTraceId(traceId);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static OrderForceChangePriceResponseDTO fail(String traceId, String errorMessage) {
        OrderForceChangePriceResponseDTO response = new OrderForceChangePriceResponseDTO();
        response.setSuccess(false);
        response.setTraceId(traceId);
        response.setErrorMessage(errorMessage);
        return response;
    }
}
