package com.ly.travel.car.tradecore.facade.request.ancillary;

import java.util.List;

import com.ly.sof.facade.base.BaseRequestDTO;

import lombok.Data;

/**
 * 请求单退辅营临时单接口
 *
 * <AUTHOR>
 * @version Id: AncillaryRefundForTempRequestDTO  2025/4/7
 */
@Data
public class AncillaryRefundForTempRequestDTO extends BaseRequestDTO {
    private static final long serialVersionUID = 7742311162959327833L;

    /** 会员id 必填 */
    private List<String>      memberIds;
    /** 订单 必填 */
    private String            orderSerialNo;
    /** 指定要退的辅营 itemId  必填 */
    private String            itemId;
}
