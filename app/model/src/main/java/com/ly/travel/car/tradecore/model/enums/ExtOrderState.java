package com.ly.travel.car.tradecore.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 类名称
 *
 * <AUTHOR>
 * @version Id: ExtOrderState  2024/5/15
 */
@Getter
@AllArgsConstructor
public enum ExtOrderState {


    /** 已派单未确认 */
    DISPATCHED_NOT_CONFIRMED(0, "已派单未确认"),

    /** 已改派未确认 */
    RE_DISPATCHED_NOT_CONFIRMED(1, "已改派未确认"),


    ;



    /** 枚举编码 */
    private final int                             code;

    /** 枚举描述 */
    private final String                          desc;

    private static final Map<Integer, ExtOrderState> ENUMS = new HashMap<>();

    static {
        for (ExtOrderState value : ExtOrderState.values()) {
            ENUMS.put(value.code, value);
        }
    }

    /**
     * getByCode
     */
    public static ExtOrderState getByCode(int code) {
        return ENUMS.get(code);
    }
}
