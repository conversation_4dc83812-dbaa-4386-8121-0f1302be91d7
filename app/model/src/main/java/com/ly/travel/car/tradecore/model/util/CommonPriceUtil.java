package com.ly.travel.car.tradecore.model.util;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.ly.sof.utils.common.Money;
import com.ly.travel.car.tradecore.model.enums.OrderTag;
import com.ly.travel.car.tradecore.model.price.BaseGoodsWrapper;
import com.ly.travel.car.tradecore.model.price.DeductionPriceDetail;
import com.ly.travel.car.tradecore.model.price.SfcDiffMoneyDetail;

public class CommonPriceUtil {

    /**
     * 基于最新的用车价格，计算抵扣价格
     *
     * @param newCarRealPrice the new real price
     * @return the money
     */
    public static DeductionPriceDetail calcDeductionPriceWithNewCarPrice(List<BaseGoodsWrapper> negativeGoods, Money newCarRealPrice, Money insurePrice, Money minPrice, List<String> orderTags) {
        DeductionPriceDetail deductionPriceDetail = new DeductionPriceDetail();
        // 1、取出票价(包含保险)
        Money ticketSalePrice = newCarRealPrice.add(insurePrice);

        // 2、计算出所有负值
        List<BaseGoodsWrapper> negativeGoodsWrappers = negativeGoods;
        // 先根据
        Money negativePrice = negativeGoodsWrappers.stream().map(BaseGoodsWrapper::getSalePrice).reduce(new Money(0), Money::add);

        // 票价大于等于1块，无需一元兜底
        Money totalDiscount = CollectionUtils.isNotEmpty(orderTags) && orderTags.contains(OrderTag.ZERO_ORDER_BILL.name()) ? ticketSalePrice : ticketSalePrice.subtract(minPrice);

        // 票价大于0，无需一元兜底
        if (totalDiscount.add(negativePrice).getCent() >= new Money().getCent()) {
            deductionPriceDetail.setDeductionPrice(negativePrice);
            return deductionPriceDetail;
        }
        deductionPriceDetail.setUseMin(true);
        // 2、一元兜底

        // 将 negativeGoods 根据价格排序，根据规则进行排序
        negativeGoodsWrappers.sort(new Comparator<BaseGoodsWrapper>() {
            @Override
            public int compare(BaseGoodsWrapper o1, BaseGoodsWrapper o2) {
                // 类型不等，直接返回
                if (o1.getNegativeOrder() != o2.getNegativeOrder()) {
                    return o1.getNegativeOrder() - o2.getNegativeOrder();
                } else {
                    // 类型相等，按照价格进行处理
                    return o1.getSalePrice().getAmount().compareTo(o2.getSalePrice().getAmount());
                }
            }
        });

        // 3、计算更新值，需要抵扣的票价
        BigDecimal positiveAmount = totalDiscount.getAmount();
        for (BaseGoodsWrapper negativeGood : negativeGoodsWrappers) {
            // 如果 positiveAmount <= 0 ,说明已经抵扣完了，剩下的抵扣项都应该设置为0
            if (positiveAmount.compareTo(BigDecimal.ZERO) <= 0) {
                // 说明已经抵扣完了
                negativeGood.setCalculateRealPrice(new Money(0));
                continue;
            }

            // 负数
            BigDecimal salePrice = negativeGood.getSalePrice().getAmount();
            // 如果 positiveAmount < 0 ,
            BigDecimal tempPositiveAmount = new BigDecimal(positiveAmount.toPlainString()); // 10

            positiveAmount = positiveAmount.add(salePrice); // 10 - 11 = -1

            // 小于等于0，说明已经抵扣完了，需要设置为1
            if (positiveAmount.compareTo(BigDecimal.ZERO) <= 0) {
                negativeGood.setCalculateRealPrice(new Money(tempPositiveAmount.abs().negate()));
            }
        }
        Money reduce = negativeGoodsWrappers.stream().map(BaseGoodsWrapper::getRealPrice).reduce(new Money(0), Money::add);
        deductionPriceDetail.setDeductionPrice(reduce);

        // 返回所有更新实际抵扣后的抵扣项总额
        return deductionPriceDetail;
    }

    /**
     * 根据资源票价计算差额
     */
    public static SfcDiffMoneyDetail getSfcDiffMoneyByResource(List<BaseGoodsWrapper> negativeGoodsWrapper, Money ticketPrice, List<BaseGoodsWrapper> newNegativeGoodsWrapper, Money resourcePrice, Money allInsurePrice, Money minPrice, List<String> orderTags) {
        // 计算当前票价
        DeductionPriceDetail ticketDeductionPriceDetail = calcDeductionPriceWithNewCarPrice(negativeGoodsWrapper, ticketPrice, allInsurePrice, minPrice, orderTags);
        // 计算资源票价
        DeductionPriceDetail resourceDeductionPriceDetail = calcDeductionPriceWithNewCarPrice(newNegativeGoodsWrapper, resourcePrice, allInsurePrice, minPrice, orderTags);
        //当前票价
        Money ticketDeductionPrice = ticketPrice.add(allInsurePrice).add(ticketDeductionPriceDetail.getDeductionPrice());
        //资源票价
        Money resourceDeductionPrice = resourcePrice.add(allInsurePrice).add(resourceDeductionPriceDetail.getDeductionPrice());

        SfcDiffMoneyDetail sfcDiffMoneyDetail = new SfcDiffMoneyDetail();
        sfcDiffMoneyDetail.setNowTicketRealPrice(ticketDeductionPrice);
        sfcDiffMoneyDetail.setResourceTicketRealPrice(resourceDeductionPrice);
        sfcDiffMoneyDetail.setDiffMoney(ticketDeductionPrice.subtract(resourceDeductionPrice));
        return sfcDiffMoneyDetail;
    }

}
