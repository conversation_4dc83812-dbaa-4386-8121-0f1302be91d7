package com.ly.travel.car.tradecore.model.trip;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.ly.flight.ancillary.rights.common.enums.RightsItemStatusEnum;
import com.ly.sof.utils.common.EnvUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.common.model.enums.CarTypeEnum;
import com.ly.travel.car.common.model.enums.ChargeTypeEnum;
import com.ly.travel.car.common.model.enums.ServiceType;
import com.ly.travel.car.common.model.enums.SupplierChargeTypeEnum;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.*;
import com.ly.travel.car.tradecore.model.ext.AdditionServerVO;
import com.ly.travel.car.tradecore.model.ext.IconInfoExtVO;
import com.ly.travel.car.tradecore.model.ext.InterlinkOrderExtVO;
import com.ly.travel.car.tradecore.model.ext.ResourceExtVO;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 资源信息
 *
 * <AUTHOR>
 * @version Id: ResourceVO, v 0.1 2024/2/26 11:12 icanci Exp $
 */
@Data
@Builder
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class ResourceVO implements Serializable {

    private static final long                      serialVersionUID = 1L;

    /** 资源状态 */
    private ResourceState                          state;

    /** 资源类 */
    private ResourceType                           resourceType;
    //顺风车 供应商类型
    private SfcAdditionalType                      sfcAdditionalType;
    /**
     * 顺风车预计特殊差价(例如:特惠车主)
     */
    private String                                 sfcAdditionalDiffMoney;
    /** 车次资源选择类型 */
    private CarChooseType                          chooseType;
    /** 订单流水号 */
    private String                                 orderSerialNo;
    /** 费用收取类型,1-预估,2-一口价 */
    private ChargeTypeEnum                         chargeType;
    /**
     * 供应商原始报价类型
     * <p>
     *     说明1：全包一口价，2：不含附加费一口价
     */
    private SupplierChargeTypeEnum                 supplierChargeType;
    /** 对C资源报价（网约车、顺风车的预估价或一口价） */
    private Money                                  salePrice;
    /** 对供资源报价（网约车、顺风车的预估价或一口价） */
    private Money                                  supplierPrice;
    /** 询价route提供的券后价,目前仅支持顺风车/网约车 */
    private Money                                  finalPrice;
    /** 询价route提供的用于计算返现的券后价,目前仅支持顺风车,且未存DB */
    private Money                                  cashBackCalculationAmount;
    /** 预估运行时长（秒） */
    private int                                    runTime;
    /** 预估运行里程（米）*/
    private double                                 runDistance;
    // =============== 车次信息 ===============
    /** 车次唯一编号 */
    private String                                 resourceId;
    /**
     * 车牌号
     * <p>
     *     外显车牌号
     */
    private String                                 carNum;
    /**
     * 车牌号（原始车票)
     * <p>
     *     萌艇订单可能会改派司机车辆，这里记录原始车牌号，carNum记录改派后的新车牌号
     */
    private String                                 originalCarNum;
    /** 车辆品牌 必填 */
    private String                                 brand;
    /** 车类型（经济型、舒适型等）此值只有网约车才会有 注意Npe */
    private CarTypeEnum                            carType;
    /** 车辆颜色 必填 */
    private String                                 color;
    /** 车次标签 */
    private List<String>                           carTags;
    /** 服务类型 此值只有顺风车才会有 注意Npe*/
    private ServiceType                            serviceType;
    /**
     * 接单车辆code
     */
    private String                                 vehicleCode;

    // =============== 乘客信息 ===============
    /** 乘客虚拟手机号 */
    private String                                 passengerVirtualPhone;

    /** 代叫虚拟号，代叫场景需要赋值 */
    private String                                 pronounVirtualLinkPhone;

    // =============== 司机信息 ===============
    /** 接单司机Code  */
    private String                                 driverCode;
    /** 司机姓名 */
    private String                                 driverName;
    /** 司机IM Code 非必填 */
    private String                                 driverChatCode;
    /** 司机电话 */
    private String                                 driverPhone;
    /** 司机虚拟手机号  */
    private String                                 driverVirtualPhone;
    /** 司机星级 */
    private String                                 level;
    /** 司机微信号 */
    private String                                 wechat;

    // =============== 供应商信息 ===============
    /** 供应链订单号 */
    private String                                 supplierOrderSerialNo;
    /**
     * 供应商订单号（第三方的供应商单号）
     */
    private String                                 merchantOrderNo;
    /** 供应商id */
    private String                                 supplierId;
    /** 供应商Code */
    private String                                 supplierCode;
    /** 供应商名称 */
    private String                                 supplierName;
    /** 供应商对客名称 */
    private String                                 supplierNameToClient;
    // =============== 券编号 ===============
    /** 券编号 */
    private List<ResourceCouponVO>                 coupons;

    // =============== 抵扣项 ===============
    /**
     * 抵扣项
     * 1. 黑金抵扣
     */
    private List<ResourceDiscountVO>               discounts;

    /**
     * 供应商支持的能力标签，如果支持则返回对应的标签，否则不返回
     * <p>
     * 说明：{@link com.ly.travel.car.tradecore.model.enums.SupplierTag}
     */
    private List<String>                           supplierTags;

    /**
     * true:连环单
     * 连环单标记
     */
    private boolean                                isInterlinkOrder;

    /**
     * 连环单（接力单）信息【非必填】
     */
    private InterlinkOrderExtVO                    interlinkOrderInfo;

    /**
     * 是否供应商支持开票
     */
    private boolean                                supportInvoice;

    /**
     * 同程开票的开票主体code(supportInvoice = false)
     */
    private int                                    tcInvoicingCode;

    /**
     * 同程开票的开票主体名称(supportInvoice = false)
     */
    private String                                 tcInvoicingName;

    /**
     * 签约主体
     * 如：网约车：用车南京同游天下（1001）
     * 顺风车：用车嘉导（1003）
     * */
    private ReceiveChannel                         receiveChannel;

    /**
     * 是否用户选择过：1：选择过，0：未选择过
     * <p>
     *     该字段用于特殊场景，例如：静默追加车型场景用户是无感知的，在车型追加列表用户仍然可以选择该车型
     */
    private Integer                                userChosen;

    /**
     * 最晚确认时间，yyyy-MM-dd HH:mm:ss
     * <p>
     *     静默追加车型才有值
     */
    private String                                 gmtLastConfirm;

    /** 网约车有、顺风车无 */
    private IconInfoExtVO                          iconInfo;

    /** 询价标识 */
    private String                                 priceMark;

    /** 当前车型座位数 */
    private int                                    seats;

    /** 司机预计达到上车点时间（秒） */
    private int                                    driverArrTime;
    /** 司机距离上车点距离（米）*/
    private double                                 driverArrDistance;
    // =============== 包装资源信息（车次冗余） ===============
    /** 包装车次唯一编号*/
    private String                                 packResourceId;

    /** 外显名称*/
    private String                                 showName;

    /** 包装车类型（经济型、舒适型等）此值只有网约车才会有 注意Npe */
    private CarTypeEnum                            packCarType;

    /** 对C资源报价（网约车、顺风车的外显价格）*/
    private Money                                  showSalePrice;

    /** 对供资源报价（网约车、顺风车的黑盒包装价格） */
    private Money                                  packSupplierPrice;

    /** 权益实际抵扣金额*/
    private Money                                  realRightDiscount;

    /** 是否展示服务商 -埋名 */
    private boolean                                displayMerchants;

    /**
     * 是否支持未支持派单：0 不支持，1 支持
     */
    private Integer                                supportUnpaidDispatch;

    /**
     * 等待派单时间（秒）
     */
    private Integer                                waitDispatchTime;

    /**
     * 是否网约车黑盒模式-V2
     */
    private boolean                                blackBoxV2;

    /**
     * 行程类型
     */
    private TripType                               tripType;

    // =================== 修改目的地新增 ===================
    /**
     * 总可修改上车点次数
     */
    private int                                    allCanChangeDepartureNum;

    /**
     * 总可修改途经点次数
     */
    private int                                    allCanChangeStopoverNum;

    /**
     * 总可修改目的地次数
     */
    private int                                    allCanChangeArrivalNum;

    /**
     * 搜索Category
     */
    private int                                    searchCategory;

    // =============== 权益项 ===============
    /**
     * 权益项
     * 8013-打车优享权益
     */
    private List<ResourceRightDiscountVO>          rightDiscounts;

    /**
     * 包装抵扣项
     * 1. 黑金抵扣
     */
    private List<ResourceDiscountVO>               packDiscounts;

    /** 黑盒包装 券*/
    private List<ResourceCouponVO>                 packCoupons;

    /** 包装资源费用收取类型,1-预估,2-一口价 */
    //private ChargeTypeEnum           packChargeType;
    /**
     * 包装供应商原始报价类型
     * <p>
     *     说明1：全包一口价，2：不含附加费一口价
     */
    //private SupplierChargeTypeEnum   packSupplierChargeType;

    /** 基础价格规则 */
    private ResourceBasePriceRuleVO                resourceBasePriceRule;
    /** 是否黑盒资源 */
    private boolean                                isBlackBox;

    //resource增加字段标记
    // =============== 合规(平台服务费)新增 ===============

    /**
     * 是否存在计价规则
     */
    private boolean                                hasPriceRule;

    /**
     * 供应链的费用项明细
     */
    private List<ResourceVO.PriceDetailVO>         tradePriceDetails;

    /**
     * 优惠项明细
     */
    private List<ResourceVO.PriceDetailVO>         discountDetails;

    // =============== 全能车 ===============

    /** 全能车增值费 */
    private Money                                  upgradeServiceFee;

    /** 全能车增值服务 */
    private List<ResourceSupplierUpgradeServiceVO> supplierUpgradeServices;
    // =============== 供应商附加服务 ===============
    /** 供应商附加服务 */
    private List<AdditionServerVO>                 additionServers;
    /** 最大行李数 */
    private int                                    maxLuggageCount;
    // =============== 支付 ===============
    /**
     * 后付发起支付节点,0:接单后支付,1:完单后支付
     */
    private Integer                                afterPaySendPayNode;

    // =============== 顺风车返现 ===============
    /**
     * 顺风车最大返现金额(返现二期中废弃了)
     */
    private String                                 maxReturn;

    /**
     * 活动code(返现二期中废弃了)
     */
    private String                                 promotionCode;

    // =================== 返现二期新增 begin ===================
    /**
     * 顺风车返现活动code
     */
    private String                                 configCode;
    /**
     * 顺风车返现金额
     */
    private BigDecimal                             cashBackPrice;
    // =================== 返现二期新增 end ===================

    /**
     * 包装资源code
     */
    private String                                 packResourceCode;

    /**
     * 增额（曹操）
     */
    private Money                                  donateActivityAmount;

    /**
     * 订单费用收取类型(用于支付引导的计价类型)
     * 1-预估价【为后付流程】
     * 2-一口价【合单支付】
     */
    private int                                    orderChargeType;

    /**
     * 基于权益的账单（搜索）
     */
    private SearchRightBillVO                      searchRightBill;

    /**
     * 接单时供应商名称
     */
    private String                                 receiveSupplierName;

    /**
     * 子车型  -- 品类拆分后的product子类型
     */
    private String                                 subCarModel;

    /**
     * 子产品类型名称   ---  品类拆分后
     */
    private String                                 subProductName;

    /**
     * 可能为空，询价场景code
     */
    private String                                 searchSceneCode;

    /**
     * 静默派车等待确认时长(单位：s)
     */
    private int                                    silentWaitSeconds;

    /**
     * 预估接单时间(SFC)
     */
    private Integer estimatedReceivingTime;

    /**
     * 黑盒模式下传入黑盒定价供应商
     */
    private String basePriceSupplierCode;

    /**
     * getCarType
     */
    public int getCarTypeCode() {
        return carType == null ? 0 : carType.getCode();
    }

    /**
     * getServiceType
     */
    public int getServiceTypeCode() {
        return serviceType == null ? 0 : serviceType.getCode();
    }

    /**
     * 是否是顺风车基准供应商
     */
    public boolean isSfcAdditionBase() {
        return sfcAdditionalType == null || SfcAdditionalType.BASE.equals(sfcAdditionalType);
    }

    /**
     * 是否是顺风车特惠供应商
     */
    public boolean isSfcAdditionCheap() {
        return SfcAdditionalType.CHEAP.equals(sfcAdditionalType);
    }

    /**
     * 是否是顺风车特惠供应商
     */
    public boolean isSfcAdditionCheapDispatchSuccess() {
        return isSfcAdditionCheap() && isDispatchSuccessOrSuccess();
    }

    /**
     * 是否是顺风车加价供应商
     */
    public boolean isSfcAdditionHighPrice() {
        return SfcAdditionalType.HIGH_PRICE.equals(sfcAdditionalType);
    }

    /**
     * 是否是派单追加成功
     */
    public boolean isDispatchSuccess() {
        return ResourceState.DISPATCH_SUCCESS.equals(state);
    }

    /**
     * 是否是派单追加成功
     */
    public boolean isDispatchSuccessOrSuccess() {
        return isDispatchSuccess() || isSuccess();
    }

    /**
     * 是否是待派单
     */
    public boolean isPending() {
        return ResourceState.PENDING.equals(state);
    }

    /**
     * 是否接单成功
     */
    public boolean isSuccess() {
        return ResourceState.SUCCESS.equals(state);
    }

    /**
     * 是否是待确认状态
     */
    public boolean isPendingConfirm() {
        return ResourceState.CONFIRM.equals(state);
    }

    /**
     * 是否是已取消
     */
    public boolean isCanceled() {
        return ResourceState.CANCELED.equals(state);
    }

    /**
     * 是否支持静默追加
     */
    public boolean canSilentAppend() {
        return supplierTags.contains(SupplierTag.SILENT_DISPATCH.name());
    }

    /**
     * Gets resource ext vo.
     *
     * @return the resource ext vo
     */
    public ResourceExtVO getResourceExtVO() {
        return new ExtBuilder(this).build();
    }

    /**
     * 移除顺风车券
     *
     * @param failureCoupons failureCoupons
     */
    public void removeSfcCoupons(List<String> failureCoupons) {
        coupons.removeIf(coupon -> failureCoupons.contains(coupon.getCouponNo()));
    }

    /**
     * 移除car的券
     *
     * @param failureCoupon failureCoupon
     */
    public void removeCarCoupons(CarCouponUniqueKey failureCoupon) {
        if (StringUtils.equals(supplierId, failureCoupon.getSupplier() + "_" + failureCoupon.getCarType().getCode())) {
            coupons.removeIf(coupon -> StringUtils.equals(coupon.getCouponNo(), failureCoupon.getCouponNo()));
        }
    }

    /**
     * 移除所有券
     */
    public void removeCoupons() {
        coupons = Lists.newArrayList();
    }

    /**
     * 是否是网约车资源
     *
     * @return the boolean
     */
    public boolean isCar() {
        return ResourceType.CAR.equals(resourceType);
    }

    /**
     * 是否是顺风车资源
     *
     * @return the boolean
     */
    public boolean isSfc() {
        return ResourceType.SFC.equals(resourceType);
    }

    /**
     * 是否是静默追加车型
     *
     * @return the boolean
     */
    public boolean isSilentAppend() {
        return chooseType == CarChooseType.SYSTEM_APPEND_CHOOSE;
    }

    /**
     * 计算Resource下coupons和rightDiscounts的总抵扣金额 -- 负值
     *
     * @param resource
     * @return
     */
    public static BigDecimal calculateTotalDiscount(ResourceVO resource) {
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal couponAmount = BigDecimal.ZERO;
        BigDecimal rightsAmount = BigDecimal.ZERO;

        if (CollectionUtils.isNotEmpty(resource.getDiscountDetails())) {
            List<PriceDetailVO> discountDetails = resource.getDiscountDetails();
            // 1.券未剔除
            if (CollectionUtils.isNotEmpty(resource.getCoupons())) {
                couponAmount = discountDetails
                        .stream()
                        .filter(discount -> DiscountCodeEnum.COUPON_DISCOUNT.getCode().equalsIgnoreCase(discount.getChargeCode()))
                        .map(discount -> new BigDecimal(discount.getAmount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                total = total.add(couponAmount);
            }

            // 2.权益占用成功
            if (CollectionUtils.isNotEmpty(resource.getRightDiscounts()) &&
                    resource.getRightDiscounts().stream()
                            .anyMatch(discount -> discount.getRightsStatus() == RightsItemStatusEnum.OCCUPY.getCode())) {
                rightsAmount = discountDetails
                        .stream()
                        .filter(discount -> DiscountCodeEnum.RIGHT_DISCOUNT.getCode().equalsIgnoreCase(discount.getChargeCode()))
                        .map(discount -> new BigDecimal(discount.getAmount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                total = total.add(rightsAmount);
            }
        }


        if (EnvUtils.offline()) {
            log.info("[ResourceVO][calculateTotalDiscount] total discounts price:{}, coupon price:{}," +
                            " rights price:{} supplierCode:{}, carType:{}, supplierId:{}",
                    total.toPlainString(), couponAmount.toPlainString(), rightsAmount.toPlainString(),
                    resource.getSupplierCode(), resource.getCarTypeCode(), resource.getSupplierId());
        }
        return total;
    }

    /**
     * 计算 权益抵扣金额  负数
     * @param resource
     * @return
     */
    public static BigDecimal calculateRightsAmount(ResourceVO resource) {
        BigDecimal rightsAmount = BigDecimal.ZERO;

        if (CollectionUtils.isNotEmpty(resource.getDiscountDetails()) &&
                CollectionUtils.isNotEmpty(resource.getRightDiscounts()) &&
                resource.getRightDiscounts().stream()
                        .anyMatch(discount -> discount.getRightsStatus() == RightsItemStatusEnum.OCCUPY.getCode())) {

            rightsAmount = resource.getDiscountDetails()
                    .stream()
                    .filter(discount -> DiscountCodeEnum.RIGHT_DISCOUNT.getCode().equalsIgnoreCase(discount.getChargeCode()))
                    .map(discount -> new BigDecimal(discount.getAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        if (EnvUtils.offline()) {
            log.info("[ResourceVO][calculateRightDiscount] rights price:{} supplierCode:{}, carType:{}, supplierId:{}",
                    rightsAmount.toPlainString(), resource.getSupplierCode(), resource.getCarTypeCode(), resource.getSupplierId());
        }
        return rightsAmount;
    }


    /**
     * 扣除抵扣项后的销售价
     */
    public Money getSalePriceWithDiscount() {
        Money totalDiscount = getTotalDiscount();
        Money total = new Money();
        //黑盒资源，且展示服务商，使用黑盒包装价格
        if (isBlackBox && !isCarBlackBox()) {
            total = showSalePrice.add(totalDiscount);
            LoggerUtils.info(log, "[getSalePriceWithDiscount]：resourceId:{} packSalePrice={},totalDiscount={}", resourceId, showSalePrice, totalDiscount);
        } else {
            total = salePrice.add(totalDiscount);
        }
        Money minPrice = new Money(1);
        if (StringUtils.isNotEmpty(getSearchSceneCode()) && StringUtils.equalsIgnoreCase(getSearchSceneCode(),OrderTag.BBM_TOGETHER_BOOKING_CROSS.name())){
            minPrice = new Money(0);
        }
        return total.compareTo(new Money(0)) <= 0 ? minPrice : total;
    }

    /**
     * 扣除抵扣项后的销售价（最小价）
     */
    public Money calcSalePriceWithDiscount(OrderVO order, Money minPrice) {
        Money totalDiscount = getTotalDiscount();
        Money total = new Money();
        //黑盒资源，且展示服务商，使用黑盒包装价格
        if (isBlackBox && !isCarBlackBox()) {
            total = showSalePrice.add(totalDiscount);
            LoggerUtils.info(log, "[getSalePriceWithDiscount]：resourceId:{} packSalePrice={},totalDiscount={}", resourceId, showSalePrice, totalDiscount);
        } else {
            total = salePrice.add(totalDiscount);
        }
        return total.compareTo(new Money(0)) <= 0 ? minPrice : total;
    }

    /**
     * 获取总抵扣金额
     */
    public Money getTotalDiscount() {
        Money totalDiscount = new Money(0);
        if (coupons != null) {
            for (ResourceCouponVO coupon : coupons) {
                totalDiscount = totalDiscount.add(coupon.getAmount());
            }
        }
        if (discounts != null) {
            for (ResourceDiscountVO discount : discounts) {
                totalDiscount = totalDiscount.add(discount.getAmount());
            }
        }

        try {
            //权益抵扣 -只有经济型才抵扣
            if (CollectionUtils.isNotEmpty(rightDiscounts) && Objects.equals(carType, CarTypeEnum.ECONOMY)) {
                LoggerUtils.info(log, "扣除抵扣项：权益={}", JSONObject.toJSONString(rightDiscounts));
                for (ResourceRightDiscountVO rightDiscountVO : rightDiscounts) {
                    totalDiscount = totalDiscount.add(rightDiscountVO.getRightsDiscount());
                }
            }
//            LoggerUtils.info(log, "扣除抵扣项：{}", totalDiscount);
        } catch (Exception e) {
            LoggerUtils.warn(log, "扣除抵扣项异常：", e);
        }
        return totalDiscount;
    }

    /**
     * 完单前 取权益总金额
     * @return
     */
    public Money getRightsDiscountMoney() {
        Money totalDiscount = new Money();
        if (CollectionUtils.isEmpty(rightDiscounts)) {
            return totalDiscount;
        }
        for (ResourceRightDiscountVO rightDiscountVO : rightDiscounts) {
            totalDiscount = totalDiscount.add(rightDiscountVO.getRightsDiscount());
        }
        return totalDiscount;
    }

    /**
     * 获取限时优惠券价格
     */
    public Money getFlashSaleCouponPrice() {
        if (coupons == null) {
            return new Money(0);
        }
        return coupons.stream().filter(ResourceCouponVO::isFlashSale).map(ResourceCouponVO::getAmount).reduce(new Money(0), Money::add);
    }

    /**
     * 资源对应的券信息
     */
    @Data
    public static class ResourceCouponVO implements Serializable {
        /** 券名称 */
        private String  name;
        /** 券编号 */
        private String  couponNo;
        /** 批次号 */
        private String  batchNo;
        /** 券价格 负数 */
        private Money   amount;
        /** 券类型 */
        private String  couponType;
        /** 是否是限时优惠 */
        private boolean flashSale;
        /**
         * 虚收违约金 注意可能为null/blank
         */
        private String  virtualPenaltyAmount;
        /*
         * 券发放类型
         * 参见 CouponIssueType https://toca.17u.cn/wiki?fid=5af521fe56654d4a804a0cd76aa1c0bc
         * 0:未知(历史数据是0) 1:普通券(即非预发券) 2:预发券
         */
        private int     issueType;
        /**
         * 券的实际抵扣(负数), 类似于item表的realPrice;
         * 仅当有总的虚收违约金且票的实付小于0才有值;建议使用getRealPriceYuanWithDefault()来获取;
         * 券的realPrice取值的正确顺序: item表券的realPrice>resource表券的realPrice>resource表券的amount
         */
        private String  realPrice;
        /**
         * 状态 仅当DefaultPayCancelProcessor中做了该券的使用/退还时才有值
         * https://toca.17u.cn/wiki?fid=deae0295a18b4609b0ac71ccb75113fc
         */
        private String  goodsState;

        /**
         * 获取商品售价的绝对值
         */
        public BigDecimal getAbsAmountYuan() {
            return getAmount().getAmount().abs();
        }

        /**
         * 获取realPrice的绝对值
         */
        public BigDecimal getAbsRealPriceYuan() {
            return getRealPriceYuanWithDefault().getAmount().abs();
        }

        /**
         * 获取商品实际抵扣金额,默认值取amount
         */
        public Money getRealPriceYuanWithDefault() {
            if (StringUtils.isBlank(realPrice)) {
                return amount;
            }
            return new Money(realPrice);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            ResourceCouponVO that = (ResourceCouponVO) o;
            return Objects.equals(couponNo, that.couponNo);
        }

        @Override
        public int hashCode() {
            return Objects.hash(couponNo);
        }
    }

    /**
     * 费用项详情-搜索
     */
    @Getter
    @Setter
    public static class PriceDetailVO implements Serializable {

        /**
         * 费用项金额
         */
        private String amount;
        /**
         * 费用项code
         */
        private String chargeCode;
        /**
         * 费用项描述
         */
        private String chargeDesc;

    }

    /**
     * The type Resource discount vo.
     */
    @Data
    public static class ResourceDiscountVO implements Serializable {
        /** 抵扣项名称 */
        private String       name;
        /** 抵扣项金额 */
        private Money        amount;
        /** 抵扣项类型 */
        private DiscountType type;
    }

    /**
     * 全能车增值服务信息
     */
    @Data
    public static class ResourceSupplierUpgradeServiceVO implements Serializable {
        /**
         * 增值服务Code：WAIT_SERVICE等待服务; RIDE_SERVICE举牌等待; WATER_SERVICE乘车送水
         */
        private String                            code;
        /**
         * 增值服务描述
         */
        private String                            desc;
        /**
         * 增值服务费用
         */
        private Money                             fee;

        /**
         * 增值服务信息
         */
        List<ResourceSupplierUpgradeServiceFeeVO> upgradeServiceFees;
    }

    @Data
    public static class ResourceSupplierUpgradeServiceFeeVO {
        /**
         * 增值服务费用类型 0-独享价 1-一人价 2-二人价 3-三人价 取对应人数
         */
        private Integer feeType;
        /**
         * 增值服务费用，对应人数的增值服务的金额，元
         */
        private Money   totalFee;
    }

    @Data
    public static class ResourceRightDiscountVO implements Serializable {

        /**
         * 9803打车优享卡 ，8013打车优享权益
         */
        private String              categoryType;
        /**
         * 分类名称
         */
        private String              categoryName;
        /** 权益编码 */
        private String              skuCode;
        /** 权益名称 */
        private String              skuName;
        /** 权益销售价 */
        private Money               skuSalePrice;
        /** 权益抵扣金额（搜索） */
        private Money               rightsDiscount;
        /** 权益价值金额 */
        private Money               rightPrice;
        /** 权益类型 */
        private RightDiscountType   type;
        /**
         * 产品，权益data，使用权益的时候，需要此data下单
         */
        private String              data;
        /**
         * 里程限制
         */
        private String              limitMileage;
        /**
         * 产品图文
         */
        private Map<String, Object> configTextList;

        /** 实际权益抵扣金额（行程结束） */
        private Money               realRightsDiscount;

        /**
         * 权益编码
         */
        private String              rightCode;

        /**
         * 占用抵扣面额
         */
        private Money               occupyDiscount;

        /**
         * 权益状态
         */
        private int                 rightsStatus;

        /**
         * 超时长数（分钟）
         */
        private String              exceedDuration;
        /**
         * 超出时长金额
         */
        private Money               exceedTotalDuration;
        /**
         * 超过里程数
         */
        private String              exceedMileage;
        /**
         * 超出里程数金额
         */
        private Money               exceedTotalExceedMileage;
        /**
         * 抵扣金额
         */
        private Money               totalDeduction;

        /**
         * 超抵扣金额  --- 超优惠上限费用
         */
        private Money exceedDeduction = new Money(0);

        /**
         * 需要购买权益
         */
        private boolean             needBuy;

        /************************* 需购买权益字 独有字段 *****************************/

        /**
         * 商品扩展信息
         */
        private Map<String, Object> extendInfo;

        /**
         * 货架场景值
         */
        private String              sceneCode;
        /**
         * 子类别类型，保险才有（2:意外险；24:意外险赠险；6:旅行险；35:旅行险赠险；4:退票险；36:特惠退票险）
         */
        private Integer             subCategoryType;
        /**
         * 子类别名称
         */
        private String              subCategoryName;
        /**
         * 市场价格
         */
        private BigDecimal          marketPrice;
        /**
         * 订单购买形式（0：一单一个;1：一单多个;2：一人一个;3：一人多个）
         */
        private Integer             orderForm;
        /**
         * 销售单位
         */
        private String              saleUnit;
        /**
         * SPU编码
         */
        private String              spuCode;
        /**
         * 辅营对应id
         */
        private String              itemId;
        /**
         * 产品类别展示售卖单位（例：份，个，人）
         */
        private String              unit;
        /**
         * 里程单价
         */
        private BigDecimal          mileageUnitPrice;

    }

    /**
     * 基础报价规则
     */
    @Data
    public static class ResourceBasePriceRuleVO implements Serializable {
        /**
         * 预估距离 ,单位米
         */
        private int        distance;
        /**
         * 预估时间 ,单位秒
         */
        private int        duration;
        /** 里程单价 ,单位元/KM 可能为空*/
        private Money      kmPrice;
        /** 时间单价 ,单位元/min 可能为空 */
        private Money      timePrice;
        /** 起步公里（km） */
        private BigDecimal startIncludeKm;
        /** 起步时长（分钟） */
        private BigDecimal startIncludeMinute;
    }

    /**
     * 资源信息扩展构建器
     */
    public static class ExtBuilder {
        final ResourceVO resource;

        public ExtBuilder(ResourceVO resource) {
            this.resource = resource;
        }

        public ResourceExtVO build() {
            ResourceExtVO extVO = new ResourceExtVO();
            extVO.setPassengerVirtualPhone(resource.getPassengerVirtualPhone());
            extVO.setPronounVirtualLinkPhone(resource.getPronounVirtualLinkPhone());
            extVO.setRunTime(resource.getRunTime());
            extVO.setRunDistance(resource.getRunDistance());
            extVO.setSupplierTags(resource.getSupplierTags());
            extVO.setChargeType(resource.getChargeType().getCode());
            extVO.setSupplierChargeType(resource.getSupplierChargeType().getCode());
            extVO.setCar(getResourceCarExtVO());
            extVO.setDriver(getResourceDriverExtVO());
            extVO.setCoupons(getResourceCouponExtVO());
            extVO.setDiscounts(getResourceDiscountExtVO());
            extVO.setInterlinkOrder(resource.isInterlinkOrder());
            extVO.setInterlinkOrderInfo(resource.getInterlinkOrderInfo());
            extVO.setReceiveChannel(resource.getReceiveChannel().getReceiveChannel());
            extVO.setUserChosen(resource.getUserChosen());
            extVO.setGmtLastConfirm(resource.getGmtLastConfirm());
            extVO.setMerchantOrderNo(resource.getMerchantOrderNo());
            extVO.setTcInvoicingCode(resource.getTcInvoicingCode());
            extVO.setTcInvoicingName(resource.getTcInvoicingName());
            extVO.setSupportInvoice(resource.isSupportInvoice());
            extVO.setSfcAdditionalType(Objects.nonNull(resource.getSfcAdditionalType()) ? resource.getSfcAdditionalType().getCode() : SfcAdditionalType.BASE.getCode());
            extVO.setSfcAdditionalDiffMoney(resource.getSfcAdditionalDiffMoney());
            extVO.setServiceType(Objects.nonNull(resource.getServiceType()) ? resource.getServiceType().getCode() : ServiceType.EXCLUSIVE.getCode());
            extVO.setPriceMark(resource.getPriceMark());
            extVO.setIconInfo(resource.getIconInfo());
            extVO.setSeats(resource.getSeats());
            extVO.setBlackBox(resource.isBlackBox());
            extVO.setDisplayMerchants(resource.displayMerchants);
            // 是否支持未支持派单：0 不支持，1 支持
            extVO.setSupportUnpaidDispatch(resource.getSupportUnpaidDispatch());
            //等待派单时间(秒)
            extVO.setWaitDispatchTime(resource.waitDispatchTime);
            extVO.setBlackBoxV2(resource.isBlackBoxV2());
            extVO.setTripType(TripType.getCodeByEnum(resource.getTripType()));
            //修改目的地新增
            extVO.setAllCanChangeDepartureNum(resource.getAllCanChangeDepartureNum());
            extVO.setAllCanChangeStopoverNum(resource.getAllCanChangeStopoverNum());
            extVO.setAllCanChangeArrivalNum(resource.getAllCanChangeArrivalNum());
            extVO.setSearchCategory(resource.getSearchCategory());
            //resource增加字段标记
            //合规(平台服务费)新增
            extVO.setHasPriceRule(resource.hasPriceRule);
            extVO.setTradePriceDetails(resource.tradePriceDetails);
            extVO.setDiscountDetails(resource.discountDetails);
            extVO.setRightDiscounts(getResourceRightDiscountExtVO());
            extVO.setBasePriceRule(getBasePriceRuleExtVO());
            extVO.setCashBackInfoExt(getBaseCashBackExtVO());
            if (Objects.nonNull(resource.getDonateActivityAmount())) {
                extVO.setDonateActivityAmount(resource.getDonateActivityAmount().getAmount().toPlainString());
            } else {
                extVO.setDonateActivityAmount(new Money().getAmount().toPlainString());
            }
            extVO.setSearchRightBill(resource.getSearchRightBill());
            extVO.setReceiveSupplierName(resource.getReceiveSupplierName());
            extVO.setSupplierNameToClient(resource.getSupplierNameToClient());
            extVO.setFinalPrice(resource.getFinalPrice() == null ? BigDecimal.ZERO : resource.getFinalPrice().getAmount());
            extVO.setSubProductName(resource.getSubProductName());
            extVO.setSubCarModel(resource.getSubCarModel());
            extVO.setSilentWaitSeconds(resource.getSilentWaitSeconds());
            extVO.setSearchSceneCode(resource.getSearchSceneCode());
            extVO.setEstimatedReceivingTime(resource.getEstimatedReceivingTime());
            extVO.setBasePriceSupplierCode(resource.getBasePriceSupplierCode());
            return extVO;
        }

        /**
         * Gets resource discount ext vo.
         *
         * @return the resource discount ext vo
         */
        private List<ResourceExtVO.ResourceDiscountExt> getResourceDiscountExtVO() {
            List<ResourceExtVO.ResourceDiscountExt> discountExtList = Lists.newArrayList();
            if (resource.getDiscounts() == null) {
                return discountExtList;
            }
            for (ResourceDiscountVO discount : resource.getDiscounts()) {
                ResourceExtVO.ResourceDiscountExt discountExt = new ResourceExtVO.ResourceDiscountExt();
                discountExt.setName(discount.getName());
                discountExt.setAmount(discount.getAmount().getAmount().toPlainString());
                discountExt.setType(discount.getType().name());
                discountExtList.add(discountExt);
            }
            return discountExtList;
        }

        /**
         * Gets resource coupon ext vo.
         */
        public List<ResourceExtVO.ResourceCouponExt> getResourceCouponExtVO() {
            List<ResourceExtVO.ResourceCouponExt> couponExtList = Lists.newArrayList();
            if (resource.getCoupons() == null) {
                return couponExtList;
            }
            for (ResourceCouponVO coupon : resource.getCoupons()) {
                ResourceExtVO.ResourceCouponExt couponExt = new ResourceExtVO.ResourceCouponExt();
                couponExt.setCouponNo(coupon.getCouponNo());
                couponExt.setIssueType(coupon.getIssueType());
                couponExt.setGoodsState(coupon.getGoodsState());
                couponExt.setRealPrice(coupon.getRealPrice());
                couponExt.setVirtualPenaltyAmount(coupon.getVirtualPenaltyAmount());
                couponExt.setAmount(coupon.getAmount().getAmount().toPlainString());
                couponExt.setName(coupon.getName());
                couponExt.setBatchNo(coupon.getBatchNo());
                couponExt.setCouponType(coupon.getCouponType());
                couponExt.setFlashSale(coupon.isFlashSale());
                couponExtList.add(couponExt);
            }
            return couponExtList;
        }

        /**
         * Gets resource driver ext vo.
         */
        public ResourceExtVO.ResourceDriverExt getResourceDriverExtVO() {
            ResourceExtVO.ResourceDriverExt driverExt = new ResourceExtVO.ResourceDriverExt();
            driverExt.setDriverCode(resource.getDriverCode());
            driverExt.setDriverName(resource.getDriverName());
            driverExt.setDriverPhone(resource.getDriverPhone());
            driverExt.setDriverVirtualPhone(resource.getDriverVirtualPhone());
            driverExt.setLevel(resource.getLevel());
            driverExt.setWechat(resource.getWechat());
            driverExt.setDriverChatCode(resource.getDriverChatCode());
            driverExt.setDriverArrTime(resource.getDriverArrTime());
            driverExt.setDriverArrDistance(resource.getDriverArrDistance());
            return driverExt;
        }

        /**
         * Gets resource car ext vo.
         */
        public ResourceExtVO.ResourceCarExt getResourceCarExtVO() {
            ResourceExtVO.ResourceCarExt carExt = new ResourceExtVO.ResourceCarExt();
            carExt.setCarNum(resource.getCarNum());
            carExt.setOriginalCarNum(resource.getOriginalCarNum());
            carExt.setBrand(resource.getBrand());
            carExt.setCarType(resource.getCarType() == null ? CarTypeEnum.ECONOMY.getCode() : resource.getCarType().getCode());
            carExt.setColor(resource.getColor());
            carExt.setCarTags(resource.getCarTags());
            carExt.setVehicleCode(resource.getVehicleCode());
            return carExt;
        }

        /**
         * Gets resource rightDiscount ext vo.
         *
         * @return the resource rightDiscount ext vo
         */
        private List<ResourceExtVO.ResourceRightDiscountExt> getResourceRightDiscountExtVO() {
            List<ResourceExtVO.ResourceRightDiscountExt> rightDiscountExtList = Lists.newArrayList();
            if (CollectionUtils.isEmpty(resource.getRightDiscounts())) {
                return rightDiscountExtList;
            }

            for (ResourceRightDiscountVO discount : resource.getRightDiscounts()) {

                ResourceExtVO.ResourceRightDiscountExt rightDiscountExt = new ResourceExtVO.ResourceRightDiscountExt();

                // 搜索给出的券金额为正值，券金额处理为负数
                rightDiscountExt.setCategoryType(discount.getCategoryType());
                rightDiscountExt.setCategoryName(discount.getCategoryName());

                rightDiscountExt.setSkuCode(discount.getSkuCode());
                rightDiscountExt.setSkuName(discount.getSkuName());
                rightDiscountExt.setSkuSalePrice(discount.getSkuSalePrice().getAmount().toPlainString());
                rightDiscountExt.setRightsDiscount(discount.getRightsDiscount().getAmount().toPlainString());
                rightDiscountExt.setRightPrice(discount.getRightPrice().getAmount().toPlainString());
                rightDiscountExt.setData(discount.getData());
                rightDiscountExt.setConfigTextList(discount.getConfigTextList());
                rightDiscountExt.setLimitMileage(discount.getLimitMileage());

                rightDiscountExt.setRightCode(discount.getRightCode());
                rightDiscountExt.setOccupyDiscount(discount.getOccupyDiscount());
                rightDiscountExt.setRealRightsDiscount(discount.getRealRightsDiscount());
                rightDiscountExt.setRightsStatus(discount.getRightsStatus());
                rightDiscountExt.setExceedDuration(discount.getExceedDuration());
                rightDiscountExt.setExceedDeduction(discount.getExceedDeduction());
                rightDiscountExt.setExceedTotalDuration(discount.getExceedTotalDuration());
                rightDiscountExt.setExceedMileage(discount.getExceedMileage());
                rightDiscountExt.setExceedTotalExceedMileage(discount.getExceedTotalExceedMileage());
                rightDiscountExt.setTotalDeduction(discount.getTotalDeduction());

                //需购买权益字 独有字段
                rightDiscountExt.setNeedBuy(true);
                rightDiscountExt.setExtendInfo(discount.getExtendInfo());
                rightDiscountExt.setSceneCode(discount.getSceneCode());
                rightDiscountExt.setSubCategoryType(discount.getSubCategoryType());
                rightDiscountExt.setSubCategoryName(discount.getSubCategoryName());
                rightDiscountExt.setMarketPrice(discount.getMarketPrice());
                rightDiscountExt.setOrderForm(discount.getOrderForm());
                rightDiscountExt.setSaleUnit(discount.getSaleUnit());
                rightDiscountExt.setSpuCode(discount.getSpuCode());
                rightDiscountExt.setItemId(discount.getItemId());
                rightDiscountExt.setUnit(discount.getUnit());
                rightDiscountExt.setMileageUnitPrice(discount.getMileageUnitPrice());

                rightDiscountExtList.add(rightDiscountExt);
            }
            return rightDiscountExtList;
        }

        /**
         * Gets resource BasePriceRule ext vo.
         * @return the resource BasePriceRule ext vo
         */
        public ResourceExtVO.BasePriceRuleExt getBasePriceRuleExtVO() {
            ResourceExtVO.BasePriceRuleExt basePriceRuleExt = new ResourceExtVO.BasePriceRuleExt();
            ResourceBasePriceRuleVO basePriceRule = resource.getResourceBasePriceRule();
            if (basePriceRule == null || Objects.isNull(basePriceRule.getKmPrice())) {
                return basePriceRuleExt;
            }
            basePriceRuleExt.setKmPrice(basePriceRule.getKmPrice().getAmount().toPlainString());
            basePriceRuleExt.setTimePrice(basePriceRule.getTimePrice().getAmount().toPlainString());
            basePriceRuleExt.setStartIncludeKm(basePriceRule.getStartIncludeKm().toPlainString());
            basePriceRuleExt.setStartIncludeMinute(basePriceRule.getStartIncludeMinute().toPlainString());
            basePriceRuleExt.setDistance(basePriceRule.getDistance());
            basePriceRuleExt.setDuration(basePriceRule.getDuration());
            return basePriceRuleExt;
        }

        /**
         * Gets resource getBaseCashBackExtVO ext vo.
         * @return the resource BasePriceRule ext vo
         */
        public ResourceExtVO.CashBackInfoExt getBaseCashBackExtVO() {
            ResourceExtVO.CashBackInfoExt cashBackInfoExt = new ResourceExtVO.CashBackInfoExt();
            cashBackInfoExt.setPromotionCode(resource.getPromotionCode());
            cashBackInfoExt.setMaxReturn(resource.getMaxReturn());
            cashBackInfoExt.setConfigCode(resource.getConfigCode());
            cashBackInfoExt.setCashBackPrice(resource.getCashBackPrice());
            return cashBackInfoExt;
        }

    }

    /**
     * 是否网约车埋名
     */
    public boolean isCarBlackBox() {
        return Objects.equals(TripType.CAR_BLACK_BOX, tripType);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchRightBillVO implements Serializable {
        /**
         * 权益部分
         */
        private SearchRightBillFeeDTO   rightFee;
        /**
         * 非权益部分
         */
        private SearchNoRightBillFeeDTO noRightFee;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchRightBillFeeDTO implements Serializable {
        /**
         * 抵扣权益名称，类似于5公里以内
         */
        private String name;
        /**
         * 权益抵扣原价，元
         */
        private String originalAmount;
        /**
         * 减免金额，元
         */
        private String reduceAmount;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchNoRightBillFeeDTO implements Serializable {
        /**
         * 未参与权益名称，类似于5公里以上
         */
        private String                          name;
        /**
         * 非权益总价，元
         */
        private String                          originalAmount;
        /**
         * 减免金额，元
         */
        private List<SearchRightBillFeeItemDTO> feeItems;
    }

    /**
     *
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchRightBillFeeItemDTO implements Serializable {
        private String code;
        /**
         * 服务项名称
         */
        private String name;
        private String amount;
    }

    /**
     * 销售价（券前价格）
     */
    public Money getSalePriceBeforeDiscount() {
        //黑盒资源，且展示服务商，使用黑盒包装价格
        if (isBlackBox && !isCarBlackBox()) {
            return showSalePrice;
        } else {
            return salePrice;
        }
    }
}
