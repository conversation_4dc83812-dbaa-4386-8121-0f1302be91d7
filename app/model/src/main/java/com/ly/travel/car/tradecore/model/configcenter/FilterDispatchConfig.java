package com.ly.travel.car.tradecore.model.configcenter;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class FilterDispatchConfig {

    /**
     * 订单类型
     */
    private List<Integer> orderTypeList;
    /**
     * 包含, 还是不包含
     */
    private boolean       orderTypeContains;
    /**
     * 城市编码列表
     */
    private List<String>  cityCodeList;
    /**
     * 包含, 还是不包含
     */
    private boolean       cityCodeContains;
    /**
     * 订单标签列表
     */
    private List<String>  orderTags;
    /**
     * 包含, 还是不包含
     */
    private boolean       orderTagsContains;

    /**
     * 订单渠道列表
     */
    private List<Integer> orderChannelList;
    /**
     * 包含, 还是不包含
     */
    private boolean       orderChannelContains;

    /**
     * 供应商编码列表
     */
    private List<String>  supplierCodeList;
}
