package com.ly.travel.car.tradecore.model.trip;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import com.ly.travel.car.common.model.enums.CarTypeEnum;

/**
 * 根据供应商、券号、价格构建Key必定唯一
 * 
 * <AUTHOR>
 * @version Id: CarCouponUniqueKey, v 0.1 2024/3/7 17:39 icanci Exp $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class CarCouponUniqueKey {
    /** 车类型 */
    private CarTypeEnum carType;
    /** 供应链 */
    private String      supplier;
    /** 券号 */
    private String      couponNo;
}