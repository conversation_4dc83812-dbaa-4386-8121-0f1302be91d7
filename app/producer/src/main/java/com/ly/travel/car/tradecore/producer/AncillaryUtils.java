package com.ly.travel.car.tradecore.producer;

import com.ly.travel.car.common.model.enums.CarTypeEnum;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.trip.ResourceVO;

/**
 * <AUTHOR>
 * @version Id: AncillaryUtils, v 0.1 2024/3/27 18:21 icanci Exp $
 */
public class AncillaryUtils {

    /**
     * 获取座位数
     *
     * @param order order
     * @param resource resource
     * @return Integer
     */
    public static Integer getSeats(OrderVO order, ResourceVO resource) {
        if (resource == null) {
            return 7;
        }

        if (order.isSfc()) {
            return 5;
        }
        if (order.isCar()) {
            int seats = resource.getSeats();
            if (seats == 0) {
                CarTypeEnum carType = resource.getCarType();
                if (carType == null || CarTypeEnum.LUXURY == carType) {
                    return 7;
                } else {
                    return 5;
                }
            } else {
                return seats;
            }
        }
        return 7;
    }
}