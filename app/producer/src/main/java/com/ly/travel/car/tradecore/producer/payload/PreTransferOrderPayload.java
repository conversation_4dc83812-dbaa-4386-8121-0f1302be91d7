package com.ly.travel.car.tradecore.producer.payload;

import com.ly.travel.car.tradecore.model.enums.PreOrderResourceType;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 预约转单MQ消息payload
 * @Author: jay.he
 * @Date: 2024-08-07 11:08
 * @Version: 1.0
 **/
@Data
@Builder
public class PreTransferOrderPayload implements Serializable {

    /**
     * traceId
     */
    private String traceId;

    /**
     * 预约订单流水号
     */
    private String orderSerialNo;

    /**
     * 延迟场景
     */
    private PreOrderResourceType resourceType;

    /**
     * 延迟时间
     */
    private long delay;
}
