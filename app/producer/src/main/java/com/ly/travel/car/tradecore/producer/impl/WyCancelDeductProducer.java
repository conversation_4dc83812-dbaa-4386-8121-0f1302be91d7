package com.ly.travel.car.tradecore.producer.impl;

import com.ly.travel.car.tradecore.producer.AbstractTradeProducer;
import com.ly.travel.car.tradecore.producer.MQBizTypeEnum;
import com.ly.travel.car.tradecore.producer.Mq;
import com.ly.travel.car.tradecore.producer.PayloadWrapper;
import com.ly.travel.car.tradecore.producer.source.WyCancelDeductResource;
import org.springframework.stereotype.Service;

/**
 * 取消无忧抵扣完成生产者
 * - 抵扣完成的场景：1.取消时违约金代扣 2.预付款取消发生违约金，发起退差
 * <AUTHOR>
 * @version Id: WyCancelDeductProducer, v 0.1 2024/3/8 13:53 wuyayuan Exp $
 */
@Service("wyCancelDeductProducer")
public class WyCancelDeductProducer extends AbstractTradeProducer<WyCancelDeductResource> {
    @Override
    protected PayloadWrapper getPayload(WyCancelDeductResource originalSource) {
        return new PayloadWrapper(originalSource.getPayload());
    }

    @Override
    protected String getTopic(WyCancelDeductResource originalSource) {
        return getPropertyValue(Mq.Topic.ANCILLARY_WY_CANCEL_DEDUCT_TOPIC);
    }

    @Override
    protected String getTag(WyCancelDeductResource originalSource) {
        return Mq.Tag.Ancillary.WY_CANCEL_DEDUCT;
    }

    @Override
    protected MQBizTypeEnum getBizType() {
        return MQBizTypeEnum.B20;
    }
}
