package com.ly.travel.car.tradecore.producer.payload;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 火车票保底票请求体
 * @version Id: GuaranteeTrainPayload  2024/12/2
 */
@Data
public class GuaranteeTrainPayload implements Serializable {

    /** 火车票订单号 */
    private String orderSerialNo;

    /** 保底订单号*/
    private String sourceSerialNo;

    /**
     *  消息通知场景：
     *
     * unpaid 抢票创单
     *
     * issue 出票成功

     */
    private String tag;

    /**
     *  保底场景标识：FREE_RIDE_GUARANTEE_TRAIN

     */
    private String guaranteeTag;

}
