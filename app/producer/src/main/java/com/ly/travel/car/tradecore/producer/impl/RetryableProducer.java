package com.ly.travel.car.tradecore.producer.impl;

import static com.ly.sof.utils.log.LogContextUtils.TRACE_ID;

import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.annotation.Resource;

import com.ly.travel.car.tradecore.producer.payload.RetryableMessage;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.SerializeEnum;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.producer.Producer;
import com.ly.sof.api.mq.producer.UniformEventPublisher;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.thread.NamedThreadFactory;

import lombok.extern.slf4j.Slf4j;

/**
 * 可重试MQ Producer
 *
 * <AUTHOR>
 * @version Id: RetryableProducer, v 0.1 2024/3/5 10:28 zj Exp $
 */
@Slf4j
@Component
public class RetryableProducer implements InitializingBean {

    /**
     * 最大重试次数
     */
    private static final int                             MAX_RETRY       = 5;
    /**
     * 默认超时时间
     */
    private static final int                             DEFAULT_TIMEOUT = 5000;

    /**
     * 本地重试发送队列
     */
    private static final BlockingQueue<RetryableMessage> RETRY_QUEUE     = new ArrayBlockingQueue<>(10000);

    /**
     * 重试调度线程
     */
    private static final ScheduledExecutorService        RETRY_EXECUTOR  = Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory("retry-producer-task-"));

    private final AtomicBoolean                          retryStared     = new AtomicBoolean(false);

    @Resource
    private Producer                                     producer;

    @Resource
    private UniformEventPublisher                        uniformEventPublisher;

    /**
     * 可重试发送消息
     *
     * @param retryable 重试消息载体
     * @return 发送结果
     */
    public boolean send(RetryableMessage retryable) {
        try {
            String topic = retryable.getTopic();
            String tag = retryable.getTag();
            SerializeEnum serialize = retryable.getSerialize();
            retryable.putProperty(TRACE_ID, LogContextUtils.getTraceId());
            Map<String, String> properties = retryable.getProperties();
            Object payload = retryable.getPayload();
            // 固定延迟消息
            if (retryable.isDelayLevelMessage()) {
                return producer.send(topic, tag, payload, DEFAULT_TIMEOUT, retryable.getMessageDelayLevel(), properties, serialize);
            }
            // 指定延迟消息
            if (retryable.isDelayMessage()) {
                return this.sendDelayTime(retryable);
            }
            return producer.send(topic, tag, payload, DEFAULT_TIMEOUT, properties, serialize);
        } catch (MQException e) {
            log.error("MQ发送失败,[{}]", JSONObject.toJSONString(retryable), e);
            int retry = retryable.getRetry().incrementAndGet();
            if (retry >= MAX_RETRY) {
                log.error("MQ发送失败,超过最大重试次数[{}]次,[{}],", retry, JSONObject.toJSONString(retryable), e);
                return false;
            }
            // 重试投递
            boolean isRetry = RETRY_QUEUE.offer(retryable);
            if (!isRetry) {
                log.error("MQ发送失败,重试投递本地队列失败,当前重试次数[{}]次,[{}],", retry, JSONObject.toJSONString(retryable), e);
            }
            return false;
        }
    }

    /**
     * 延迟消息-指定延迟时间
     * 
     * @param retryable 重试消息载体
     * @return 是否成功
     * @throws MQException
     */
    private boolean sendDelayTime(RetryableMessage retryable) throws MQException {
        String topic = retryable.getTopic();
        String tag = retryable.getTag();
        SerializeEnum serialize = retryable.getSerialize();
        Object payload = retryable.getPayload();
        UniformEvent event = uniformEventPublisher.createUniformEvent(topic, tag, false, payload);
        event.setTimeout(DEFAULT_TIMEOUT);
        event.setSerialize(serialize);
        event.addProperties(retryable.getProperties());
        event.setDelayTime(retryable.getDelayDate());
        return uniformEventPublisher.publishUniformEvent(event);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.init();
    }

    private void init() {
        if (!retryStared.compareAndSet(false, true)) {
            return;
        }
        // 开启重试任务
        Thread retryThread = new Thread(new RetryTask());
        retryThread.setDaemon(true);
        retryThread.start();
    }

    /**
     * 重试任务
     */
    private class RetryTask implements Runnable {
        @Override
        public void run() {
            while (!RETRY_EXECUTOR.isShutdown()) {
                try {
                    RetryableMessage retryable = RETRY_QUEUE.poll(3000, TimeUnit.MILLISECONDS);
                    if (retryable == null) {
                        continue;
                    }
                    // 重试次数
                    int retry = retryable.getRetry().intValue();
                    RETRY_EXECUTOR.schedule(() -> {
                        // 重试发送
                        RetryableProducer.this.send(retryable);
                    }, retry * 200L, TimeUnit.MILLISECONDS);
                } catch (InterruptedException e) {
                    // ignore
                } catch (Exception e) {
                    log.error("重试投递MQ任务执行异常", e);
                }
            }
        }
    }
}
