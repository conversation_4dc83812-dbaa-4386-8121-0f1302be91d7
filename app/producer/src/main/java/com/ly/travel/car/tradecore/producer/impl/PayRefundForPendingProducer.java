package com.ly.travel.car.tradecore.producer.impl;

import org.springframework.stereotype.Service;

import com.ly.travel.car.tradecore.producer.AbstractTradeProducer;
import com.ly.travel.car.tradecore.producer.MQBizTypeEnum;
import com.ly.travel.car.tradecore.producer.Mq;
import com.ly.travel.car.tradecore.producer.PayloadWrapper;
import com.ly.travel.car.tradecore.producer.source.PayRefundForPendingSource;

/**
 *  补差退款的mq
 *  https://toca.17u.cn/wiki?fid=7bf2aab9294044bcb5984e2993dfbaf8
 *
 * <AUTHOR>
 * @version Id: PayRefundForPendingProducer, v 0.1 2025/02/25 23:18 zhihua.li Exp $
 */
@Service("payRefundForPendingProducer")
public class PayRefundForPendingProducer extends AbstractTradeProducer<PayRefundForPendingSource> {

    @Override
    protected PayloadWrapper getPayload(PayRefundForPendingSource originalSource) {
        return new PayloadWrapper(originalSource.getPayload());
    }

    @Override
    protected String getTopic(PayRefundForPendingSource originalSource) {
        return getPropertyValue(Mq.Topic.PAY_CLEAR);
    }

    @Override
    protected String getTag(PayRefundForPendingSource originalSource) {
        return Mq.Tag.Pay.SUB_ORDER_REFUND;
    }

    @Override
    protected MQBizTypeEnum getBizType() {
        return MQBizTypeEnum.B13;
    }
}
