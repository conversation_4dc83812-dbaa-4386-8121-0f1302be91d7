package com.ly.travel.car.tradecore.business.traveling.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.common.utils.DateUtils;
import com.ly.travel.car.tradecore.business.ctx.StandardOrderContext;
import com.ly.travel.car.tradecore.business.traveling.TravelingProcessService;
import com.ly.travel.car.tradecore.business.traveling.model.MessageType;
import com.ly.travel.car.tradecore.constant.NumberConstants;
import com.ly.travel.car.tradecore.facade.request.callback.OrderProcessCallbackRequestDTO;
import com.ly.travel.car.tradecore.facade.response.callback.OrderProcessCallbackResponseDTO;
import com.ly.travel.car.tradecore.integration.client.lbs.EuclideanDistanceRequest;
import com.ly.travel.car.tradecore.integration.client.lbs.EuclideanDistanceResponse;
import com.ly.travel.car.tradecore.integration.client.lbs.LbsClient;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.*;
import com.ly.travel.car.tradecore.model.goods.TicketVO;
import com.ly.travel.car.tradecore.model.trip.AddressVO;
import com.ly.travel.car.tradecore.order.GoodsCmdService;
import com.ly.travel.car.tradecore.order.OrderService;
import com.ly.travel.car.tradecore.service.ancillary.AncillaryService;
import com.ly.travel.car.tradecore.service.distribution.DistributionService;
import com.ly.travel.car.tradecore.service.log.LogContext;
import com.ly.travel.car.tradecore.service.log.LogExt;
import com.ly.travel.car.tradecore.service.log.LogModule;
import com.ly.travel.car.tradecore.service.log.OperateLogService;
import com.ly.travel.car.tradecore.service.push.PushService;
import com.ly.travel.car.tradecore.service.supplier.SupplierService;
import com.ly.travel.car.tradecore.service.tracer.TracerService;
import com.ly.travel.car.tradecore.utils.DataMonitorUtils;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.DriverLocationResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 行中处理服务
 *
 * <AUTHOR>
 * @version Id: TravelingProcessServiceImpl.java, v 0.1 2024-03-05 16:59 ryan Exp $$
 */
@Slf4j
@Service("travelingProcessService")
public class TravelingProcessServiceImpl implements TravelingProcessService {

    @Resource(name = "orderService")
    private OrderService orderService;

    @Resource(name = "tracerService")
    private TracerService tracerService;

    @Resource
    private AncillaryService ancillaryService;

    @Resource
    private DistributionService distributionService;

    @Resource
    protected GoodsCmdService goodsCmdService;

    @Resource
    private SupplierService supplierService;

    @Resource
    private LbsClient lbsClient;

    @Resource
    private PushService pushService;

    @Resource
    private OperateLogService operateLogService;

    /** 供应链通知用户已上车操作日志 */
    private static final String BEFORE_CONFIRM_GETON_LOG = "供应链通知用户已上车";
    /** 供应链通知行程结束操作日志 */
    private static final String BEFORE_TRIP_END_LOG      = "供应链通知行程结束";

    /**
     * Process order process callback response dto.
     *
     * @param request the request
     * @return the order process callback response dto
     */
    @Override
    public OrderProcessCallbackResponseDTO process(OrderProcessCallbackRequestDTO request) {
        try {
            // 查询订单
            OrderVO order = orderService.queryOrder(request.getOrderSerialNo());
            if (order == null) {
                return OrderProcessCallbackResponseDTO.success(request.getTraceId());
            }

            // 订单已关闭或者已取消不处理
            if (order.isClosed() || order.isCanceled()) {
                DataMonitorUtils.dataMonitorLog(order,
                        "行程中",
                        DataMonitorUtils.DataMonitorEnum.SUPPLIER_STATE_MISMATCH);
                return OrderProcessCallbackResponseDTO.success(request.getTraceId());
            }

            // 接单信息验证
            if (ticketValidate(order, request)) {
                return OrderProcessCallbackResponseDTO.success(request.getTraceId());
            }

            // 获取消息类型
            MessageType messageType = MessageType.getByCode(request.getMessageType());
            if (messageType == null) {
                return OrderProcessCallbackResponseDTO.success(request.getTraceId());
            }

            if (messageType == MessageType.DRIVER_ARRIVE_NOTICE) {
                // 处理司机已到达位置到乘客上车点距离
                handleDriverPosition(order);
            }

            // 司机到达通知 行程中的订单状态
            if (messageType == MessageType.USER_ON_CA_NOTICE) {

                // 乘客已上车 执行业务逻辑前记录日志, 目的：区分用户操作/供应链通知
                logBeforeProcess(BEFORE_CONFIRM_GETON_LOG, order);

                // 辅营正式单
                ancillaryService.book(new StandardOrderContext(order), null);
            }

            // 行程结束
            if (messageType == MessageType.TRIP_END_NOTICE) {

                // 行程结束 执行业务逻辑前记录日志, 目的：区分用户操作/供应链通知
                logBeforeProcess(BEFORE_TRIP_END_LOG, order);
            }

            // 更新订单状态
            UserState userState = calcUserState(messageType);
            OrderState orderState = calcOrderState(order, messageType);
            OrderStateSceneEnum orderStateScene = calcOrderStateScene(messageType);
            if (orderState != null && userState != null) {
                if (messageType == MessageType.DRIVER_DEPARTURE_NOTICE) {
                    orderService.updateOrderState(order, orderStateScene, userState, order.getOrderState(), orderState);
                    LoggerUtils.info(log, "[司机已出发] 走状态机场景类型新方法");
                } else {
                    orderService.updateOrderState(order, userState, order.getOrderState(), orderState);
                }
                // 司机已接单的状态不做重复推送
                if (orderState.getCode() != OrderState.RECEIVING_ORDER.getCode()) {
                    // API分销订单推送信息
                    distributionService.notifyOrderStateForApiDistribution(order, orderState, false);
                    // 页面分销通知订单状态
                    distributionService.notifyOrderStateForPageDistribution(order, orderState, false);
                }
                // 分销的用户状态通知(API分销+页面分销)
                distributionService.notifyUserState(order, userState);
            }

            // 用户推送
            UserSceneEnum userScene = calcUserScene(messageType);
            if (userScene != null && !order.isDistributionOrder()) {
                tracerService.notify(order, userScene);
            }
            
            // 推送灵动岛：USER_ON_CA_NOTICE（乘客已上车）--行程中，DRIVER_ARRIVE_NOTICE（司机已到达）,DRIVER_DEPARTURE_NOTICE(司机已出发)
            if (messageType == MessageType.DRIVER_ARRIVE_NOTICE || messageType == MessageType.USER_ON_CA_NOTICE
                || (!order.getOrderType().isTiming() && messageType == MessageType.DRIVER_DEPARTURE_NOTICE)) {
                pushService.pushIsland(order, messageType, PushIslandScene.DEFAULT);
            }

            return OrderProcessCallbackResponseDTO.success(request.getTraceId());
        } catch (Exception e) {
            return OrderProcessCallbackResponseDTO.success(request.getTraceId());
        }
    }
    /**
     * 处理司机已到达位置到乘客上车点距离
     *
     * @param order 订单信息
     */
    private void handleDriverPosition(OrderVO order) {
        DriverLocationResponse driverLocationResponse = supplierService.driverLocation(order);
        if (!driverLocationResponse.isSuccess()
                || Objects.isNull(driverLocationResponse.getLatitude())
                || Objects.isNull(driverLocationResponse.getLongitude())) {
            return;
        }
        // 调用LBS直线距离接口
        EuclideanDistanceResponse distanceResponse = lbsClient.euclideanDistance(
                buildEuclideanDistanceRequest(order, driverLocationResponse), order.getOrderSerialNo());

        if (Objects.isNull(distanceResponse)
                || distanceResponse.getCode() != NumberConstants.NUMBER_0
                || Objects.isNull(distanceResponse.getResult())) {
            log.info("调用LBS获取距离信息失败");
            return;
        }
        TicketVO ticket = order.getTicket();
        ticket.setDriverArrTime(DateUtils.getY4M2d2H2m2s2WebString(new Date()));
        ticket.setDriverDistance(distanceResponse.getResult().toPlainString());
        log.info("判责：更新距离信息:{}", FastJsonUtils.toJSONString(ticket.getTicketExtVO()));
        goodsCmdService.updateTicketExt(order.getOrderSerialNo(), ticket.getItemId(),
                FastJsonUtils.toJSONString(ticket.getTicketExtVO()));
        try {
            log.info("判责：获取最新的item信息:{}", FastJsonUtils.toJSONString(orderService.queryOrder(order.getOrderSerialNo()).getTicket().getTicketExtVO()));
        } catch (Exception e) {
            log.error("判责：获取最新的item信息失败：", e);
        }
    }

    /**
     * 构建lbs直线距离请求
     *
     * @param order    订单信息
     * @param response 司机位置信息
     * @return lbs直线距离请求
     */
    private EuclideanDistanceRequest buildEuclideanDistanceRequest(OrderVO order, DriverLocationResponse response) {
        AddressVO depAddress = order.getTripInfo().getDepAddress();

        EuclideanDistanceRequest request = new EuclideanDistanceRequest();
        EuclideanDistanceRequest.EuclideanDistanceDTO arg_1 = new EuclideanDistanceRequest.EuclideanDistanceDTO();
        arg_1.setType("Point");
        arg_1.setCoordinate_system("GCJ02");
        arg_1.setCoordinates(Lists.newArrayList(response.getLongitude(), response.getLatitude())); // 经纬度

        EuclideanDistanceRequest.EuclideanDistanceDTO arg_2 = new EuclideanDistanceRequest.EuclideanDistanceDTO();
        arg_2.setType("Point");
        arg_2.setCoordinate_system("GCJ02");
        arg_2.setCoordinates(Lists.newArrayList(new BigDecimal(depAddress.getLon()), new BigDecimal(depAddress.getLat()))); // 经纬度

        request.setArg_1(arg_1);
        request.setArg_2(arg_2);

        return request;
    }

    /**
     * 是否不是同一用车订单
     */
    private boolean ticketValidate(OrderVO order, OrderProcessCallbackRequestDTO request) {
        TicketVO ticket = order.getTicket();
        return ticket == null || !ticket.getSupplierCode().equals(request.getSupplierCode()) || !ticket.getBusinessNo().equals(request.getSupplierOrderNo());
    }

    /**
     * 计算订单状态
     * messageType:1：司机到达通知 2:乘客上车通知 3:行程结束通知
     */
    private OrderState calcOrderState(OrderVO order, MessageType messageType) {
        switch (messageType) {
            case DRIVER_ARRIVE_NOTICE:
                return OrderState.AWAITING_TRAVEL;
            case USER_ON_CA_NOTICE:
                return OrderState.IN_TRIP;
            case TRIP_END_NOTICE:
                return OrderState.TRIP_FINISHED;
            case DRIVER_DEPARTURE_NOTICE:
                return order.getOrderState();
            default:
                return null;
        }
    }

    /**
     * 计算用户状态
     * messageType:1：司机到达通知 2:乘客上车通知 3:行程结束通知
     */
    private UserState calcUserState(MessageType messageType) {
        switch (messageType) {
            case DRIVER_ARRIVE_NOTICE:
                return UserState.WAITING_TO_BOARD;
            case USER_ON_CA_NOTICE:
                return UserState.DURING_THE_TRIP;
            case TRIP_END_NOTICE:
                return UserState.TRIP_FINISHED;
            case DRIVER_DEPARTURE_NOTICE:
                return UserState.WAITING_TO_PICKUP;
            default:
                return null;
        }
    }

    /**
     * 计算用户场景
     * messageType:1：司机到达通知 2:乘客上车通知 3:行程结束通知
     */
    private UserSceneEnum calcUserScene(MessageType messageType) {
        switch (messageType) {
            case DRIVER_ARRIVE_NOTICE:
                return UserSceneEnum.DRIVER_ARRIVE_NOTICE;
            case USER_ON_CA_NOTICE:
                return UserSceneEnum.USER_ON_CA_NOTICE;
            case TRIP_END_NOTICE:
                return UserSceneEnum.TRIP_END_NOTICE;
            case CAR_POOL_NOTICE:
                return UserSceneEnum.CAR_POOL_NOTICE;
            case DRIVER_DEPARTURE_NOTICE:
                return UserSceneEnum.DRIVER_DEPARTURE;
            default:
                return null;
        }
    }

    /**
     * 计算订单状态机场景类型
     * @param messageType
     * @return
     */
    private OrderStateSceneEnum calcOrderStateScene(MessageType messageType) {
        switch (messageType) {
            case DRIVER_DEPARTURE_NOTICE:
                return OrderStateSceneEnum.DRIVER_DEPARTURE_NOTICE;
            default:
                return null;
        }
    }

    /**
     * 执行业务操作前记录日志
     * @param content
     * @param orderVO
     */
    private void logBeforeProcess(String content, OrderVO orderVO) {

        Map<String, Object> ext = Maps.newHashMap();
        ext.put(LogExt.LOG_EXT_CONTENT, content);

        LogContext logContext = LogContext.builder()//
                .logModule(LogModule.SYSTEM)//
                .order(orderVO)//
                .ext(ext)//
                .build();

        operateLogService.log(logContext);
    }
}
