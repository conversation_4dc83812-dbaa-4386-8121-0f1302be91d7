package com.ly.travel.car.tradecore.service.log.builder;

import java.util.List;

import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.ly.travel.car.orderservice.facade.model.log.OrderLog;
import com.ly.travel.car.tradecore.service.log.LogBuilder;
import com.ly.travel.car.tradecore.service.log.LogContext;
import com.ly.travel.car.tradecore.service.log.LogModule;

/**
 * 重派重复日志
 * 
 * <AUTHOR>
 * @version Id: ReassignRepeatLogBuilder, v 0.1 2024/4/10 19:18 icanci Exp $
 */
@Service("reassignRepeatLogBuilder")
public class ReassignRepeatLogBuilder extends AbstractLogBuilder implements LogBuilder {
    /**
     * 构建日志
     *
     * @param context 日志上下文
     * @return 日志
     */
    @Override
    public List<OrderLog> build(LogContext context) {
        String message = messageFactory.operateLog(context.getLogModule().getCode());
        return Lists.newArrayList(createLog(module(), context.getOperator(), context.getOrder(), message));
    }

    /**
     * 获取模块
     *
     * @return 模块
     */
    @Override
    public LogModule module() {
        return LogModule.REASSIGN_REPEAT;
    }
}