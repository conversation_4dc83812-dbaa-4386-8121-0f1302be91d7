package com.ly.travel.car.tradecore.service.tracer.content;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.UserSceneEnum;
import com.ly.travel.car.tradecore.service.tracer.NotifyContentService;

/**
 * <AUTHOR>
 * @version Id: SupplierReassignResultContent, v 0.1 2024/3/28 21:33 jjy30439 Exp $
 */
@Service
public class SupplierReassignResultContent implements NotifyContentService {

    /**
     * 获取内容
     *
     * @param order 订单
     * @param ctx 上下文
     * @return 内容
     */
    @Override
    public String getContent(OrderVO order, Map<String, Object> ctx) {
        return order.getTicket().getCarNum();
    }

    /**
     * 获取场景
     *
     * @return 场景
     */
    @Override
    public UserSceneEnum scene() {
        return UserSceneEnum.SUPPLIER_REASSIGN_RESULT_NOTICE;
    }
}