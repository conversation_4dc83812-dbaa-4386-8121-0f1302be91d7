package com.ly.travel.car.tradecore.business.book.strategy.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ly.sof.utils.common.DateUtil;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.sof.utils.mapping.JacksonUtils;
import com.ly.travel.car.common.model.enums.ChargeTypeEnum;
import com.ly.travel.car.common.utils.DateUtils;
import com.ly.travel.car.search.facade.request.car.BBMSearchRequestDTO;
import com.ly.travel.car.search.model.car.Route;
import com.ly.travel.car.search.model.encode.RouteEncoder;
import com.ly.travel.car.tradecore.builder.PreTransferOrderBookBuilderProxy;
import com.ly.travel.car.tradecore.builder.book.TicketBuilder;
import com.ly.travel.car.tradecore.builder.book.model.TicketBuilderKey;
import com.ly.travel.car.tradecore.builder.book.model.TripInfoBuilderResp;
import com.ly.travel.car.tradecore.business.book.model.BaseBookContext;
import com.ly.travel.car.tradecore.business.book.strategy.OrderBookAbstractStrategy;
import com.ly.travel.car.tradecore.business.cancel.model.CancelContext;
import com.ly.travel.car.tradecore.business.cancel.processor.goodsitemcancel.GoodsItemCancelProcessorProxy;
import com.ly.travel.car.tradecore.business.cancel.processor.paycancel.PayCancelProcessorProxy;
import com.ly.travel.car.tradecore.enums.strategy.BookStrategyEnum;
import com.ly.travel.car.tradecore.error.BizErrorFactory;
import com.ly.travel.car.tradecore.error.OrderCancelException;
import com.ly.travel.car.tradecore.error.OrderQueryException;
import com.ly.travel.car.tradecore.facade.request.trade.CancelRequestDTO;
import com.ly.travel.car.tradecore.facade.response.trade.BookResponseDTO;
import com.ly.travel.car.tradecore.integration.client.flight.FlightChangeClient;
import com.ly.travel.car.tradecore.integration.client.flight.model.FlightRoundChangeDTO;
import com.ly.travel.car.tradecore.integration.client.flight.model.FlightRoundChangeRequestDTO;
import com.ly.travel.car.tradecore.integration.client.flight.model.FlightRoundChangeResponse;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.*;
import com.ly.travel.car.tradecore.model.goods.TicketVO;
import com.ly.travel.car.tradecore.model.order.FlightInfoVO;
import com.ly.travel.car.tradecore.model.rulesnapshot.RuleSnapshotInfoVO;
import com.ly.travel.car.tradecore.model.trip.TripInfoVO;
import com.ly.travel.car.tradecore.order.OrderService;
import com.ly.travel.car.tradecore.producer.impl.PreOrderDelayCancelProducer;
import com.ly.travel.car.tradecore.producer.impl.PreTransferOrderProducer;
import com.ly.travel.car.tradecore.producer.source.PreOrderDelayCancelSource;
import com.ly.travel.car.tradecore.producer.source.PreTransferOrderSource;
import com.ly.travel.car.tradecore.service.config.ConfigCenter;
import com.ly.travel.car.tradecore.service.log.LogContext;
import com.ly.travel.car.tradecore.service.log.LogExt;
import com.ly.travel.car.tradecore.service.log.LogModule;
import com.ly.travel.car.tradecore.service.log.OperateLogService;
import com.ly.travel.car.tradecore.service.push.PushService;
import com.ly.travel.car.tradecore.service.rulesnapshot.RuleSnapshotService;
import com.ly.travel.car.tradecore.service.rulesnapshot.model.RuleSnapshotInfoWrapper;
import com.ly.travel.car.tradecore.service.tracer.TracerService;
import com.ly.travel.car.tradecore.utils.CfgUtils;
import com.ly.travel.car.tradecore.utils.Pre2FirmOrderCarSignTTLHolder;
import com.ly.travel.car.tradecore.utils.SmsTemplateConstants;

import com.ly.watcher.shaded.org.checkerframework.checker.units.qual.C;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 转单策略
 * @Author: jay.he
 * @Date: 2024-08-07 13:31
 * @Version: 1.0
 **/
@Slf4j
@Component
public class CarOrderTransferBookStrategy extends OrderBookAbstractStrategy {

    private static final BizErrorFactory ERROR_FACTORY = BizErrorFactory.getInstance();

    @Resource
    private OrderService orderService;

    @Resource
    private PreTransferOrderBookBuilderProxy preTransferOrderBookBuilderProxy;

    @Resource
    protected GoodsItemCancelProcessorProxy goodsItemCancelProcessor;

    @Resource
    private PushService pushService;

    @Resource
    private TicketBuilder ticketBuilder;

    @Resource
    private OperateLogService operateLogService;

    @Resource
    private ConfigCenter configCenter;

    @Resource
    private PreTransferOrderProducer preTransferOrderProducer;

    @Resource(name = "flightChangeClient")
    private FlightChangeClient flightChangeClient;

    @Resource
    protected PayCancelProcessorProxy payCancelProcessor;

    @Resource
    private RuleSnapshotService ruleSnapshotService;

    @Resource
    private PreOrderDelayCancelProducer preOrderDelayCancelProducer;

    @Resource
    private TracerService tracerService;


    @Override
    public BookResponseDTO book(BaseBookContext context) {
        LoggerUtils.info(log, "[CarOrderTransferBookStrategy][before dispatchImmediatelyProcess] book:orderVO={}", FastJsonUtils.toJSONString(context.getOrder()));
        //立即转单处理超时(6s)，自动取消订单(10分钟延迟取消)
        BookResponseDTO response = dispatchImmediatelyProcess(context);
        LoggerUtils.info(log, "[CarOrderTransferBookStrategy][after dispatchImmediatelyProcess] book:orderVO={}", FastJsonUtils.toJSONString(context.getOrder()));

        if (response.isSuccess()) {
            return response;
        }
        //处理航变
        flightChangeProcess(context);
        //转正式单前置处理
        transferPreProcess(context);
        LoggerUtils.info(log, "[CarOrderTransferBookStrategy] book:orderVO={}", FastJsonUtils.toJSONString(context.getOrder()));
        //下正式单
        return super.book(context);
    }


    /**
     * 转单前处理  ---  立即派车处理超时时，走延迟取消流程
     * 立即转单处理超时(6s)，自动取消订单(10分钟延迟取消)
     *
     * @param context
     * @return
     */
    private BookResponseDTO dispatchImmediatelyProcess(BaseBookContext context) {
        OrderVO order = context.getOrder();
        if (order.isUserRequireDispatchImmediately()) {
            Date now = new Date();
            if (now.after(DateUtil.addSeconds(order.getGmtUsage(), 6))) {
                doDelayCancelPreOrder(context, CancelType.SYSTEM_CANCEL);
                return BookResponseDTO.success(order.getTraceId(), order.getOrderSerialNo(), BigDecimal.ZERO, null, PayCategory.PAYMENT_POINT.getCode());
            }
        }
        return BookResponseDTO.fail(context.getTraceId(), null, null);
    }

    private void flightChangeProcess(BaseBookContext context) {
        //只处理接机  -- 非接送机直接返回
        if (!OrderType.getByCrmType(getOrderTypeFromSign(context.getOrder().getSign())).isCarJsPlane()) {
            return;
        }
        String arrivalTime = getFlightChangeArrivalTime(context);
        if (StringUtils.isNoneBlank(arrivalTime)) { //发生航变
            flightChangeOps(context, arrivalTime);
        }
    }

    private void flightChangeOps(BaseBookContext context, String flightArrivalTime) {
        //发生了航变，newUsageTime是航变后的接机用车时间 = 抵达时间+buffMinutes
        Date newUsageTime = DateUtil.addMinutes(DateUtil.parseDateNewFormat(flightArrivalTime),
                configCenter.getFlightChangeBufferMinutes());
        log.info("发生航变后，订单：{},新用车时间:{}, 原用车时间:{}",
                context.getOrder().getOrderSerialNo(),
                DateUtil.getNewFormatDateString(newUsageTime),
                DateUtil.getNewFormatDateString(context.getOrder().getGmtUsage()));
        context.getOrder().setGmtUsage(newUsageTime);
        //航班后更改用车时间
        orderService.updatePreOrderGmtUsage(context.getOrder(), newUsageTime);
        orderService.updateOrderTags(context.getOrder(), Lists.newArrayList(OrderTag.PRE_ORDER_FLIGHT_CHANGE));

        cancelPreOrder(context, CancelType.FLIGHT_CHANGE_CANCEL);
        throw new RuntimeException("当前订单对应主订单发生航变");

    }

    /**
     * 转正式单前置处理
     * 1.打tag和业务类型
     * 2.为基于预约单生成的orderVO设置默认值
     * 3.二次询价+Resource资源过滤
     *
     * @param context
     */
    protected void transferPreProcess(BaseBookContext context) {
        /*
        二次询价+Resource资源过滤
        1.基于预约单OrderVO，生成正式单所需数据
        2.权益复用，不用再次解析
        3.更改预约单状态
         */
        filterAndRebuildTripIno(context);
    }

    /**
     * 基于预约单价格，结合二次询价过滤Resources生成Trip信息
     *
     * @param context
     */
    private void filterAndRebuildTripIno(BaseBookContext context) {
        try {
            final TicketVO originTicket = context.getOrder().getTicket();
            TripInfoBuilderResp tripInfoBuilderResp = preTransferOrderBookBuilderProxy.getTripInfoBuilderResp(null,context.getOrderSerialNo());
            TripInfoVO tripInfo = tripInfoBuilderResp.getTripInfo();
            log.info("[CarOrderTransferBookStrategy][filterAndRebuildTripIno] 二次询价过滤资源，预约单号：{},tripInfo:{}", context.getOrderSerialNo(), tripInfo);
            context.getOrder().setTripInfo(tripInfo);
            //重新构建ticket
            TicketVO newTicket = ticketBuilder
                    .build(new TicketBuilderKey(context.getOrder().getOrderType(), tripInfo, tripInfoBuilderResp.getServiceType(), tripInfoBuilderResp.getPriceInfo()));
            newTicket.setCarTags(originTicket.getCarTags());
            newTicket.setSalePrice(originTicket.getSalePrice());
            newTicket.setRealPrice(originTicket.getRealPrice());
            context.getOrder().setSearchTraceId(tripInfoBuilderResp.getSearchTraceId());
            context.getOrder().setTicket(newTicket);
        } catch (Exception e) {
            log.error("[CarOrderTransferBookStrategy][filterAndRebuildTripIno] 二次询价过滤资源失败，预约订单号：{},error:", context.getOrderSerialNo(), e);
            cancelPreOrder(context, CancelType.PRE_ORDER_TO_NORMAL_NO_RESOURCES);
            throw new RuntimeException("二次询价过滤资源失败");
        }
    }


    @Override
    protected void cancelPreOrder(BaseBookContext context, CancelType cancelType) {
        boolean needCancel = checkNeedCancel(context);
        log.warn("[CarOrderTransferBookStrategy][cancelPreOrder],预约单:{},异常取消，是否直接取消：{}",
                context.getOrderSerialNo(), needCancel);
        //1.是否直接取消
        if (needCancel) {
            if (context.getOrder().isUserRequireDispatchImmediately()) {
                //立即派单转单失败，需要走预约单延迟取消
                doDelayCancelPreOrder(context, cancelType);
            } else {
                doCancelPreOrder(context, cancelType);
            }
            return;
        }
        //2.是否循环转单，本次转单失败，下次继续触发
        Long delaySeconds = getDelaySeconds(context);
        if (CfgUtils.isOfflineEnv() && configCenter.getPreTransferOrderPhoneWhiteList().contains(context.getOrder().getUserPhoneNo())) {
            //线下白名单phone，循环转单设置为2分钟一次
            delaySeconds = 3 * 60L;
        }
        log.info("[CarOrderTransferBookStrategy][cancelPreOrder],预约单:{},循环转正式单，延迟时间：{}秒",
                context.getOrderSerialNo(), delaySeconds);
        sendDelayMQ(context, delaySeconds);
    }

    /**
     * 预约单延迟取消MQ
     *
     * @param context
     * @param cancelType
     */
    private void doDelayCancelPreOrder(BaseBookContext context, CancelType cancelType) {
        OrderVO order = null;
        try {
            order = orderService.queryPreOrder(context.getOrderSerialNo());
        } catch (OrderQueryException e) {
            throw new RuntimeException(e);
        }
        //1.更新状态
        orderService.updatePreOrderState(order,
                OrderState.PRE_ORDER_DELAY_CANCELED,
                UserState.PRE_ORDER_DELAY_CANCELED);
        //更新订单预计取消时间
        order
                .setLatestCancelTime(DateUtils.getY4M2d2H2m2s2WebString(
                        DateUtil.addMinutes(order.getGmtUsage(),
                                configCenter.getPreOrderDelayCancelMinutes())));
        log.info("order ext info, isPreOrder:{}, info: {}",
                order.isPreOrder(),
                FastJsonUtils.toJSONString(order.getOrderExtVO()));
        // 更新订单ext
        orderService.updatePreOrderExt(order);
        //2.发延迟取消MQ
        preOrderDelayCancelProducer.send(PreOrderDelayCancelSource.builder().order(order).resourceType(PreOrderResourceType.ESTIMATE)
            .delay(configCenter.getPreOrderDelayCancelMinutes() * 60L).cancelReason(Strings.concat(cancelType.getCancelReason(), "*立即派单失败-取消订单"))
            .cancelReasonType(cancelType.getCancelReasonType()).cancelSmsReason(cancelType.getSmsReason()).build());
        //3.轨迹推送
        tracerService.notify(order, UserSceneEnum.PRE_ORDER_DELAY_CANCELED);
    }


    /**
     * check是否需要直接取消订单
     *
     * @param context
     * @return
     */
    private boolean checkNeedCancel(BaseBookContext context) {
        //在用车时间近10分钟(可配置)内，或者火车票上门接送
        Integer delayMinutes = configCenter.getPreBookDelayMinutes();
        Date gmtUsage = context.getOrder().getGmtUsage();
        Date latestDelayTime = DateUtil.addMinutes(gmtUsage, -delayMinutes);
        //是否为最后一次转单
        boolean latestTransferFlag = latestDelayTime.before(new Date());
        //预估价或一口价的最后一次转单或用户操作立即转单，执行异常时需要取消订单
        return context.getOrder().isUserRequireDispatchImmediately()
                || getChargeType(context.getOrder()) == ChargeTypeEnum.ESTIMATE
                || (getChargeType(context.getOrder()) == ChargeTypeEnum.FIXED && latestTransferFlag);
    }

    /**
     * 获取下次转单时间
     *
     * @param context
     * @return
     */
    private Long getDelaySeconds(BaseBookContext context) {
        Date now = new Date();
        //最后一次转单时间
        Date lastTransferTime = getLastTransferTime(context);
        //下次循环转单时间(24小时后)
        Date nextTransferTime = DateUtil.addHours(now, 24);
        if (nextTransferTime.before(lastTransferTime)) {
            return DateUtil.getDiffSeconds(nextTransferTime, now);
        } else {
            return DateUtil.getDiffSeconds(lastTransferTime, now);
        }
    }

    /**
     * 获取最后一次转正式单时间
     */
    private Date getLastTransferTime(BaseBookContext context) {
        Integer delayMinutes = configCenter.getPreBookDelayMinutes();
        Date gmtUsage = context.getOrder().getGmtUsage();
        return DateUtil.addMinutes(gmtUsage, -delayMinutes);
    }

    private String getFlightChangeArrivalTime(BaseBookContext context) {
        FlightInfoVO flightInfo = context.getOrder().getBookInfo().getFlightInfo();
        if (Objects.isNull(flightInfo) || StringUtils.isBlank(flightInfo.getFlightOrderNo())) {
            return null;
        }
        FlightRoundChangeRequestDTO request = new FlightRoundChangeRequestDTO();
        request.setSerialNo(flightInfo.getFlightOrderNo());
        FlightRoundChangeResponse response = flightChangeClient.queryDetail(request);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getData())) {
            return null;
        }
        // 取最新航变记录
        FlightRoundChangeDTO flightRoundChangeDto = response.getData().stream().sorted(FlightRoundChangeDTO::compareTo).findFirst().orElse(null);
        if (Objects.isNull(flightRoundChangeDto)) {
            return null;
        }
        // 校验是否发生航变
        FlightChangeTypeEnum flightChangeTypeEnum = FlightChangeTypeEnum.getByCode(flightRoundChangeDto.getChangeType());
        switch (flightChangeTypeEnum) {
            case CANCEL:
            case AIRPORT_CHANGE:
                cancelPreOrder(context, CancelType.FLIGHT_CHANGE_CANCEL);
                return null;
            case DELAY:
                OrderType originOrderType = OrderType.getByCrmType(getOrderTypeFromSign(context.getOrder().getSign()));
                switch (originOrderType) {
                    case CAR_PLANE_IN:
                        return flightRoundChangeDto.getNewArrivalTime();
                    case CAR_PLANE_OUT: //送机不处理
                        return null;
                }
        }
        return null;
    }

    /**
     * 获取首次询价的订单类型(接送机、接送站-转单时会转成网约车，但需要基于原类型更新接送机时间)
     *
     * @param sign
     * @return
     */
    private int getOrderTypeFromSign(String sign) {
        // 解析网约车行程信息
        Route route = RouteEncoder.decode(sign);
        return JacksonUtils.fromJSONString(MapUtils.emptyIfNull(route.getExt()).get("aggRequest"), BBMSearchRequestDTO.class).getBizType();
    }

    /**
     * 循环发送预约单转正MQ
     *
     * @param context
     * @param delaySeconds
     */
    private void sendDelayMQ(BaseBookContext context, Long delaySeconds) {
        preTransferOrderProducer.send(PreTransferOrderSource.builder()
                .order(context.getOrder())
                .resourceType(PreOrderResourceType.ESTIMATE)
                .delay(delaySeconds.intValue())
                .build());
    }

    /**
     * 执行取消操作
     *
     * @param context
     * @param cancelType
     */
    private void doCancelPreOrder(BaseBookContext context,CancelType cancelType) {
        log.error("[CarOrderTransferBookStrategy][cancelPreOrder] 预约单转正式单失败自动取消，订单：{} 原因:{}", context.getOrder().getOrderSerialNo(), cancelType.getCancelReason());
        OrderVO order = null;
        try {
            order = orderService.queryPreOrder(context.getOrderSerialNo());
        } catch (OrderQueryException e) {
            throw new RuntimeException(e);
        }
        //Context中的OrderVO在Listener中被赋初始值(为了创正式单)，退款时需要拿原值
        context.getOrder().setAmount(order.getAmount());
        orderService.updateCancelPreOrder(context.getOrder(), CancelType.SYSTEM_CANCEL.getCode(), cancelType.getCancelReason(), CancelReasonType.PLATFORM);
        //字段取消原因和操作日志
        cancelOperatorLog(context, cancelType.getCancelReason());
        //5.取消商品(优惠券、里程、辅营等)
        try {
            CancelContext cancelContext = buildCancelContext(context, cancelType);
            log.info("[CarOrderTransferBookStrategy][cancelPreOrder] 退款失败，取消商品前：{}", JacksonUtils.toJSONString(cancelContext.getRefundItems()));
            goodsItemCancelProcessor.doProcess(cancelContext);
            log.info("[CarOrderTransferBookStrategy][cancelPreOrder] 退款失败，，取消商品后：{}", JacksonUtils.toJSONString(cancelContext.getRefundItems()));
            //支付取消(扣款or退款) --- 一口价先付场景，需要添加支付取消
            payCancelProcessor.doProcess(cancelContext);
            //订单取消-轨迹推送
            tracerService.notify(context.getOrder(), UserSceneEnum.PRE_ORDER_TRANSFER_FAILURE_CANCELED);
        } catch (OrderCancelException e) {
            log.error("[CarOrderTransferBookStrategy][cancelPreOrder] error:", e);
        }
        //023短信废弃，改用通用的034
    }

    protected void cancelOperatorLog(BaseBookContext context, String reason) {
        String content = String.format("内容：[订单取消:自动取消预约单:原因:%s]", reason);
        LoggerUtils.info(log, "[订单取消:取消预约单] 订单号:{},原因:{}", context.getOrderSerialNo(), reason);
        // 顺带记录操作日志
        Map<String, Object> ext = Maps.newHashMap();
        ext.put(LogExt.LOG_EXT_CONTENT, content);
        ext.put("cancelType", CancelType.SYSTEM_CANCEL.getCode());
        ext.put("reason", reason);
        ext.put("disclaimer", true);
        LogContext logContext = LogContext.builder()//
                .logModule(LogModule.CANCEL)//
                .order(context.getOrder())//
                .ext(ext)//
                .build();
        operateLogService.log(logContext);
    }

    private CancelContext buildCancelContext(BaseBookContext context, CancelType cancelType) {
        CancelRequestDTO request = new CancelRequestDTO();
        request.setMemberIds(Lists.newArrayList(context.getOrder().getMemberInfo().getId()));
        request.setOrderSerialNo(context.getOrderSerialNo());
        request.setCancelType(cancelType.getCode());
        request.setCancelTypeRealCode(cancelType.getRealCode());
        request.setReason(cancelType.getCancelReason());
        request.setDisclaimer(true);
        request.setPenaltyConfirmAmount("0");
        request.setPaidTimeout(false);
        //新增两个字段，上游透传
        request.setCancelReasonType(cancelType.getCancelReasonType());
        request.setCancelSmsReason(cancelType.getSmsReason());
        request.setSupplierCallbackCancel(false);
        request.setPlatform("WX");
        return CancelContext.of(context.getOrder(), request);
    }

    /**
     * 保存二次询价搜索快照
     *
     * @param context
     */
    @Override
    protected void saveRuleSnapshot(BaseBookContext context) {
        try {
            String carSign = Pre2FirmOrderCarSignTTLHolder.get();
            if (StringUtils.isBlank(carSign)) {
                LoggerUtils.warn(log, "[saveRuleSnapshot] 预约单转正式单，二次询价Sign不存在");
            }
            //基于二次询价生成快照
            RuleSnapshotInfoVO ruleSnapshotInfo = RuleSnapshotInfoWrapper.createForPre2FirmOrder(context.getOrder(), carSign);
            ruleSnapshotService.saveRuleSnapshot(ruleSnapshotInfo);
        } finally {
            Pre2FirmOrderCarSignTTLHolder.remove();
        }
    }

    @Override
    protected BookResponseDTO bookSuccessProcess(BaseBookContext context) {
        orderService.updatePreOrderState(context.getOrder(), OrderState.PRE_TO_FORM_ORDER, context.getOrder().getUserState());
        return super.bookSuccessProcess(context);
    }

    @Override
    public BookStrategyEnum getStrategy() {
        return BookStrategyEnum.PRE_TRANSFER_BOOK_STRATEGY;
    }
}
