package com.ly.travel.car.tradecore.service.price.bill;

import java.util.List;

import com.google.common.collect.Lists;
import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.common.model.enums.ServiceType;
import com.ly.travel.car.tradecore.base.ctx.Context;
import com.ly.travel.car.tradecore.business.finished.model.OrderFinishedSupplierBill;
import com.ly.travel.car.tradecore.business.finished.model.OrderFinishedUserBill;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.goods.TicketVO;
import com.ly.travel.car.tradecore.model.price.BaseGoodsWrapper;
import com.ly.travel.car.tradecore.model.trip.ResourceVO;
import com.ly.travel.car.tradecore.model.trip.TripInfoVO;
import com.ly.travel.car.tradecore.service.price.PriceUtils;
import com.ly.travel.car.tradecore.service.price.model.OrderBillContext;
import com.ly.travel.car.tradecore.service.price.model.OrderBillWrapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 顺风车订单账单计算服务
 *
 * <AUTHOR>
 * @version Id: SfcOrderBillService.java, v 0.1 2024-03-21 22:48 ryan Exp $$
 */
@Slf4j
public abstract class BaseSfcOrderBillService implements OrderBillService {

    /**
     * 行程结束账单计算服务
     *
     * @param context the context
     * @param details the details
     * @return the car bill wrapper
     */
    @Override
    public OrderBillWrapper bill(Context context, OrderFinishedSupplierBill details) {
        OrderVO order = context.getOrder();
        TicketVO ticket = order.getTicket();
        TripInfoVO tripInfo = order.getTripInfo();
        ServiceType serviceType = ticket.getServiceType();
        OrderBillContext orderBillContext = new OrderBillContext(context, details);

        // 计算【对客实际销售价】
        Money newRealPrice = getNewRealPrice(orderBillContext);
        orderBillContext.setNewRealPrice(newRealPrice);

        // 原总价(用车费+保险+全部抵扣项)
        Money oldTicketTotalSalePrice = getOldTicketTotalSalePrice(orderBillContext);

        // 新总价(新的用车费+保险+新的全部抵扣项实抵金额)
        Money newTicketTotalSalePrice = getNewTicketTotalSalePrice(orderBillContext);

        OrderFinishedUserBill userBill = new OrderFinishedUserBill();
        userBill.setOldTicketTotalSalePrice(oldTicketTotalSalePrice);
        userBill.setNewTicketTotalSalePrice(newTicketTotalSalePrice);
        userBill.setCarPrice(newRealPrice);
        userBill.setRealCarPrice(newRealPrice);
        userBill.setRealPrice(newRealPrice);
        userBill.setFlashSaleCouponPrice(order.getFlashSaleCouponPrice());
        userBill.setRealSupplierPrice(details.getTotalAmount());
        userBill.setCarAttachPrice(new Money(0));
        userBill.setFeeItems(Lists.newArrayList());
        userBill.setExclusivePrice(tripInfo.getSfcServicePriceByType(ServiceType.EXCLUSIVE));
        userBill.setCarpoolPrice(tripInfo.getSfcServicePriceByType(ServiceType.CAR_POOL));
        userBill.setWillingCarpoolPrice(tripInfo.getSfcServicePriceByType(ServiceType.WILLING_CAR_POOL));
        userBill.setServiceType(serviceType);
        userBill.setResource(getTicketResource(orderBillContext));
        userBill.setOrderType(order.getOrderType());
        orderBillContext.setUserBill(userBill);
        //账单调整
        billChange(orderBillContext);

        LoggerUtils.info(log, "[SfcBill] orderSerialNo:{} userBill:{}", order.getOrderSerialNo(), userBill.toString());
        return OrderBillWrapper.create(userBill, details, null, details.getHitRiskType());
    }



    /**
     * 默认获取订单老的用车销售价
     */
    public Money defaultGetOldTicketTotalSalePrice(OrderVO order) {
        return order.getTicket().getSalePrice().add(order.getAllInsurePrice()).add(order.getAllDeductionRealPrice());
    }

    /**
     * 默认获取订单新的用车销售价
     */
    public Money defaultGetNewTicketTotalSalePrice(OrderVO order, Money newRealPrice) {
        // 新总价(新的用车费+保险+新的全部抵扣项实抵金额)
        List<BaseGoodsWrapper> negativeGoodsWrappers = PriceUtils.getNegativeGoodsWrapper(order.getAllNegativeGoods());
        Money deductionPrice = PriceUtils.calcDeductionPricWithNewCarPrice(negativeGoodsWrappers, newRealPrice, order.getAllInsurePrice(), order);
        return newRealPrice.add(order.getAllInsurePrice()).add(deductionPrice);
    }

    /**
     * Gets new real price.
     *
     */
    public Money defaultGetNewRealPrice(OrderBillContext orderBillContext) {
        OrderFinishedSupplierBill supplierBill = orderBillContext.getSupplierBill();
        OrderVO order = orderBillContext.getOrder();
        TicketVO ticket = order.getTicket();
        TripInfoVO tripInfo = order.getTripInfo();
        ServiceType serviceType = ticket.getServiceType();
        Money newRealPrice = ticket.getRealPrice();
        if (supplierBill.isSfcPooled() && serviceType == ServiceType.WILLING_CAR_POOL) {
            ResourceVO resource = tripInfo.findByServiceType(ServiceType.CAR_POOL);
            if (resource != null) {
                newRealPrice = resource.getSalePrice();
            }
        }
        return newRealPrice;
    }

    /**
     * Gets new real price.
     *
     * @return the new real price
     */
    public ResourceVO getFinishedResource(OrderBillContext orderBillContext) {
        OrderVO order = orderBillContext.getOrder();
        OrderFinishedSupplierBill supplierBill = orderBillContext.getSupplierBill();
        TicketVO ticket = order.getTicket();
        TripInfoVO tripInfo = order.getTripInfo();
        ServiceType serviceType = ticket.getServiceType();
        if (supplierBill.isSfcPooled() && serviceType == ServiceType.WILLING_CAR_POOL) {
            return tripInfo.findByServiceType(ServiceType.CAR_POOL);
        }
        return tripInfo.findByServiceType(serviceType);
    }

    /********************************************************************************************************************
     * 以下是抽象方法，子类需要实现
     ********************************************************************************************************************/

    /**
     * 获取订单资源信息
     */
    public abstract ResourceVO getTicketResource(OrderBillContext orderBillContext);
    /**
     * 获取订单新的用车实际价格
     */
    public abstract Money getNewRealPrice(OrderBillContext orderBillContext);

    /**
     * 获取订单老的用车销售价 (支付金额)
     */
    public abstract Money getOldTicketTotalSalePrice(OrderBillContext orderBillContext);

    /**
     * 获取订单新的用车销售价 (支付金额)
     */
    public abstract Money getNewTicketTotalSalePrice(OrderBillContext orderBillContext);

    /**
     * 账单调整
     * 此时 userBill为空
     */
    public abstract void billChange(OrderBillContext orderBillContext);
}
