/**
 * LY.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.ly.travel.car.tradecore.business.pay.immediatededuct;

import com.ly.travel.car.tradecore.business.pay.model.ImmediateDeductContext;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.facade.response.pay.ImmediateDeductResponseDTO;

/**
 * 立即扣款
 */
public interface ImmediateDeductService {

    /**
     * 立即扣款
     *
     */
    ImmediateDeductResponseDTO immediateDeduct(ImmediateDeductContext context) throws BuilderException;
}
