package com.ly.travel.car.tradecore.business.refund;

import com.ly.travel.car.tradecore.business.refund.model.RefundForDiffContext;
import com.ly.travel.car.tradecore.facade.response.refund.RefundForDiffResponseDTO;

/**
 * 退差退款服务
 */
public interface RefundForDiffService {

    /**
     * 退差退款
     * @param context
     */
    RefundForDiffResponseDTO refundForDiff(RefundForDiffContext context) throws Exception;
}
