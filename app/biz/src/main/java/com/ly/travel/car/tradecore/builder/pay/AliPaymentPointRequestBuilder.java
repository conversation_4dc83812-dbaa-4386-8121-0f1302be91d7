package com.ly.travel.car.tradecore.builder.pay;

import com.ly.travel.car.tradecore.producer.payload.PaymentPointDeductPayload;
import org.springframework.stereotype.Service;

import com.ly.sof.utils.common.Money;
import com.ly.travel.car.tradecore.base.ctx.Context;
import com.ly.travel.car.tradecore.builder.BaseBuilder;
import com.ly.travel.car.tradecore.business.book.model.BaseBookContext;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.facade.request.pay.PayPointErpCancelRequestDTO;
import com.ly.travel.car.tradecore.integration.client.pay.model.alipay.AliPayAuthorizationRequestDTO;
import com.ly.travel.car.tradecore.integration.client.pay.model.alipay.AliPayCancelScoreRequestDTO;
import com.ly.travel.car.tradecore.integration.client.pay.model.alipay.AliPayPaidRequestDTO;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.producer.Constants;
import com.ly.travel.car.tradecore.utils.PayUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 支付分请求构造
 * 
 * <AUTHOR>
 * @version Id: PayPaymentPointPaidRequestBuilder, v 0.1 2024/3/7 10:48 ryan Exp $
 */
@Slf4j
@Service("aliPaymentPointRequestBuilder")
public class AliPaymentPointRequestBuilder extends BaseBuilder {
    /**
     * 构建
     *
     * @param context the context
     * @return 构建出来目标对象 pay source paid request dto
     * @throws BuilderException 构建异常
     */
    public AliPayPaidRequestDTO buildPaidRequest(Context context) {
        OrderVO order = context.getOrder();
        AliPayPaidRequestDTO result = new AliPayPaidRequestDTO();
        result.setSerialId(order.getOrderSerialNo());
        result.setOrderType(order.getOrderType().getPayOrderType());
        result.setPayAmount(order.calcTotalPrice().getAmount());
        result.setChannel(order.getBookInfo().getOrderChannel());
        result.setProjectSource(Constants.Pay.PROJECT_SOURCE);
        result.setMemberId(order.getMemberInfo().getId());
        result.setTraceId(order.getTraceId());
        result.setAppUk(appUk());
        return result;
    }

    public AliPayPaidRequestDTO buildPaidRequestForPenalty(OrderVO order, Money deductAmount) {
        AliPayPaidRequestDTO result = new AliPayPaidRequestDTO();
        result.setSerialId(order.getOrderSerialNo());
        result.setOrderType(order.getOrderType().getPayOrderType());
        result.setPayAmount(deductAmount.getAmount());
        result.setChannel(order.getBookInfo().getOrderChannel());
        result.setProjectSource(Constants.Pay.PROJECT_SOURCE);
        result.setMemberId(order.getMemberInfo().getId());
        result.setTraceId(order.getTraceId());
        result.setAppUk(appUk());
        return result;
    }

    public AliPayCancelScoreRequestDTO buildCancelErpPayScoreRequestDTO(PayPointErpCancelRequestDTO requestDTO, OrderVO orderVO) {
        AliPayCancelScoreRequestDTO result = new AliPayCancelScoreRequestDTO();
        result.setSerialId(orderVO.getOrderSerialNo());
        result.setOrderType(orderVO.getOrderType().getPayOrderType());
        result.setReason(requestDTO.getReason());
        result.setTraceId(orderVO.getTraceId());
        result.setAppUk(appUk());
        return result;
    }

    public AliPayCancelScoreRequestDTO buildCancelPayScoreRequestDTO(String reason, OrderVO orderVO) {
        AliPayCancelScoreRequestDTO result = new AliPayCancelScoreRequestDTO();
        result.setSerialId(orderVO.getOrderSerialNo());
        result.setOrderType(orderVO.getOrderType().getPayOrderType());
        result.setReason(reason);
        result.setTraceId(orderVO.getTraceId());
        result.setAppUk(appUk());
        return result;
    }

    public AliPayAuthorizationRequestDTO buildAuthorizationRequestDTO(BaseBookContext bookContext) {
        OrderVO order = bookContext.getOrder();
        AliPayAuthorizationRequestDTO result = new AliPayAuthorizationRequestDTO();
        result.setMemberId(bookContext.getMemberId());
        result.setProjectOwner(order.getPayProjectOwner());
        PayUtils.RegulatorTypeVO regulatorType = PayUtils.getRegulatorType(order);
        result.setReceiveCompany(String.valueOf(regulatorType.getReceiveChannel().getReceiveCompany()));
        result.setChannel(order.getBookInfo().getOrderChannel());
        result.setTraceId(bookContext.getTraceId());
        result.setAppUk(appUk());
        return result;
    }

    /**
     * 根据延迟扣款mq构建扣款请求 --- 违约金通用
     * @param payload
     * @return
     */
    public AliPayPaidRequestDTO buildPaidRequestByPayload(PaymentPointDeductPayload payload) {
        AliPayPaidRequestDTO result = new AliPayPaidRequestDTO();
        result.setSerialId(payload.getSerialId());
        return result;
    }
}