/**
 * LY.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.ly.travel.car.tradecore.mapper;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueMappingStrategy;

import com.ly.sof.utils.common.DateUtil;
import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.tradecore.business.finished.model.BalancePayPriceVO;
import com.ly.travel.car.tradecore.dal.dataobject.UserPendingPayInfoDO;
import com.ly.travel.car.tradecore.integration.Symbols;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.cvt.EnumConverter;
import com.ly.travel.car.tradecore.model.cvt.MoneyConverter;
import com.ly.travel.car.tradecore.model.enums.UserPendingPayScene;
import com.ly.travel.car.tradecore.model.enums.UserPendingPayStatus;
import com.ly.travel.car.tradecore.model.pay.UserPendingPayInfoExtVO;
import com.ly.travel.car.tradecore.model.pay.UserPendingPayInfoVO;
import com.ly.travel.car.tradecore.utils.CfgUtils;

/**
 * UserPendingPayInfoMapper
 *
 * <AUTHOR>
 * @version Id: UserPendingPayInfoMapper.java, v 0.1 2024-03-05 14:40 ryan Exp $$
 */
@Mapper(componentModel = "spring", imports = { StringUtils.class,
                                                        CfgUtils.class }, uses = { EnumConverter.class,
                                                                          MoneyConverter.class }, nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface UserPendingPayInfoMapper {

    @Mappings({
                //
                @Mapping(target = "env", expression = "java(CfgUtils.getEnv())"), @Mapping(target = "ext", expression = "java(ext(userPendingPayInfo))"),
                @Mapping(target = "paySerialNo", expression = "java(StringUtils.defaultString(userPendingPayInfo.getPendingPaySerialNo()))"),
            //
    })
    UserPendingPayInfoDO v2d(UserPendingPayInfoVO userPendingPayInfo);

    default String ext(UserPendingPayInfoVO userPendingPayInfo) {
        UserPendingPayInfoExtVO ext = new UserPendingPayInfoExtVO();
        ext.setItems(buildItems(userPendingPayInfo.getItems()));
        return FastJsonUtils.toJSONString(ext);
    }

    List<UserPendingPayInfoExtVO.PayItem> buildItems(List<UserPendingPayInfoVO.PayItem> items);

    default UserPendingPayInfoVO buildUserPendingPayInfo(OrderVO order, Money balanceTotalPrice, String balancePaySerialNo, UserPendingPayScene scene) {
        return UserPendingPayInfoVO.builder()//
            .memberId(order.getMemberInfo().getId())//
            .orderSerialNo(order.getOrderSerialNo())//
            .pendingPaySerialNo(StringUtils.EMPTY)//
            .pendingPaySerialNo(balancePaySerialNo)//
            .scene(scene)//
            .state(UserPendingPayStatus.PENDING)//
            .amount(balanceTotalPrice)//
            .gmtPaid(DateUtil.parseDateNewFormat(Symbols.DEFAULT_DATE_TIME))//
            .items(BalancePayPriceVO.buildPendingPayItems(order, balanceTotalPrice))//
            .build();
    }
}
