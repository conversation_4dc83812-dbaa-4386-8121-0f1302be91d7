package com.ly.travel.car.tradecore.service.rights;

import com.ly.travel.car.tradecore.base.ctx.Context;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 权益操作模板
 * @Author: jay.he
 * @Date: 2024-12-31 11:09
 * @Version: 1.0
 **/
public interface RightsOpsTemplate<REQ, RES> {

    /**
     * 模板方法定义权益操作流程
     */
    default RES execute(REQ req) {
        // 1. 前置校验
        if (!preCheck(req)) {
            return null;
        }

        // 2. 执行操作
        RES res = doOperation(req);

        // 3. 后置处理
        if (needPostProcess(req)) {
            return postProcess(req);
        }
        return res;
    }

    default boolean preCheck(REQ req) {
        return true;
    }

    RES doOperation(REQ req);

    default RES postProcess(REQ req) {
        return null;
    }

    default boolean needPostProcess(REQ req) {
        return false;
    }

}
