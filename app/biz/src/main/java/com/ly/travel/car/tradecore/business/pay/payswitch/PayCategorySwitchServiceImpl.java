package com.ly.travel.car.tradecore.business.pay.payswitch;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ly.sof.api.error.LYError;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.tradecore.business.ctx.StandardOrderContext;
import com.ly.travel.car.tradecore.business.pay.PayCategorySwitchService;
import com.ly.travel.car.tradecore.error.BizErrorFactory;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.error.ValidateException;
import com.ly.travel.car.tradecore.facade.request.pay.PayCategoryConfigRequestDTO;
import com.ly.travel.car.tradecore.facade.request.pay.PayCategorySwitchRequestDTO;
import com.ly.travel.car.tradecore.facade.response.pay.PayCategoryConfigResponseDTO;
import com.ly.travel.car.tradecore.facade.response.pay.PayCategorySwitchResponseDTO;
import com.ly.travel.car.tradecore.integration.Symbols;
import com.ly.travel.car.tradecore.integration.client.redis.impl.LockTemplateProxy;
import com.ly.travel.car.tradecore.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.tradecore.model.configcenter.PayCategoryConfig;
import com.ly.travel.car.tradecore.model.configcenter.PayCategoryConfigDetail;
import com.ly.travel.car.tradecore.model.enums.PayCategory;
import com.ly.travel.car.tradecore.order.OrderService;
import com.ly.travel.car.tradecore.order.StandardOrderCtx;
import com.ly.travel.car.tradecore.service.config.ConfigCenter;
import com.ly.travel.car.tradecore.service.pay.PayService;
import com.ly.travel.car.tradecore.utils.PayUtils;
import com.ly.travel.car.tradecore.utils.RedisKeyBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * PayCategorySwitchServiceImpl
 *
 * <AUTHOR>
 * @version Id: PayCategorySwitchServiceImpl  2024/7/1
 */
@Slf4j
@Service("payCategorySwitchService")
public class PayCategorySwitchServiceImpl implements PayCategorySwitchService {
    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();

    @Resource
    private OrderService                 orderService;

    @Resource
    private PayService                   payService;

    @Resource
    protected RedisClientProxy           redisClientProxy;

    @Resource
    protected ConfigCenter               configCenter;

    @Resource
    private LockTemplateProxy            lockTemplateProxy;

    @Override
    public PayCategorySwitchResponseDTO payCategorySwitch(PayCategorySwitchRequestDTO request) {
        boolean holdLock = false;
        try {
            //优先获取单独的切换支付分锁，防止用户重复点击阻塞主流程
            holdLock = redisClientProxy.setnx(RedisKeyBuilder.switchPayCategoryLock(request.getOrderSerialNo()), request.getOrderSerialNo(), 60);
            if (!holdLock) {
                throw new ValidateException(BIZ_ERROR_FACTORY.sysErr("重复请求"));
            }
            //获取通用流程锁，防止并发状态，司机已接单和切换支付分同时并发
            return lockTemplateProxy.getLockAndResult(RedisKeyBuilder.orderProcessingLock(request.getOrderSerialNo()), 3000, (lock) -> {
                if (lock) {
                    LoggerUtils.info(log, "切换支付方式，抢到锁");
                    try {
                        // 1.查询订单上下文
                        StandardOrderContext context = StandardOrderCtx.of(request.getOrderSerialNo());
                        // 2.校验会员id
                        String memberId = context.getMemberId();
                        if (!request.getMemberIds().contains(memberId)) {
                            throw new ValidateException(BIZ_ERROR_FACTORY.invalidOrder(request.getOrderSerialNo(), memberId));
                        }
                        // 3.判断支付方式
                        if (context.getOrder().isPaymentPoint()) {
                            // 如果已经是支付分抛出异常
                            throw new ValidateException(BIZ_ERROR_FACTORY.payCategoryValidateFailure());
                        }
                        if (!PayUtils.isPaymentPoint(PayCategory.getByCode(request.getPayCategory()))) {
                            // 如果请求的支付方式不是支付分,抛出异常
                            throw new ValidateException(BIZ_ERROR_FACTORY.payCategoryValidateFailure());
                        }
                        // 4.判断订单状态和支付状态
                        if (context.getOrder().isCanceled() || context.getOrder().isPaid()) {
                            throw new ValidateException(BIZ_ERROR_FACTORY.invalidOrder(request.getOrderSerialNo(), memberId));
                        }
                        // 由于支付宝没有进行延迟五分钟支付，所以改派场景下还是要限制 ，限制只要发生过接单就不允许切换支付方式
                        if (context.getOrder().isOrderReceived()) {
                            LoggerUtils.warn(log, "该订单发生接单后，仍有请求切换支付方式为支付分后付");
                            throw new ValidateException(BIZ_ERROR_FACTORY.orderStateErr("当前订单状态不允许切换支付方式"));
                        }

                        //先更新订单状态为支付分支付，防止支付创建成功回调过快
                        orderService.switchPayCategory(context.getOrder(), PayCategory.getByCode(request.getPayCategory()), "用户发起切换支付方式", Symbols.USER_NAME);
                        boolean createResult = payService.createPayOrderForPayScoreIndependent(context, request.getCurrentChannel(), request.getCurrentOpenId(),
                            request.getCurrentMemberId());
                        // 如果成功返回结果成功
                        if (createResult) {
                            return PayCategorySwitchResponseDTO.successResponse(context.getTraceId());
                        } else {
                            //如果失败还原订单支付方式
                            orderService.switchPayCategory(context.getOrder(), PayCategory.ONLINE, "用户切换支付方式时创建支付分订单失败", Symbols.SYSTEM_NAME);
                            LYError lyError = BIZ_ERROR_FACTORY.sysErr("创建支付分订单失败");
                            return PayCategorySwitchResponseDTO.failureResponse(context.getTraceId(), lyError.getCode(), lyError.getMessage());
                        }
                    } catch (BuilderException | ValidateException e) {
                        LoggerUtils.warn(log, "[payCategorySwitchService][payCategorySwitch] failure:{}", e, e.getMessage());
                        return PayCategorySwitchResponseDTO.failureResponse(request.getTraceId(), e.getError().getCode(), e.getError().getMessage());
                    }
                } else {
                    LoggerUtils.warn(log, "[payCategorySwitchService][payCategorySwitch] failure:{}", "当前有其他主流程动作执行，未获取到流程锁");
                    LYError lyError = BIZ_ERROR_FACTORY.sysErr("当前有其他主流程动作执行，未获取到流程锁");
                    return PayCategorySwitchResponseDTO.failureResponse(request.getTraceId(), lyError.getCode(), lyError.getMessage());
                }
            });
        } catch (ValidateException e) {
            LoggerUtils.warn(log, "[payCategorySwitchService][payCategorySwitch] failure:{}", e, e.getMessage());
            return PayCategorySwitchResponseDTO.failureResponse(request.getTraceId(), e.getError().getCode(), e.getError().getMessage());
        } finally {
            //解锁
            if (holdLock) {
                redisClientProxy.remove(RedisKeyBuilder.switchPayCategoryLock(request.getOrderSerialNo()));
            }
        }
    }

    @Override
    public PayCategoryConfigResponseDTO payCategoryConfig(PayCategoryConfigRequestDTO requestDTO) {
        PayCategoryConfig payCategoryConfig = configCenter.getPayCategoryConfig();
        LoggerUtils.info(log, "[PayCategorySwitchService] 读取统一配置,支付方式的配置:{}", FastJsonUtils.toJSONString(payCategoryConfig));
        if (payCategoryConfig == null) {
            LYError lyError = BIZ_ERROR_FACTORY.sysErr("未配置支付方式");
            return PayCategoryConfigResponseDTO.failureResponse(requestDTO.getTraceId(), lyError.getCode(), lyError.getMessage());
        }
        if (CollectionUtils.isEmpty(payCategoryConfig.getDefaultPayCategories()) && CollectionUtils.isEmpty(payCategoryConfig.getChannelPayCategories())) {
            LYError lyError = BIZ_ERROR_FACTORY.sysErr("未配置支付方式");
            return PayCategoryConfigResponseDTO.failureResponse(requestDTO.getTraceId(), lyError.getCode(), lyError.getMessage());
        }

        // 优先 渠道配置的支付方式
        List<PayCategoryConfigDetail> payCategories = getChannelPayCategories(requestDTO, payCategoryConfig);
        LoggerUtils.info(log, "[PayCategorySwitchService] 渠道配置的支付方式为:{}", FastJsonUtils.toJSONString(payCategories));

        // 无则取默认支付方式
        if (CollectionUtils.isEmpty(payCategories)) {
            payCategories = getDefaultPayCategories(requestDTO, payCategoryConfig);
            LoggerUtils.info(log, "[PayCategorySwitchService] 无相应的渠道配置的支付方式, 默认的支付方式为:{}", FastJsonUtils.toJSONString(payCategories));
        }

        if (CollectionUtils.isEmpty(payCategories)) {
            LYError lyError = BIZ_ERROR_FACTORY.sysErr("未配置支付方式");
            return PayCategoryConfigResponseDTO.failureResponse(requestDTO.getTraceId(), lyError.getCode(), lyError.getMessage());
        }

        // 按照优先级从大到小排序
        payCategories.sort((o1, o2) -> Integer.compare(o2.getPriority(), o1.getPriority()));

        return PayCategoryConfigResponseDTO.successResponse(requestDTO.getTraceId(), payCategories);
    }

    /**
     * 获取默认的支付方式
     */
    private List<PayCategoryConfigDetail> getDefaultPayCategories(PayCategoryConfigRequestDTO requestDTO, PayCategoryConfig payCategoryConfig) {
        List<PayCategoryConfigDetail> defaultPayCategories = payCategoryConfig.getDefaultPayCategories();
        if (CollectionUtils.isEmpty(defaultPayCategories)) {
            return new ArrayList<>(0);
        }
        return defaultPayCategories;
    }

    /**
     * 获取渠道配置的支付方式
     */
    private List<PayCategoryConfigDetail> getChannelPayCategories(PayCategoryConfigRequestDTO requestDTO, PayCategoryConfig payCategoryConfig) {
        List<PayCategoryConfig.ChannelPayCategory> channelPayCategories = payCategoryConfig.getChannelPayCategories();
        if (CollectionUtils.isEmpty(channelPayCategories)) {
            LoggerUtils.warn(log, "[PayCategorySwitchService] 渠道配置的支付方式为空");
            return new ArrayList<>(0);
        }
        // 过滤渠道
        PayCategoryConfig.ChannelPayCategory channelPayCategory = channelPayCategories.stream().filter(category -> requestDTO.getOrderChannel().equals(category.getOrderChannel()))
            .findFirst().orElse(null);
        if (channelPayCategory == null) {
            LoggerUtils.warn(log, "[PayCategorySwitchService] 渠道配置的支付方式为空, orderChannel:{}", requestDTO.getOrderChannel());
            return new ArrayList<>(0);
        }

        List<PayCategoryConfigDetail> payCategoryConfigDetails;
        // 无openId
        if (StringUtils.isBlank(requestDTO.getOpenId())) {
            payCategoryConfigDetails = channelPayCategory.getNoOpenIdPayCategories();
        } else {
            // 有openId
            payCategoryConfigDetails = channelPayCategory.getHasOpenIdPayCategories();
        }
        if (CollectionUtils.isEmpty(payCategoryConfigDetails)) {
            payCategoryConfigDetails = new ArrayList<>(0);
        }

        return payCategoryConfigDetails;
    }
}
