package com.ly.travel.car.tradecore.builder.activity;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.ly.flight.marketingcore.facade.dto.ValidateRequestDTO;
import com.ly.travel.car.tradecore.base.Builder;
import com.ly.travel.car.tradecore.base.ctx.Context;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.utils.ActivityUtils;
import com.ly.travel.car.tradecore.utils.Constants;

/**
 * ActivityValidateBuilder
 *
 * <AUTHOR>
 * @version Id: ActivityValidateBuilder.java, v 0.1 2024-02-29 18:59 ryan Exp $$
 */
@Service("activityValidateBuilder")
public class ActivityValidateBuilder implements Builder<Context, ValidateRequestDTO> {

    @Value("${token.activity.code}")
    private String token;

    /**
     * 构建
     *
     * @param context 原始输入
     * @return 构建出来目标对象
     * @throws BuilderException 构建异常
     */
    @Override
    public ValidateRequestDTO build(Context context) throws BuilderException {
        OrderVO order = context.getOrder();
        ValidateRequestDTO request = new ValidateRequestDTO();
        request.setSource(Constants.ACTIVITY_SOURCE);
        request.setPromotions(ActivityUtils.buildPromotions(order));
        request.setOrderNo(context.getOrderSerialNo());
        request.setUnionId(order.getMemberInfo().getUnionId());
        request.setOpenId(order.getMemberInfo().getOpenId());
        request.setMemberId(order.getMemberInfo().getId());
        request.setPhoneNum(order.getPassenger().getLinkPhone());
        request.setChannel(String.valueOf(order.getBookInfo().getOrderChannel()));
        request.setClientIp(order.getBookInfo().getEquipment().getClientIp());
        request.setTraceId(context.getTraceId());
        request.setTimestamp(System.currentTimeMillis());
        request.setToken(token);
        request.setCarParam(ActivityUtils.buildCarParam(order));
        return request;
    }
}
