package com.ly.travel.car.tradecore.service.price.model;

import java.util.List;

import com.ly.sof.utils.common.Money;
import com.ly.travel.car.tradecore.model.goods.TicketVO;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class CarPriceDetail {
    // 计算【用车费+附加费】
    // 如果是一口价全包，则真实用车销售价为用车下单的价格，否则为用车价 + 附加费
    private Money newRealCarPrice;

    // 计算【客人需实付价=用车费+附加费+限时优惠】
    // 一口价已加过限时优惠，所以不再加限时优惠
    private Money newRealPrice;

    // 计算【用车费】 不包含附加费
    // 基于调控计算出最新的对客用车价格
    private Money newCarPrice;

    // 计算【对客附加费】
    private Money attachPrice;

    // 调控后的对客用车费明细
    private List<TicketVO.FeeItem> feeItems;

    // 对客附加费明细
    private List<TicketVO.FeeItem> attachFeeItems;
    
    // 原始供应商费用项 (调控之前的)
    private List<TicketVO.FeeItem> originalFeeItems;

}
