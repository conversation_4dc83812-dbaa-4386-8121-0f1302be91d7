package com.ly.travel.car.tradecore.business.finished.model;

import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.mapping.JacksonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.travel.car.tradecore.base.ctx.AbstractContext;
import com.ly.travel.car.tradecore.model.OrderVO;

import com.ly.travel.car.tradecore.utils.DataMonitorUtils;
import com.ly.travel.car.tradecore.utils.MoneyUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * OrderTripFinishedContext
 *
 * <AUTHOR>
 * @version Id: OrderTripFinishedContext.java, v 0.1 2024-02-28 15:27 ryan Exp $$
 */
@Slf4j
@Getter
public class OrderTripFinishedContext extends AbstractContext {

    private final OrderFinishedSupplierBill details;

    public OrderTripFinishedContext(OrderVO order, OrderFinishedSupplierBill details) {
        super(order);
        this.details = details;
    }

    public static OrderTripFinishedContext of(OrderVO order, OrderFinishedSupplierBill details) {
        //交易与供应链完单金额diff日志监控
        amountDiffMonitor(order, details);

        return new OrderTripFinishedContext(order, details);
    }

    private static void amountDiffMonitor(OrderVO order, OrderFinishedSupplierBill details) {
        DataMonitorUtils.dataMonitorLog(order,
                JacksonUtils.toJSONString(details),
                DataMonitorUtils.DataMonitorEnum.SUPPLIER_FINISH_AMOUNT_DIFF);
        try {
            String config = StringUtils.defaultIfEmpty(ConfigCenterClient.get("supplier_finish_amount_diff_percent"), "10");
            if (MoneyUtils.isDifferenceExceedsPercentage(order.getAmount(), details.getTotalAmount(), new BigDecimal(config))) {
                log.warn("订单金额和供应商账单金额差异超过{}%," +
                                "订单号:{}," +
                                "订单金额:{}," +
                                "供应商账单金额:{}," +
                                "供应商账单明细:{}",
                        config,
                        order.getOrderSerialNo(),
                        order.getAmount(),
                        details.getTotalAmount(),
                        JacksonUtils.toJSONString(details));
            }
        }catch (Exception e) {
            log.error("[OrderTripFinishedContext][of] error:{}", e.getMessage());
        }
    }

    public Money getNewSettlePrice() {
        return details.getTotalAmount();
    }
}
