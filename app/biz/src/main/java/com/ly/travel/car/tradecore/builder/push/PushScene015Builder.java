package com.ly.travel.car.tradecore.builder.push;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.ly.travel.car.tradecore.base.ctx.Context;
import com.ly.travel.car.tradecore.model.enums.PushSmsScene;

/**
 * 订单完成-前付退差价
 * 
 * <AUTHOR>
 * @version Id: PushScene015Builder, v 0.1 2024/3/22 16:02 icanci Exp $
 */
@Service("pushScene015Builder")
public class PushScene015Builder extends PushBuilder {

    /**
     * 获取场景
     *
     * @return PushSmsScene
     */
    @Override
    public PushSmsScene getScene() {
        return PushSmsScene.SCENE_015;
    }

    /**
     * 脚本执行参数
     *
     * @param context context
     * @return 脚本执行参数
     */
    @Override
    protected Map<String, String> getPlaceHolder(Context context, PushSmsScene scene, Map<String, String> ext) {
        Map<String, String> placeHolder = super.getPlaceHolder(context, scene, ext);
        placeHolder.put("feeremark", ext.get("refundMoney"));
        return placeHolder;
    }
}