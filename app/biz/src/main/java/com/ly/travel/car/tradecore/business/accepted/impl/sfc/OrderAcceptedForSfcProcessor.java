package com.ly.travel.car.tradecore.business.accepted.impl.sfc;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.ly.travel.car.tradecore.business.accepted.impl.OrderAcceptedProcessor;
import com.ly.travel.car.tradecore.business.accepted.model.OrderAcceptedContext;
import com.ly.travel.car.tradecore.enums.PaymentPointTypeEnum;
import com.ly.travel.car.tradecore.integration.Symbols;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.*;
import com.ly.travel.car.tradecore.model.trip.ResourceVO;
import com.ly.travel.car.tradecore.order.OrderService;
import com.ly.travel.car.tradecore.service.ancillary.AncillaryService;
import com.ly.travel.car.tradecore.service.distribution.DistributionService;
import com.ly.travel.car.tradecore.service.free.ActivityFreeOrderService;
import com.ly.travel.car.tradecore.service.pay.PayService;
import com.ly.travel.car.tradecore.service.push.PushService;
import com.ly.travel.car.tradecore.service.rule.CancelRuleService;
import com.ly.travel.car.tradecore.service.tracer.TracerService;
import com.ly.travel.car.tradecore.utils.CfgUtils;
import com.ly.travel.car.tradecore.utils.PayUtils;

/**
 * 顺风车司机已接单处理器
 *
 * <AUTHOR>
 * @version Id: OrderAcceptedForSfcProcessor.java, v 0.1 2024-02-28 15:16 ryan Exp $$
 */
@Service("orderAcceptedForSfcProcessor")
public class OrderAcceptedForSfcProcessor implements OrderAcceptedProcessor {
    @Resource(name = "orderService")
    private OrderService             orderService;
    @Resource
    private TracerService            frontTracerService;
    @Resource(name = "payService")
    private PayService               payService;
    @Resource(name = "cancelRuleService")
    private CancelRuleService        cancelRuleService;
    @Resource
    private AncillaryService         ancillaryService;
    @Resource
    private PushService              pushService;

    @Resource
    private ActivityFreeOrderService activityFreeOrderService;

    @Resource
    private DistributionService      distributionService;

    /**
     * Process.
     *
     * @param context the context
     */
    @Override
    public void process(OrderAcceptedContext context) {
        OrderVO order = context.getOrder();
        ResourceVO acceptedResource = context.getResource();
        order.updateTicketActualResourceId(context.getDispatchType(),acceptedResource);
        // 接单成功，更新订单信息
        orderService.updateOrderForSfcAccepted(order, acceptedResource);
        // 延后支付标识
        AfterPay afterPay = AfterPay.getByCode(acceptedResource.getAfterPaySendPayNode());
        //原来的Resource
        List<ResourceVO> updateResource = Lists.newArrayList(acceptedResource);
        //实际接单的Resource 和 下单的不一样 就一起扔进去
        if (!StringUtils.isBlank(order.getTicket().getActualResourceId()) && !StringUtils.equals(order.getTicket().getActualResourceId(), acceptedResource.getResourceId())) {
            updateResource.add(order.getActualResource());
        }

        // 更新资源状态
        orderService.updateResourceInfo(order, updateResource, ResourceState.SUCCESS);

        // 免单活动打标
        activityFreeOrderService.addMadaOrderTag(order);

        //顺风车特惠车主接单要打Tag
        if (acceptedResource.getSfcAdditionalType() == SfcAdditionalType.CHEAP){
            orderService.updateOrderTag(order, OrderTag.SFC_SPECIAL_CHEAP_SUPPLY_ACCEPT_SUCCESS);
        }

        // 后付 转前付的时候也要买辅营
        boolean isPaymentPoint2Online = false;
        // 支付分支付，发起支付分扣款
        if (order.isPaymentPoint() && !afterPay.isAfter()) {

            PaymentPointTypeEnum paymentPointType = PayUtils.getPaymentPointType(order.getPayCategory(), order.getOrderTags());
            List<OrderTag> orderTags = PayUtils.initDeductCarFeeOrderTags(paymentPointType);

            boolean success = payService.deductPayOrderForPayScore(context);
            // 支付分扣款失败，更新支付方式
            if (!success) {
                isPaymentPoint2Online = true;
                orderService.switchPayCategory(context.getOrder(), PayCategory.ONLINE, "顺风车接单时支付，支付分扣款失败", Symbols.SYSTEM_NAME);
                // 添加支付分扣款失败的tag
                PayUtils.addDeductFailTag(orderTags, paymentPointType);
                if (CfgUtils.payScoreToOnlineSwitch()) {
                    // 更新支付分扣款失败时间
                    orderService.updatePayScoreDeductFailTime(order);
                }
            }
            if (CfgUtils.payScoreToOnlineSwitch()) {
                // 支付分扣款用车费用
                orderService.updateOrderTags(order, orderTags);
            }
        }
        // 通知用户已接单(和在线支付)
        frontTracerService.notify(order, UserSceneEnum.DRIVER_ACCEPT_NOTICE);
        // 保存取消规则快照
        cancelRuleService.save(context);

        // 如果 已经支付 ,后付 , 延后支付 发起辅营正式单创单
        if (order.isPaid() || order.isPaymentPoint() || isPaymentPoint2Online) {
            ancillaryService.book(context, null);
        }

        // 页面分销通知订单状态
        distributionService.notifyOrderStateForPageDistribution(order, OrderState.RECEIVING_ORDER, false);

        // 未支付并且是先付
        if (!order.isPaid() && order.isOnlinePay()) {
            // 发送推送
            pushService.push(order, PushSmsScene.SCENE_007);
        } else {
            // 发送推送，支付后者后付
            pushService.push(order, PushSmsScene.SCENE_006);
        }

        // 推送灵动岛
        pushService.pushIsland(order, null, PushIslandScene.DEFAULT);
    }
}
