package com.ly.travel.car.tradecore.business.book.strategy;

import com.ly.travel.car.tradecore.business.book.model.BaseBookContext;
import com.ly.travel.car.tradecore.business.strategy.OrderExecuteStrategy;
import com.ly.travel.car.tradecore.facade.response.trade.BookResponseDTO;

/**
 * @Description: 用车预定与下单策略
 * @Author: jay.he
 * @Date: 2024-08-05 09:59
 * @Version: 1.0
 **/
public interface OrderBookStrategy extends OrderExecuteStrategy<BaseBookContext, BookResponseDTO> {

    /**
     * 执行逻辑
     * @param context
     * @return
     */
    @Override
    default BookResponseDTO execute(BaseBookContext context) {
        return book(context);
    }

    /**
     * 订单下单
     *
     * @param context 订单上下文，包含下单所需的所有信息.
     * @return BookResponseDTO 包含订单处理结果的信息，包括成功状态和可能的错误信息.
     */
    BookResponseDTO book(BaseBookContext context);

}
