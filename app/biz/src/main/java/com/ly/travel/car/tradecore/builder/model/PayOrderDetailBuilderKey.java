package com.ly.travel.car.tradecore.builder.model;

import com.ly.travel.car.tradecore.facade.request.pay.PayDetailRequestDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

import com.ly.travel.car.tradecore.base.ctx.Context;
import com.ly.travel.car.tradecore.facade.request.pay.PayCheckRequestDTO;

import java.util.List;

/**
 * 支付详情构建key
 * 
 * <AUTHOR>
 * @version Id: PayOrderDetailBuilderKey, v 0.1 2024/3/8 10:24 icanci Exp $
 */
@Data
@AllArgsConstructor
public class PayOrderDetailBuilderKey {
    private Context      context;
    /** 订单 必填 */
    private String       orderSerialNo;
    /** 补差单号 非必传   */
    private String       balanceSerialNo;
    private List<String> memberIds;
}