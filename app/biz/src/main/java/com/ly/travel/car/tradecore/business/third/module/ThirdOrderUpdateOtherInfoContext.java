package com.ly.travel.car.tradecore.business.third.module;

import com.ly.travel.car.tradecore.base.ctx.AbstractContext;
import com.ly.travel.car.tradecore.facade.request.third.ThirdUpdateOtherInfoRequestDTO;
import com.ly.travel.car.tradecore.facade.request.third.ThirdUpdateRequestDTO;
import com.ly.travel.car.tradecore.model.OrderVO;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ThirdOrderUpdateOtherInfoContext extends AbstractContext {

    private ThirdUpdateOtherInfoRequestDTO requestDTO;

    public ThirdOrderUpdateOtherInfoContext(OrderVO order) {
        super(order);
    }

    public static ThirdOrderUpdateOtherInfoContext of(ThirdUpdateOtherInfoRequestDTO request) {
        ThirdOrderUpdateOtherInfoContext context = new ThirdOrderUpdateOtherInfoContext(null);
        context.setRequestDTO(request);
        return context;
    }
}
