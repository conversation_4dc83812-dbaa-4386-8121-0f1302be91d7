package com.ly.travel.car.tradecore.utils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import com.ly.travel.car.tradecore.model.enums.SupplierCarFeeType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.sof.utils.mapping.JacksonUtils;
import com.ly.travel.car.tradecore.business.finished.model.OrderFinishedSupplierBill;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.car.tradecore.model.enums.PayCategory;
import com.ly.travel.car.tradecore.model.enums.PayState;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class DataMonitorUtils {

    private static final String ModuleName = "DataMonitorModule";
    @Getter
    @AllArgsConstructor
    public enum DataMonitorEnum {
        ZERO_PAY_ORDER("ZERO_PAY_ORDER", "ZERO_PAY_ORDER"),
        CREATE_POINT_ERROR("CREATE_POINT_ERROR","CREATE_POINT_ERROR"),
        SFC_INTRIP_NO_PAY("SFC_INTRIP_NO_PAY","SFC_INTRIP_NO_PAY"),
        BIG_NEGATIVE_GOODS("BIG_NEGATIVE_GOODS","BIG_NEGATIVE_GOODS"),
        SUPPLIER_STATE_MISMATCH("SUPPLIER_STATE_MISMATCH","SUPPLIER_STATE_MISMATCH"),
        SUPPLIER_FINISH_AMOUNT_DIFF("SUPPLIER_FINISH_AMOUNT_DIFF","SUPPLIER_FINISH_AMOUNT_DIFF"),

        ;

        private final String category;
        private final String subCategory;
    }

    // 提取排除的费用类型为 Set 常量，便于扩展
    private static final Set<SupplierCarFeeType> EXCLUDED_TYPES = Sets.newHashSet(
            SupplierCarFeeType.SETTLE_TOTAL_AMOUNT,
            SupplierCarFeeType.COMMISSION
    );

    public static void dataMonitorLog(OrderVO orderVO, DataMonitorEnum dataMonitorEnum) {
        DataMonitorUtils.dataMonitorLog(orderVO, StringUtils.EMPTY, dataMonitorEnum);
    }

    public static void dataMonitorLog(OrderVO orderVO, String message, DataMonitorEnum dataMonitorEnum) {
        //备份出来
        String oldModule = LogContextUtils.getModule();
        String oldCategory = LogContextUtils.getCategory();
        String oldSubCategory = LogContextUtils.getSubCategory();
        try {
            //调整数据
            LogContextUtils.setModule(ModuleName);
            LogContextUtils.setCategory(dataMonitorEnum.getCategory());
            LogContextUtils.setSubCategory(dataMonitorEnum.getSubCategory());
            //根据场景写数据
            switch (dataMonitorEnum) {
                case ZERO_PAY_ORDER: {
                    zeroPayOrder(orderVO);
                    return;
                }
                case CREATE_POINT_ERROR: {
                    createPointError(orderVO, message);
                    return;
                }
                case SFC_INTRIP_NO_PAY: {
                    sfcInTripNoPay(orderVO);
                    return;
                }
                case SUPPLIER_FINISH_AMOUNT_DIFF: {
                    supplierFinishDiffAmount(orderVO, message);
                    return;
                }
                case BIG_NEGATIVE_GOODS: {
                    bigNegativeGoods(orderVO, message);
                    return;
                }
                case SUPPLIER_STATE_MISMATCH: {
                    supplierStateMismatch(orderVO, message);
                    return;
                }
            }
        }catch (Exception e){
            LoggerUtils.error(log, "数据监控异常，请联系研发排查:{}", e.getMessage(), e);
        }finally {
            //数据重新放回去
            LogContextUtils.setModule(oldModule);
            LogContextUtils.setCategory(oldCategory);
            LogContextUtils.setSubCategory(oldSubCategory);
        }
    }

    private static void supplierStateMismatch(OrderVO orderVO, String message){
        HashMap<String, String> logMap = new HashMap<String, String>();
        logMap.put("订单编号", orderVO.getOrderSerialNo());
        //供应链操作
        logMap.put("供应链操作", message);
        //订单状态
        logMap.put("订单状态", orderVO.getOrderState().getDesc());

        LoggerUtils.info(log, FastJsonUtils.toJSONString(logMap));
    }

    private static void bigNegativeGoods(OrderVO orderVO, String message) {
        HashMap<String, String> logMap = new HashMap<String, String>();
        logMap.put("订单编号", orderVO.getOrderSerialNo());
        //交易订单金额
        logMap.put("交易订单金额", orderVO.getAmount().getAmount().toPlainString());
        //车票实付销售价
        logMap.put("车票实付销售价", orderVO.getTicket().getRealPrice().getAmount().toPlainString());
        //车票应付销售价
        logMap.put("车票应付销售价", orderVO.getTicket().getSalePrice().getAmount().toPlainString());
        //抵扣金额
        logMap.put("抵扣金额", message);
        //订单状态
        logMap.put("订单状态", orderVO.getOrderState().getDesc());

        LoggerUtils.info(log, FastJsonUtils.toJSONString(logMap));
    }

    private static void supplierFinishDiffAmount(OrderVO orderVO, String message) {
        if (Objects.isNull(message)){
            return;
        }
        OrderFinishedSupplierBill billDetail = JacksonUtils.fromJSONString(message, OrderFinishedSupplierBill.class);
        if (Objects.nonNull(billDetail)) {
            HashMap<String, String> logMap = new HashMap<String, String>();
            logMap.put("订单编号", orderVO.getOrderSerialNo());
            //交易订单金额
            logMap.put("交易订单金额", orderVO.getAmount().getAmount().toPlainString());
            //车票实付销售价
            logMap.put("车票实付销售价", orderVO.getTicket().getRealPrice().getAmount().toPlainString());
            //供应链完单金额
            logMap.put("供应链完单金额", Optional.of(billDetail)
                    .map(OrderFinishedSupplierBill::getTotalAmount)
                    .map(amount -> amount.getAmount().toPlainString())
                    .orElse(""));
            //供应链完单费用项总金额(去除总额和佣金后累加)
            logMap.put("供应链完单费用项累加总金额", Optional.of(billDetail)
                    .map(OrderFinishedSupplierBill::getDetails)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(list -> list.stream()
                            .filter(detail -> !EXCLUDED_TYPES.contains(detail.getType()))
                            .map(detail -> new BigDecimal(detail.getAmount()))
                            .reduce(new BigDecimal(0), BigDecimal::add)
                    )
                    .map(BigDecimal::toPlainString)
                    .orElse("")
            );

            //供应链完单账单
            logMap.put("供应链完单账单", JacksonUtils.toJSONString(billDetail));
            //询价资源报价金额
            logMap.put("询价接单资源报价金额", Optional.ofNullable(orderVO.getTicketResource().getSalePrice())
                    .map(salePrice -> salePrice.getAmount().toPlainString())
                    .orElse(""));

            LoggerUtils.info(log, FastJsonUtils.toJSONString(logMap));
        }
    }

    private static void zeroPayOrder(OrderVO orderVO) {
        HashMap<String, String> logMap = new HashMap<String, String>();
        logMap.put("订单编号", orderVO.getOrderSerialNo());
        logMap.put("订单金额", orderVO.getAmount().getAmount().toPlainString());
        logMap.put("搜索券前价", orderVO.getTicketResource().getSalePrice().getAmount().toPlainString());
        logMap.put("搜索券后价", orderVO.getTicketResource().getFinalPrice().getAmount().toPlainString());
        logMap.put("订单标签", StringUtils.join(orderVO.getOrderTags(),","));
        logMap.put("支付方式", orderVO.getPayCategory().getDesc());
        logMap.put("里程抵扣",orderVO.getMileageAmount().toPlainString());
        BigDecimal price = orderVO.getTicketResource().getFinalPrice().getAmount().add(orderVO.getMileageAmount());
        logMap.put("创单券后价", price.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO.toPlainString() : price.toPlainString() );
        if (CollectionUtils.isNotEmpty(orderVO.getTicketResource().getRightDiscounts())){
            logMap.put("权益",StringUtils.join(orderVO.getTicketResource().getRightDiscounts().stream().map(rightDiscounts -> rightDiscounts.getSkuName() + "|" + rightDiscounts.getRightsDiscount().getAmount().toPlainString()).collect(Collectors.toList()),","));
        }
        if (CollectionUtils.isNotEmpty(orderVO.getAncillaries())){
            logMap.put("辅营",StringUtils.join(orderVO.getAncillaries().stream().map(ancillaryVO -> ancillaryVO.getName() + "|" + ancillaryVO.getSalePrice().getAmount().toPlainString()).collect(Collectors.toList()),","));
        }
        if (CollectionUtils.isNotEmpty(orderVO.getCoupons())){
            logMap.put("优惠券",StringUtils.join(orderVO.getCoupons().stream().map(couponVO -> couponVO.getName() + "|" + couponVO.getSalePrice().getAmount().toPlainString()).collect(Collectors.toList()),","));
        }
        if (CollectionUtils.isNotEmpty(orderVO.getActivities())){
            logMap.put("活动",StringUtils.join(orderVO.getActivities().stream().map(activityVO -> activityVO.getName() + "|" + activityVO.getSalePrice().getAmount().toPlainString()).collect(Collectors.toList()),","));
        }
        if (CollectionUtils.isNotEmpty(orderVO.getVirtualItems())){
            logMap.put("抵扣",StringUtils.join(orderVO.getVirtualItems().stream().map(virtualItemVO -> virtualItemVO.getName() + "|" + virtualItemVO.getSalePrice().getAmount().toPlainString()).collect(Collectors.toList()),","));
        }
        LoggerUtils.info(log, FastJsonUtils.toJSONString(logMap));
    }

    private static void createPointError(OrderVO orderVO, String errorMessage) {
        HashMap<String, String> logMap = new HashMap<String, String>();
        logMap.put("订单编号", orderVO.getOrderSerialNo());
        logMap.put("业务类型", orderVO.getOrderType().getDesc());
        logMap.put("会员ID", orderVO.getMemberInfo().getId());
        logMap.put("OpenId", orderVO.getMemberInfo().getOpenId());
        logMap.put("UnionId", orderVO.getMemberInfo().getUnionId());
        logMap.put("订单金额", orderVO.getAmount().getAmount().toPlainString());
        logMap.put("支付方式", orderVO.getPayCategory().getDesc());
        logMap.put("失败原因", errorMessage);
        LoggerUtils.info(log, FastJsonUtils.toJSONString(logMap));
    }

    private static void sfcInTripNoPay(OrderVO orderVO){
        // 分销订单，忽略
        if (orderVO.isDistributionOrder()){
            return;
        }
        // 非前付忽略
        if (orderVO.getPayCategory() != PayCategory.ONLINE){
            return;
        }
        // 非未支付，忽略
        if (orderVO.getPayState() != PayState.UNPAID){
            return;
        }
        // 非行程中 忽略
        if (orderVO.getOrderState() != OrderState.AWAITING_TRAVEL
                && orderVO.getOrderState() != OrderState.IN_TRIP
                && orderVO.getOrderState() != OrderState.TRIP_FINISHED){
            return;
        }
        HashMap<String, String> logMap = new HashMap<String, String>();
        logMap.put("订单编号", orderVO.getOrderSerialNo());
        logMap.put("订单状态", orderVO.getOrderState().getDesc());
        logMap.put("业务类型", orderVO.getOrderType().getDesc());
        logMap.put("订单金额", orderVO.getAmount().getAmount().toPlainString());
        logMap.put("支付方式", orderVO.getPayCategory().getDesc());
        logMap.put("创单支付方式", Objects.nonNull(orderVO.getBookPayCategory()) ? orderVO.getBookPayCategory().getDesc() : "无");
        LoggerUtils.info(log, FastJsonUtils.toJSONString(logMap));
    }
}
