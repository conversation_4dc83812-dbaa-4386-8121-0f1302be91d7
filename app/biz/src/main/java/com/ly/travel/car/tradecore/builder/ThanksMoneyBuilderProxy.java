package com.ly.travel.car.tradecore.builder;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.ly.travel.car.tradecore.builder.thanksmoney.ThanksMoneyBuilder;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.facade.request.append.AppendThanksMoneyRequestDTO;
import com.ly.travel.car.tradecore.model.goods.ThanksMoneyVO;

/**
 * <AUTHOR>
 * @version Id:ThanksMoneyBuilderProxy.java,v 0.1 2024/3/8 21:09 chengweiwen Exp $$
 */
@Service
public class ThanksMoneyBuilderProxy {

    @Resource
    private ThanksMoneyBuilder thanksMoneyBuilder;

    /**
     * 构建加速匹配服务(感谢金)
     *
     * @param requestDTO 追加加速匹配服务(感谢金)请参
     * @return {@link ThanksMoneyVO}
     * @throws BuilderException 异常
     */
    public ThanksMoneyVO doBuildThanksMoney(AppendThanksMoneyRequestDTO requestDTO) throws BuilderException {
        return thanksMoneyBuilder.build(requestDTO);
    }
}
