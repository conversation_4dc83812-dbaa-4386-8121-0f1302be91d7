package com.ly.travel.car.tradecore.business.append.car;

import com.ly.travel.car.tradecore.business.append.car.model.AppendRet;
import com.ly.travel.car.tradecore.facade.request.append.AppendCarRequestDTO;

/**
 * AppendCarService
 *
 * <AUTHOR>
 * @version Id: AppendCarService.java, v 0.1 2024-02-29 15:01 ryan Exp $$
 */
public interface AppendCarService {

    /**
     * 车型追加
     *
     * @param request the request
     * @return the append goods response dto
     */
    AppendRet append(AppendCarRequestDTO request);
}
