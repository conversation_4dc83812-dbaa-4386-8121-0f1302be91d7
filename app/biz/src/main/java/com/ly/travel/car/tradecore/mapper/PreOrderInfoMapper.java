package com.ly.travel.car.tradecore.mapper;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueMappingStrategy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.sof.utils.mapping.JacksonUtils;
import com.ly.travel.car.common.model.enums.CarTypeEnum;
import com.ly.travel.car.common.model.enums.ChargeTypeEnum;
import com.ly.travel.car.common.model.enums.ServiceType;
import com.ly.travel.car.common.model.enums.SupplierChargeTypeEnum;
import com.ly.travel.car.common.utils.DateUtils;
import com.ly.travel.car.tradecore.dal.dataobject.*;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.cvt.DateConverter;
import com.ly.travel.car.tradecore.model.cvt.DefaultLongConverter;
import com.ly.travel.car.tradecore.model.cvt.EnumConverter;
import com.ly.travel.car.tradecore.model.enums.*;
import com.ly.travel.car.tradecore.model.ext.OrderExtVO;
import com.ly.travel.car.tradecore.model.ext.ResourceExtVO;
import com.ly.travel.car.tradecore.model.order.BookInfoVO;
import com.ly.travel.car.tradecore.model.order.EquipmentVO;
import com.ly.travel.car.tradecore.model.order.MemberInfoVO;
import com.ly.travel.car.tradecore.model.passenger.PassengerVO;
import com.ly.travel.car.tradecore.model.pay.PaySerialInfoVO;
import com.ly.travel.car.tradecore.model.refund.RefundInfoVO;
import com.ly.travel.car.tradecore.model.trip.AddressVO;
import com.ly.travel.car.tradecore.model.trip.ResourceVO;
import com.ly.travel.car.tradecore.model.trip.TripInfoVO;
import com.ly.travel.car.tradecore.model.util.DateExtUtils;
import com.ly.travel.car.tradecore.utils.CfgUtils;
import com.ly.travel.car.tradecore.utils.Constants;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: VO与DO转换
 * @Author: jay.he
 * @Date: 2024-08-08 09:55
 * @Version: 1.0
 **/
@Slf4j
@Mapper(componentModel = "spring", uses = {DateConverter.class, DefaultLongConverter.class,
        EnumConverter.class}, nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public abstract class PreOrderInfoMapper {

    @Resource
    protected OrderInfoMapper orderInfoMapper;

    /**
     * OrderVO转预约单DO
     *
     * @param order
     * @return
     */
    public PreOrderInfoDO vo2do(OrderVO order) {
        PreOrderInfoDO orderInfo = new PreOrderInfoDO();
        orderInfo.setOrderSerialNo(order.getOrderSerialNo());
        orderInfo.setPayState(order.getPayState().getCode());
        orderInfo.setOrderState(order.getOrderState().getCode());
        orderInfo.setUserState(order.getUserState().getCode());
        orderInfo.setOrderChannel(order.getBookInfo().getOrderChannel());
        orderInfo.setOrderType(order.getOrderType().getCrmType());
        orderInfo.setOrderTags(JacksonUtils.toJSONString(order.getOrderTags()));
        orderInfo.setContactName(StringUtils.defaultString(order.getPassenger().getName()));
        orderInfo.setContactPhone(getContactPhone(order.getPassenger()));
        orderInfo.setAreaCode(order.getPassenger().getAreaCode());
        orderInfo.setUnionId(order.getMemberInfo().getUnionId());
        orderInfo.setOpenId(order.getMemberInfo().getOpenId());
        orderInfo.setMemberId(order.getMemberInfo().getId());
        orderInfo.setClientIp(order.getBookInfo().getEquipment().getClientIp());
        orderInfo.setDeviceId(order.getBookInfo().getEquipment().getDeviceId());
        orderInfo.setGmtPaid(order.getGmtPaid());
        orderInfo.setGmtCanceled(order.getGmtCanceled());
        orderInfo.setGmtUsage(order.getGmtUsage());
        orderInfo.setUsageDelay(order.getUsageDelay());
        orderInfo.setGmtPayOut(order.getGmtUsage());

        orderInfo.setCancelType(order.getCancelType().getCode());
        orderInfo.setCancelReason(order.getCancelReason());
        orderInfo.setTraceId(order.getTraceId());
        orderInfo.setAmount(order.getAmountYuan().doubleValue());
        orderInfo.setReceiveChannel(order.getReceiveChannel().getReceiveChannel());
        orderInfo.setPayCategory(order.getPayCategory().getCode());
        orderInfo.setRefId(order.getBookInfo().getRefId());
        orderInfo.setSmsScene(StringUtils.EMPTY);
        orderInfo.setIsDelete(BooleanInt.N.getCode());
        orderInfo.setCreateTime(order.getCreateTime());
        orderInfo.setUpdateTime(order.getUpdateTime());
        orderInfo.setExt(JacksonUtils.toJSONString(order.getOrderExtVO()));
        orderInfo.setEnv(CfgUtils.getEnv());
        orderInfo.setTrafficNo(getTrafficNo(order));

        //复合信息-json存储    --- 保存前有单独流程，处理复合信息
        orderInfo.setCarSign(order.getSign());
        orderInfo.setReqInfo(order.getReqInfo());
        return orderInfo;
    }

    /**
     * 预约单DO转订单VO
     *
     * @param order
     * @return
     */
    public OrderVO buildPreOrder(PreOrderInfoDO order, SegmentInfoDO segmentInfo,List<PayInfoDO> payInfos, List<RefundInfoDO> refundInfos) throws IOException {
        OrderExtVO orderExt = JacksonUtils.fromJSONString(order.getExt(), OrderExtVO.class);
        PassengerInfoDO passengerInfo = JacksonUtils.fromJSONString(order.getCarPassengerInfo(), PassengerInfoDO.class);
        List<ResourceInfoDO> resourceInfos = JacksonUtils.fromJSON2List(order.getCarResourceInfo(), ResourceInfoDO.class);

        OrderType orderType = OrderType.getByCrmType(order.getOrderType());
        OrderVO.OrderVOBuilder<?, ?> builder = OrderVO.builder();
        builder.receiveChannel(ReceiveChannel.getByReceiveChannel(order.getReceiveChannel(), orderType));

        builder.remark(orderExt.getRemark());
        builder.traceId(order.getTraceId());
        builder.orderSerialNo(order.getOrderSerialNo());

        builder.orderType(orderType);
        builder.orderState(OrderState.getByCode(order.getOrderState()));
        builder.userState(UserState.getByCode(order.getUserState()));
        builder.orderTags(FastJsonUtils.fromJSON2List(order.getOrderTags(), String.class));
        builder.amount(new Money(order.getAmount()));

        builder.userAmount(new Money(StringUtils.isBlank(orderExt.getUserAmount()) ? String.valueOf(order.getAmount()) : orderExt.getUserAmount()));
        builder.bookUserAmount(new Money(StringUtils.isBlank(orderExt.getBookUserAmount()) ? "0" : orderExt.getBookUserAmount()));
        builder.smsScene(order.getSmsScene());
        builder.payCategory(PayCategory.getByCode(order.getPayCategory()));
        builder.bookPayCategory(PayCategory.getByCode(order.getPayCategory()));
        builder.payState(PayState.getByCode(order.getPayState()));
        builder.gmtPaid(order.getGmtPaid());
        builder.gmtCanceled(order.getGmtCanceled());
        builder.gmtUsage(order.getGmtUsage());
        builder.usageDelay(order.getUsageDelay());
        builder.gmtPayOut(order.getGmtPayOut());

        builder.createTime(order.getCreateTime());
        builder.updateTime(order.getUpdateTime());

        builder.cancelType(CancelType.getByRealCodeOrCode(orderExt.getCancelTypeRealCode(), order.getCancelType()));
        builder.cancelReason(order.getCancelReason());
        builder.cancelReasonType(CancelReasonType.getByCode(orderExt.getCancelReasonType()));


        //预约单不存在的数据含义，默认值即可
        builder.reassignNum(0);
        builder.gmtDispatch(Constants.DEFAULT_DATE);
        builder.gmtDispatched(Constants.DEFAULT_DATE);
        builder.gmtReassigned(Constants.DEFAULT_DATE);
        builder.gmtDeparture(Constants.DEFAULT_DATE);
        builder.gmtArrive(Constants.DEFAULT_DATE);
        builder.gmtDriverArrived(Constants.DEFAULT_DATE);
        builder.gmtTripFinished(Constants.DEFAULT_DATE);
        builder.gmtPayScoreDeduct(Constants.DEFAULT_DATE);
        builder.gmtPassengerBoard(Constants.DEFAULT_DATE);
        builder.gmtPayScoreDeduct(Constants.DEFAULT_DATE);

        builder.bookInfo(buildBookInfo(order, orderExt));
        builder.memberInfo(buildMemberInfo(order));
        builder.tripInfo(buildTripInfo(order.getGmtUsage(), order.getUsageDelay(), segmentInfo, resourceInfos, orderType, orderExt));
        builder.passenger(buildPassengerInfo(order, passengerInfo, orderExt));


        builder.coupons(Lists.newArrayList());
        builder.activities(Lists.newArrayList());
        builder.ancillaries(Lists.newArrayList());
        builder.thanksMoneys(Lists.newArrayList());
        builder.virtualItems(Lists.newArrayList());
        builder.discounts(Lists.newArrayList());

        builder.supportInvoice(orderExt.isSupportInvoice());
        builder.tcInvoicingCode(orderExt.getTcInvoicingCode());
        builder.tcInvoicingName(orderExt.getTcInvoicingName());
        builder.latestCancelTime(orderExt.getLatestCancelTime());

        if (StringUtils.isBlank(orderExt.getPayScoreDeductFailTime())) {
            builder.payScoreDeductFailTime(Constants.DEFAULT_DATE);
        } else {
            builder.payScoreDeductFailTime(DateUtils.parseY4M2d2H2m2s2WebString(orderExt.getPayScoreDeductFailTime()));
        }
        //支付分创建成功时间
        builder.gmtPayScoreCreated(DateExtUtils.parseY4M2d2H2m2s2WebString(orderExt.getGmtPayScoreCreated()));
        builder.rights(Lists.newArrayList());
        builder.freeCancelTime(orderExt.getFreeCancelTime());
        builder.oriLinkPhone(orderExt.getOriLinkPhone());

        //预约单特有信息
        builder.sign(order.getCarSign());
        builder.reqInfo(order.getReqInfo());
        builder.preOrder(true);
        builder.bizSceneType(BizSceneType.getByCode(orderExt.getBizSceneType()));
        builder.carBasicInfo(orderExt.getCarBasicInfo());
        builder.orderChargeType(orderExt.getOrderChargeType());
        builder.priceValidateNoDiscounts(orderExt.isPriceValidateNoDiscounts());
        builder.displayMerchants(orderExt.isDisplayMerchants());
        builder.blackBox(orderExt.isBlackBox());
        builder.tripType(TripType.getByCode(orderExt.getTripType()));

        //修改目的地新增
        builder.allCanChangeDepartureNum(orderExt.getAllCanChangeDepartureNum());
        builder.allCanChangeArrivalNum(orderExt.getAllCanChangeArrivalNum());
        builder.allCanChangeStopoverNum(orderExt.getAllCanChangeStopoverNum());
        builder.alreadyChangeDepartureNum(orderExt.getAlreadyChangeDepartureNum());
        builder.alreadyChangeArrivalNum(orderExt.getAlreadyChangeArrivalNum());
        builder.alreadyChangeStopoverNum(orderExt.getAlreadyChangeStopoverNum());

        // 构建item、商品条目列表
        buildItemInfo(builder, order, passengerInfo, segmentInfo);

        List<PaySerialInfoVO> paySerialInfos = buildPaySerials(payInfos);
        builder.paySerialInfos(paySerialInfos);
        builder.insSplit(paySerialInfos.stream().anyMatch(payInfo -> payInfo.getIsIns() == 1));
        builder.regulatorFlag(paySerialInfos.stream().anyMatch(payInfo -> payInfo.getRegulatorFlag() == 1));
        builder.userPendingPayInfos(Lists.newArrayList());
        builder.refundInfos(buildRefundInfos(refundInfos));
        builder.sourceId(orderExt.getSourceId());
        builder.searchTraceId(orderExt.getSearchTraceId());

        OrderVO orderVO = builder.build();
        LoggerUtils.info(log, "订单查询:预约单订单状态:{},支付状态:{}", orderVO.getOrderState(), orderVO.getPayState());
        return orderVO;
    }

    private void buildItemInfo(OrderVO.OrderVOBuilder<?, ?> builder, PreOrderInfoDO order, PassengerInfoDO passengerInfo, SegmentInfoDO segmentInfo) throws IOException {
        List<ItemInfoDO> itemDetails = JacksonUtils.fromJSON2List(order.getCarItemInfo(), ItemInfoDO.class);
        // 构建item、商品条目列表
        Map<Integer, Map<String, ItemInfoDO>> itemsMap = itemDetails.stream().collect(Collectors.toMap(ItemInfoDO::getBusinessType, v -> {
            Map<String, ItemInfoDO> items = Maps.newHashMap();
            items.put(v.getItemId(), v);
            return items;
        }, (k1, k2) -> {
            k1.putAll(k2);
            return k1;
        }));
        LoggerUtils.info(log, "订单查询:itemsMap:{}", FastJsonUtils.toJSONString(itemsMap));
        List<OpsiRelationDO> opsiRelations = buildPreOpsiRelationsDO(order, itemDetails, Lists.newArrayList(passengerInfo), Lists.newArrayList(segmentInfo));
        for (Map.Entry<Integer, Map<String, ItemInfoDO>> entry : itemsMap.entrySet()) {
            BusinessType businessType = BusinessType.getByCode(entry.getKey());
            Map<String, ItemInfoDO> items = entry.getValue();
            List<OpsiRelationDO> psis = opsiRelations.stream().filter(v -> items.containsKey(v.getItemId())).collect(Collectors.toList());
            LoggerUtils.info(log, "订单查询:businessType:{} psis:{}", businessType, FastJsonUtils.toJSONString(psis));
            switch (businessType) {
                case TICKET:
                    builder.ticket(orderInfoMapper.buildTickets(items, psis));
                    break;
                case COUPON:
                    builder.coupons(orderInfoMapper.buildCoupons(items, psis));
                    break;
                case MILEAGE:
                    builder.mileage(orderInfoMapper.buildMileages(items, psis));
                    break;
                case ANCILLARY:
                    builder.ancillaries(orderInfoMapper.buildAncillaries(items, psis));
                    break;
                case VIRTUAL:
                    builder.virtualItems(orderInfoMapper.buildVirtualItems(items, psis));
                    break;
                case ACTIVITY:
                    builder.activities(orderInfoMapper.buildActivities(items, psis));
                    break;
                case DISCOUNT:
                    builder.discounts(orderInfoMapper.buildDiscounts(items, psis));
                    break;
                case RIGHT_DISCOUNT:
                    builder.rights(orderInfoMapper.buildRightDiscounts(items, psis));
                    break;
                default:
                    break;
            }
        }
    }

    private void buildPassenger(OrderVO.OrderVOBuilder<?, ?> builder, PreOrderInfoDO order, PassengerInfoDO passengerInfo, OrderExtVO orderExt) {
        builder.passenger(buildPassengerInfo(order, passengerInfo, orderExt));
    }

    private void buildTripInfo(OrderVO.OrderVOBuilder<?, ?> builder, PreOrderInfoDO order, SegmentInfoDO segmentInfo, OrderExtVO orderExt) throws IOException {
        List<ResourceInfoDO> resourceInfos = JacksonUtils.fromJSON2List(order.getCarResourceInfo(), ResourceInfoDO.class);
        OrderType orderType = OrderType.getByCrmType(order.getOrderType());
        builder.tripInfo(buildTripInfo(order.getGmtUsage(), order.getUsageDelay(), segmentInfo, resourceInfos, orderType, orderExt));
    }

    /**
     * 构建预定信息
     *
     * @param order order
     * @param extVO extVO
     * @return 预定信息
     */
    private BookInfoVO buildBookInfo(PreOrderInfoDO order, OrderExtVO extVO) {
        BookInfoVO.BookInfoVOBuilder<?, ?> builder = BookInfoVO.builder();
        builder.refId(order.getRefId());
        builder.orderChannel(order.getOrderChannel());
        builder.traceId(order.getTraceId());
        builder.traceAppId(extVO.getTraceAppId());
        builder.traceWallet(extVO.getTraceWallet());
        builder.traceWxAppScene(extVO.getTraceWxAppScene());
        builder.equipment(EquipmentVO.builder()//
                .clientIp(order.getClientIp())//
                .deviceId(order.getDeviceId())//
                .build());

        AddressVO address = extVO.getAddress() == null ? AddressVO.builder().build() : extVO.getAddress();
        if (address != null) {
            builder.address(AddressVO.builder()//
                    .cityCode(address.getCityCode())//
                    .cityName(address.getCityName())//
                    .address(address.getAddress())//
                    .name(address.getName())//
                    .fullAddress(address.getFullAddress())//
                    .lat(address.getLat())//
                    .lon(address.getLon())//
                    .poi(address.getPoi())//
                    .build());
        }

        builder.flightInfo(extVO.getFlightInfo());
        builder.trainInfo(extVO.getTrainInfo());
        return builder.build();
    }

    /**
     * 构建会员信息
     *
     * @param order order
     * @return 会员信息
     */
    private MemberInfoVO buildMemberInfo(PreOrderInfoDO order) {
        MemberInfoVO.MemberInfoVOBuilder<?, ?> builder = MemberInfoVO.builder();
        builder.openId(order.getOpenId());
        builder.id(order.getMemberId());
        builder.unionId(order.getUnionId());
        return builder.build();
    }

    /**
     * 联系人信息
     *
     * @param passenger passenger
     * @return 联系人信息
     */
    private String getContactPhone(PassengerVO passenger) {
        return StringUtils.isBlank(passenger.getPronounLinkPhone()) ? passenger.getLinkPhone() : passenger.getPronounLinkPhone();
    }

    /**
     * Gets traffic no.
     *
     * @param order the order
     * @return the traffic no
     */
    private String getTrafficNo(OrderVO order) {
        if (order.getBookInfo().getFlightInfo() != null) {
            return StringUtils.defaultString(order.getBookInfo().getFlightInfo().getFlightNo());
        }
        if (order.getBookInfo().getTrainInfo() != null) {
            return StringUtils.defaultString(order.getBookInfo().getTrainInfo().getTrainNo());
        }
        return StringUtils.EMPTY;
    }

    /**
     * 构建乘客信息
     *
     * @param orderInfoDO   the order info do
     * @param passengerInfo passengerInfo
     * @param orderExt      orderExt
     * @return 乘客信息 passenger vo
     */
    private PassengerVO buildPassengerInfo(PreOrderInfoDO orderInfoDO, PassengerInfoDO passengerInfo, OrderExtVO orderExt) {
        PassengerVO.PassengerVOBuilder<?, ?> builder = PassengerVO.builder();
        builder.id(passengerInfo.getPassengerId());
        builder.orderSerialNo(passengerInfo.getOrderSerialNo());
        builder.name(passengerInfo.getName());
        builder.linkPhone(passengerInfo.getLinkPhone());
        builder.virtualPhone(passengerInfo.getVirtualPhone());
        builder.areaCode(passengerInfo.getAreaCode());
        builder.passengerCount(passengerInfo.getPassengerCount());
        builder.contactPhoneSuffix(orderExt.getContactPhoneSuffix());
        builder.needVirtualPhone(orderExt.getNeedVirtualPhone());
        builder.modifyTimes(passengerInfo.getModifyTimes());
        return builder.build();
    }


    /**
     * 构建行程信息
     *
     * @param gmtUsage      gmtUsage
     * @param usageDelay    usageDelay
     * @param segmentInfo   segmentInfo
     * @param resourceInfos resourceInfos
     * @return 行程信息
     */
    private TripInfoVO buildTripInfo(Date gmtUsage, int usageDelay, SegmentInfoDO segmentInfo, List<ResourceInfoDO> resourceInfos, OrderType orderType, OrderExtVO orderExt) {
        TripInfoVO.TripInfoVOBuilder<?, ?> builder = TripInfoVO.builder();
        builder.id(segmentInfo.getSegmentId());
        builder.orderSerialNo(segmentInfo.getOrderSerialNo());
        builder.gmtDeparture(segmentInfo.getGmtDeparture());
        builder.gmtArrive(segmentInfo.getGmtArrive());
        builder.gmtUsage(gmtUsage);
        builder.usageDelay(usageDelay);
        builder.bizType(orderExt.getBizType());
        builder.depAddress(AddressVO.builder()//
                .cityCode(segmentInfo.getDepartureCityCode())//
                .cityName(segmentInfo.getDepartureCity())//
                .name(segmentInfo.getDepartureName())//
                .address(segmentInfo.getDepartureAddress())//
                .fullAddress(segmentInfo.getDepartureFullAddress())//
                .lat(segmentInfo.getDepLat())//
                .lon(segmentInfo.getDepLon())//
                .poi(segmentInfo.getDepPoi())//
                .build());
        builder.arrAddress(AddressVO.builder()//
                .cityCode(segmentInfo.getArrivalCityCode())//
                .cityName(segmentInfo.getArrivalCity())//
                .name(segmentInfo.getArrivalName())//
                .address(segmentInfo.getArrivalAddress())//
                .fullAddress(segmentInfo.getArrivalFullAddress())//
                .lat(segmentInfo.getArrLat())//
                .lon(segmentInfo.getArrLon())//
                .poi(segmentInfo.getArrPoi())//
                .build());
        builder.resources(buildResources(resourceInfos, orderType, orderExt));
        return builder.build();
    }

    /**
     * 构建资源信息
     *
     * @param resourceInfos resourceInfos
     * @return 资源信息
     */
    private List<ResourceVO> buildResources(List<ResourceInfoDO> resourceInfos, OrderType orderType, OrderExtVO orderExt) {
        List<ResourceVO> resources = Lists.newArrayListWithCapacity(resourceInfos.size());
        for (ResourceInfoDO resourceInfo : resourceInfos) {
            ResourceExtVO resourceExt = FastJsonUtils.fromJSONString(resourceInfo.getExt(), ResourceExtVO.class);
            resources.add(ResourceVO.builder()//
                    .state(ResourceState.getByCode(resourceInfo.getStatus()))//
                    .chooseType(CarChooseType.getByCode(resourceInfo.getChooseType()))//
                    .orderSerialNo(resourceInfo.getOrderSerialNo())//
                    .salePrice(new Money(resourceInfo.getSalePrice()))//
                    .supplierPrice(new Money(resourceInfo.getSupplierPrice()))//
                    .carType(CarTypeEnum.of(resourceExt.getCar().getCarType()))//
                    .resourceId(resourceInfo.getResourceId())//
                    .carTags(resourceExt.getCar().getCarTags())//
                    .carNum(resourceExt.getCar().getCarNum())//
                    .originalCarNum(resourceExt.getCar().getOriginalCarNum())//
                    .brand(resourceExt.getCar().getBrand())//
                    .color(resourceExt.getCar().getColor())//
                    .driverName(resourceExt.getDriver().getDriverName())//
                    .driverPhone(resourceExt.getDriver().getDriverPhone())//
                    .driverCode(resourceExt.getDriver().getDriverCode())//
                    .driverVirtualPhone(resourceExt.getDriver().getDriverVirtualPhone())//
                    .passengerVirtualPhone(resourceExt.getPassengerVirtualPhone())//
                    .pronounVirtualLinkPhone(resourceExt.getPronounVirtualLinkPhone())//
                    .driverChatCode(resourceExt.getDriver().getDriverChatCode())//
                    .level(resourceExt.getDriver().getLevel())//
                    .wechat(resourceExt.getDriver().getWechat())//
                    .supplierId(resourceInfo.getSupplierId())//
                    .supplierOrderSerialNo(resourceInfo.getSupplierOrderSerialNo())//
                    .merchantOrderNo(resourceExt.getMerchantOrderNo())//
                    .supplierCode(resourceInfo.getSupplierCode())//
                    .supplierName(resourceInfo.getSupplierName())//
                    .coupons(orderInfoMapper.buildResourceCoupons(resourceExt.getCoupons()))//
                    .discounts(orderInfoMapper.buildResourceDiscounts(resourceExt.getDiscounts()))//
                    .resourceType(ResourceType.getByCode(resourceInfo.getResourceType()))//
                    .supplierTags(resourceExt.getSupplierTags())//
                    .chargeType(ChargeTypeEnum.of(resourceExt.getChargeType()))//
                    .supplierChargeType(SupplierChargeTypeEnum.of(resourceExt.getSupplierChargeType()))//
                    .runDistance(resourceExt.getRunDistance())//
                    .runTime(resourceExt.getRunTime())//
                    .isInterlinkOrder(resourceExt.isInterlinkOrder()) //
                    .interlinkOrderInfo(resourceExt.getInterlinkOrderInfo())//
                    .receiveChannel(ReceiveChannel.getByReceiveChannel(resourceExt.getReceiveChannel(), orderType))//
                    .supportInvoice(resourceExt.isSupportInvoice())//
                    .tcInvoicingName(resourceExt.getTcInvoicingName())//
                    .tcInvoicingCode(resourceExt.getTcInvoicingCode())//
                    .userChosen(resourceExt.getUserChosen())//
                    .gmtLastConfirm(resourceExt.getGmtLastConfirm())//
                    .iconInfo(resourceExt.getIconInfo())//
                    .serviceType(ServiceType.of(resourceExt.getServiceType()))//
                    .priceMark(resourceExt.getPriceMark())//
                    .seats(resourceExt.getSeats())//
                    .driverArrDistance(resourceExt.getDriver().getDriverArrDistance())//
                    .driverArrTime(resourceExt.getDriver().getDriverArrTime())//
                    .packResourceId(resourceInfo.getPackResourceId())//
                    .isBlackBox(resourceExt.isBlackBox())//
                    .showName(resourceInfo.getShowName())//
                    .showSalePrice(new Money(resourceInfo.getPackSalePrice()))//
                    .packCoupons(orderInfoMapper.buildResourceCoupons(resourceExt.getPackCoupons()))//
                    .packDiscounts(orderInfoMapper.buildResourceDiscounts(resourceExt.getPackDiscounts()))//
                    .rightDiscounts(orderInfoMapper.buildResourceRightDiscountsForPreOrder(resourceExt.getRightDiscounts()))//
                    .resourceBasePriceRule(orderInfoMapper.buildResourceBasePriceRuleVO(resourceExt.getBasePriceRule()))//构建基础价格快照
                    .maxReturn(resourceExt.getCashBackInfoExt() != null ? resourceExt.getCashBackInfoExt().getMaxReturn() : StringUtils.EMPTY)
                    .promotionCode(resourceExt.getCashBackInfoExt() != null ? resourceExt.getCashBackInfoExt().getPromotionCode() : StringUtils.EMPTY)
                    .vehicleCode(resourceExt.getCar().getVehicleCode())
                    .configCode(resourceExt.getCashBackInfoExt() != null ? resourceExt.getCashBackInfoExt().getConfigCode() : StringUtils.EMPTY)//
                    .cashBackPrice(resourceExt.getCashBackInfoExt() != null ? resourceExt.getCashBackInfoExt().getCashBackPrice() : BigDecimal.ZERO)//
                    .donateActivityAmount(StringUtils.isBlank(resourceExt.getDonateActivityAmount()) ? new Money() : new Money(resourceExt.getDonateActivityAmount()))//
                    .searchRightBill(resourceExt.getSearchRightBill()) //
                    .orderChargeType(orderExt.getOrderChargeType())//
                    .searchSceneCode(resourceExt.getSearchSceneCode()) //
                    .build());
        }
        return resources;
    }


    /**
     * 构建预约单opsi关联
     *
     * @param order
     * @param itemDOs
     * @param passengerDOs
     * @param segmentDOs
     * @return
     */
    private List<OpsiRelationDO> buildPreOpsiRelationsDO(PreOrderInfoDO order, List<ItemInfoDO> itemDOs, List<PassengerInfoDO> passengerDOs,
                                                         List<SegmentInfoDO> segmentDOs) {
        List<OpsiRelationDO> opsiRelations = Lists.newArrayList();

        PassengerInfoDO passenger = passengerDOs.stream().findFirst().orElse(null);
        SegmentInfoDO segment = segmentDOs.stream().findFirst().orElse(null);
        for (ItemInfoDO item : itemDOs) {
            opsiRelations.add(buildPreOrderOpsi(order, passenger, segment, item));
        }
        return opsiRelations;
    }

    /**
     * 构建预约单opsi关联信息
     *
     * @param order     order
     * @param passenger passenger
     * @param segment   segment
     * @param ticket    ticket
     * @return opsi关联信息
     */
    private OpsiRelationDO buildPreOrderOpsi(PreOrderInfoDO order, PassengerInfoDO passenger, SegmentInfoDO segment, ItemInfoDO ticket) {
        OpsiRelationDO opsiRelation = new OpsiRelationDO();
        opsiRelation.setOrderSerialNo(order.getOrderSerialNo());
        opsiRelation.setPassengerId(passenger.getPassengerId());
        opsiRelation.setSegmentId(segment.getSegmentId());
        opsiRelation.setItemId(ticket.getItemId());
        opsiRelation.setDealState(GoodsState.PENDING.getDealState());
        opsiRelation.setDealType(GoodsState.PENDING.getDealType());
        opsiRelation.setRelationId(0);
        opsiRelation.setTraceId(order.getTraceId());
        opsiRelation.setMemberId(order.getMemberId());
        opsiRelation.setIsDelete(BooleanInt.N.getCode());
        opsiRelation.setCreateTime(new Date());
        opsiRelation.setUpdateTime(new Date());
        opsiRelation.setEnv(CfgUtils.getEnv());
        return opsiRelation;
    }

    /**
     * 构建退款信息
     *
     * @param refundInfos refundInfos
     * @return 退款信息
     */
    private List<RefundInfoVO> buildRefundInfos(List<RefundInfoDO> refundInfos) {
        if (CollectionUtils.isEmpty(refundInfos)) {
            return Lists.newArrayList();
        }
        List<RefundInfoVO> refundInfoVOS = Lists.newArrayListWithCapacity(refundInfos.size());
        for (RefundInfoDO refundInfo : refundInfos) {
            refundInfoVOS.add(RefundInfoVO.builder()//
                    .refundSerialNo(refundInfo.getRefundSerialNo())//
                    .orderSerialNo(refundInfo.getOrderSerialNo())//
                    .refundMoney(BigDecimal.valueOf(refundInfo.getRefundMoney()))//
                    .refundStatus(RefundStatus.getByCode(refundInfo.getStatus()))//
                    .refundScene(RefundScene.getByCode(refundInfo.getScene()))//
                    .gmtApply(refundInfo.getGmtApply())//
                    .gmtRefundComplete(refundInfo.getGmtRefundComplete())//
                    .build());
        }
        return refundInfoVOS;
    }

    /**
     * 构建支付流水
     *
     * @param payInfos payInfos
     * @return 支付流水
     */
    private List<PaySerialInfoVO> buildPaySerials(List<PayInfoDO> payInfos) {
        if (CollectionUtils.isEmpty(payInfos)) {
            return Lists.newArrayList();
        }
        List<PaySerialInfoVO> paySerialInfos = Lists.newArrayListWithCapacity(payInfos.size());
        for (PayInfoDO payInfo : payInfos) {
            paySerialInfos.add(buildPaySerial(payInfo));
        }
        return paySerialInfos;
    }

    /**
     * 构建支付流水
     *
     * @param payInfo payInfo
     * @return payInfo
     */
    private PaySerialInfoVO buildPaySerial(PayInfoDO payInfo) {
        PaySerialInfoVO.PaySerialInfoVOBuilder<?, ?> builder = PaySerialInfoVO.builder();
        builder.tcSerialId(payInfo.getTcSerialId());
        builder.merSerialId(payInfo.getMerSerialId());
        builder.outSerialId(payInfo.getMerSerialId());
        builder.amount(BigDecimal.valueOf(payInfo.getAmount()));
        builder.gmtPaid(payInfo.getGmtPaid());
        builder.payProduct(payInfo.getPayProduct());
        builder.payProductName(payInfo.getPayProductName());
        builder.bankName(payInfo.getBankName());
        builder.bankType(payInfo.getBankType());
        builder.payChannelCode(payInfo.getPayChannelCode());
        builder.payChannel(payInfo.getPayChannel());
        builder.isIns(payInfo.getIsIns());
        builder.payCompanyCode(payInfo.getPayCompanyCode());
        builder.regulatorFlag(payInfo.getRegulatorFlag());
        return builder.build();
    }
}
