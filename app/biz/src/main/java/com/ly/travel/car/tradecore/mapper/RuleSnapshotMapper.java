package com.ly.travel.car.tradecore.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.NullValueMappingStrategy;

import com.ly.travel.car.tradecore.dal.dataobject.RuleSnapshotInfoDO;
import com.ly.travel.car.tradecore.model.cvt.EnumConverter;
import com.ly.travel.car.tradecore.model.rulesnapshot.RuleSnapshotInfoVO;

/**
 * <AUTHOR>
 * @version Id:RuleSnapshotMapper.java,v 0.1 2024/3/6 23:05 chengweiwen Exp $$
 */
@Mapper(componentModel = "spring", uses = { EnumConverter.class }, nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface RuleSnapshotMapper {

    RuleSnapshotInfoDO vo2do(RuleSnapshotInfoVO ruleSnapshotInfoVO);

    List<RuleSnapshotInfoVO> dos2voList(List<RuleSnapshotInfoDO> ruleSnapshotInfoDOList);
}
