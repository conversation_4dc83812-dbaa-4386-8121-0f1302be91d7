package com.ly.travel.car.tradecore.error;

import lombok.Data;

/**
 * <AUTHOR>
 * @version Id: OrderBookException, v 0.1 2024/3/4 09:47 icanci Exp $
 */
@Data
public class OrderBookException extends RuntimeException {

    /**
     * 授权失败
     */
    public static final int ERROR_CODE_AUTHORIZATION_FAILED        = 1;
    /**
     * 支付分创单失败
     */
    public static final int ERROR_CODE_PAYMENT_ORDER_CREATE_FAILED = 2;
    /**
     * 
     * 1 -授权失败
     * 2 -支付分创单失败
     * 
     */
    private int             errorCode;
    /**
     * Constructs a new runtime exception with the specified cause and a
     * detail message of <tt>(cause==null ? null : cause.toString())</tt>
     * (which typically contains the class and detail message of
     * <tt>cause</tt>).  This constructor is useful for runtime exceptions
     * that are little more than wrappers for other throwables.
     *
     * @param cause the cause (which is saved for later retrieval by the
     *              {@link #getCause()} method).  (A <tt>null</tt> value is
     *              permitted, and indicates that the cause is nonexistent or
     *              unknown.)
     * @since 1.4
     */
    public OrderBookException(Throwable cause) {
        super(cause);
    }

    /**
     * Constructs a new runtime exception with the specified detail message.
     * The cause is not initialized, and may subsequently be initialized by a
     * call to {@link #initCause}.
     *
     * @param message the detail message. The detail message is saved for
     *                later retrieval by the {@link #getMessage()} method.
     */
    public OrderBookException(String message) {
        super(message);
    }

    public OrderBookException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

}