package com.ly.travel.car.tradecore.builder.supplier;

import java.math.BigDecimal;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.ly.sof.utils.common.Money;
import com.ly.travel.car.tradecore.base.Builder;
import com.ly.travel.car.tradecore.business.finished.model.OrderFinishedSupplierBill;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.facade.request.callback.OrderFinishCallbackRequestDTO;
import com.ly.travel.car.tradecore.model.enums.CarFeeType;
import com.ly.travel.car.tradecore.model.enums.SupplierCarFeeType;

/**
 * 供应链构建器-行程结束
 * 
 * <AUTHOR>
 * @version Id: SupplierAppendCarBuilder, v 0.1 2024/3/4 19:48 ryan Exp $
 */
@Slf4j
@Service("supplierTripFinishedBuilder")
public class SupplierTripFinishedBuilder implements Builder<OrderFinishCallbackRequestDTO, OrderFinishedSupplierBill> {

    /**
     * 构建
     *
     * @param requestDTO the request dto
     * @return 构建出来目标对象 order trip finished details
     * @throws BuilderException 构建异常
     */
    @Override
    public OrderFinishedSupplierBill build(OrderFinishCallbackRequestDTO requestDTO) throws BuilderException {
        OrderFinishedSupplierBill details = new OrderFinishedSupplierBill();
        details.setOrderSerialNo(requestDTO.getOrderSerialNo());
        details.setSupplierCode(requestDTO.getSupplierCode());
        details.setSupplierOrderNo(requestDTO.getSupplierOrderNo());
        details.setExKilos(requestDTO.getExKilos());
        details.setExMinutes(requestDTO.getExMinutes());
        details.setTotalAmount(new Money(requestDTO.getTotalAmount()));
        details.setPoolStatus(requestDTO.getPoolStatus());
        details.setDetails(buildDetails(requestDTO));
        details.setHitRiskType(requestDTO.getHitRiskType());
        return details;
    }

    /**
     * Build details list.
     */
    private List<OrderFinishedSupplierBill.BillDetail> buildDetails(OrderFinishCallbackRequestDTO requestDTO) {
        List<OrderFinishedSupplierBill.BillDetail> details = Lists.newArrayList();
        for (OrderFinishCallbackRequestDTO.BillDetailDTO detail : requestDTO.getDetails()) {
            OrderFinishedSupplierBill.BillDetail billDetail = new OrderFinishedSupplierBill.BillDetail();
            SupplierCarFeeType carFeeType = SupplierCarFeeType.getByCode(detail.getType());
            // 完单时供应链返回了未知费用项
            if (carFeeType == SupplierCarFeeType.OTHER_FEE) {
                log.warn("供应链返回费用类型SupplierCarFeeType转换异常: code={}, feeType={}", detail.getType(), carFeeType);
            }
            billDetail.setType(SupplierCarFeeType.getByCode(detail.getType()));
            billDetail.setDesc(detail.getDesc());
            billDetail.setOriginalType(detail.getType());
            // 产品要求折扣费改成负数
            if (carFeeType.getCarFeeType() == CarFeeType.DISCOUNT_FEE && NumberUtils.isCreatable(detail.getAmount())) {
                BigDecimal amount = new BigDecimal(detail.getAmount());
                billDetail.setAmount(amount.abs().negate().toString());
            } else {
                billDetail.setAmount(detail.getAmount());
            }
            details.add(billDetail);
        }
        return details;
    }
}