package com.ly.travel.car.tradecore.business.reassigncallback.model;

import org.apache.commons.lang3.StringUtils;

import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.tradecore.business.ctx.AbstractOrderDispatchContext;
import com.ly.travel.car.tradecore.error.OrderBookException;
import com.ly.travel.car.tradecore.facade.request.callback.ReassignResultCallbackRequestDTO;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.ReassignResultCallbackScene;
import com.ly.travel.car.tradecore.model.enums.RequestSceneType;
import com.ly.travel.car.tradecore.model.goods.TicketVO;
import com.ly.travel.car.tradecore.model.trip.ResourceVO;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 供应链改派结果回写上下文对象
 *
 * <AUTHOR>
 * @version Id:ReassignCallbackContext.java,v 0.1 2024/3/4 16:27 chengweiwen Exp $$
 */
@Slf4j
@Getter
@Setter
public class ReassignCallbackContext extends AbstractOrderDispatchContext {

    /** 改派结果回写场景 1-改派成功结果回写、2-供应商直接换司机、3-萌艇司机挂靠 */
    private final ReassignResultCallbackScene scene;

    /** 改派结果回写资源 */
    private final ResourceVO                  resource;

    /** 是否推送短信 */
    private boolean                           pushSms;

    /** 是否允许历史司机 */
    private boolean                           historyDriver;
    /**
     * 派单时候的类型  0 正常派单 5 交易追加的(如:顺风车特惠追加)
     */
    private int                               dispatchType;

    public ReassignCallbackContext(OrderVO order, ReassignResultCallbackScene scene, ResourceVO resource,boolean historyDriver, int dispatchType) {
        super(order);
        this.scene = scene;
        this.resource = resource;
        this.historyDriver = historyDriver;
        this.dispatchType = dispatchType;
    }

    public static ReassignCallbackContext of(OrderVO order, ReassignResultCallbackRequestDTO requestDTO) {
        ReassignResultCallbackScene callbackScene = ReassignResultCallbackScene.getEnumByCode(requestDTO.getScene());
        ResourceVO reassignResource = buildReassignResource(order, requestDTO);

        LoggerUtils.info(log, "改派订单:{}, reassignResource:{}", order.getOrderSerialNo(), FastJsonUtils.toJSONString(reassignResource));
        return new ReassignCallbackContext(order, callbackScene, reassignResource,requestDTO.isHistoryDriver(), requestDTO.getDispatchType());
    }

    /**
     * 组装改派成功资源
     *
     * @param order 订单信息
     * @param requestDTO 改派结果回写请参
     * @return {@link ResourceVO}
     */
    private static ResourceVO buildReassignResource(OrderVO order, ReassignResultCallbackRequestDTO requestDTO) {
        ResourceVO reassignResource = getCallbackResource(order, requestDTO, RequestSceneType.REASSIGN_CALLBACK);
        if (reassignResource == null) {
            throw new OrderBookException("订单资源不存在");
        }

        // 供应链订单号
        reassignResource.setSupplierOrderSerialNo(requestDTO.getSupplierOrderSerialNo());
        return reassignResource;
    }

    /**
     * 根据 车牌号plateNumber+司机手机号driverPhone （真实/虚拟任一） 判断是否为同一司机
     */
    public boolean isSameDriver(OrderVO order) {
        TicketVO ticket = order.getTicket();
        String oldDriverFlag = ticket.getCarNum() + ticket.getDriverPhone() + StringUtils.defaultString(ticket.getDriverVirtualPhone());
        String newDriverFlag = resource.getCarNum() + resource.getDriverPhone() + StringUtils.defaultString(resource.getDriverVirtualPhone());
        boolean same = StringUtils.equals(oldDriverFlag, newDriverFlag);
        LoggerUtils.info(log, "改派优化:是否同一司机:{} 老司机:{} 新司机:{}", same, oldDriverFlag, newDriverFlag);
        return same;
    }
}
