package com.ly.travel.car.tradecore.service.log.builder;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.ly.travel.car.orderservice.facade.model.log.OrderLog;
import com.ly.travel.car.tradecore.integration.Symbols;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.trip.ResourceVO;
import com.ly.travel.car.tradecore.service.log.LogBuilder;
import com.ly.travel.car.tradecore.service.log.LogContext;
import com.ly.travel.car.tradecore.service.log.LogModule;

/**
 * 供应商确认接单日志
 *
 * <AUTHOR>
 * @version Id: AppendCarLogBuilder, v 0.1 2024/3/5 15:20 ryan Exp $
 */
@Service
public class SupplierConfirmLogBuilder extends AbstractLogBuilder implements LogBuilder {

    /**
     * 构建日志
     *
     * @param context 日志上下文
     * @return 日志
     */
    @Override
    public List<OrderLog> build(LogContext context) {
        LogModule logModule = context.getLogModule();
        OrderVO order = context.getOrder();
        String message = messageFactory.operateLog(logModule.getCode());
        OrderLog log = createLog(logModule, context.getOperator(), order, message);
        return Lists.newArrayList(log);
    }

    /**
     * 获取模块
     *
     * @return 模块
     */
    @Override
    public LogModule module() {
        return LogModule.SUPPLY_CONFIRM;
    }
}
