package com.ly.travel.car.tradecore.builder.book;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ly.travel.car.tradecore.base.Builder;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.facade.model.MemberDTO;
import com.ly.travel.car.tradecore.facade.request.trade.BaseBookRequestDTO;
import com.ly.travel.car.tradecore.model.order.MemberInfoVO;

/**
 * 会员信息构建
 *
 * <AUTHOR>
 * @version Id: MemberInfoBuilder, v 0.1 2024/2/27 17:01 icanci Exp $
 */
@Service("memberInfoBuilder")
public class MemberInfoBuilder implements Builder<BaseBookRequestDTO, MemberInfoVO> {
    /**
     * 构建
     *
     * @param request 原始输入
     * @return 构建出来目标对象
     * @throws BuilderException 构建异常
     */
    @Override
    public MemberInfoVO build(BaseBookRequestDTO request) throws BuilderException {
        MemberDTO member = request.getBookInfo().getMember();

        return MemberInfoVO//
            .builder()//
            .id(member.getId())//
            .unionId(StringUtils.defaultIfBlank(member.getUnionId(), StringUtils.EMPTY))//
            .openId(StringUtils.defaultIfBlank(member.getOpenId(), StringUtils.EMPTY))//
            .userUniqueId(StringUtils.defaultIfBlank(member.getUserUniqueId(), StringUtils.EMPTY))
            .build();
    }
}