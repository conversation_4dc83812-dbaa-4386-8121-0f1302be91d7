package com.ly.travel.car.tradecore.business.thirdordernotify.orderStateNotify.impl;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.ly.travel.car.tradecore.business.cancel.OrderCancelService;
import com.ly.travel.car.tradecore.business.cancel.model.CancelContext;
import com.ly.travel.car.tradecore.business.thirdordernotify.model.ThirdOrderStateNotifyContext;
import com.ly.travel.car.tradecore.business.thirdordernotify.orderStateNotify.ThirdOrderStateNotifyProcessor;
import com.ly.travel.car.tradecore.facade.request.trade.CancelRequestDTO;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.CancelType;
import com.ly.travel.car.tradecore.model.enums.ThirdOrderNotifyState;
import com.ly.travel.car.tradecore.order.OrderService;

@Service("thirdOrderStateNotifyFailureProcessor")
public class ThirdOrderStateNotifyFailureProcessor implements ThirdOrderStateNotifyProcessor {

    @Resource
    private OrderService       orderService;
    @Resource
    private OrderCancelService orderCancelService;

    @Override
    public void process(ThirdOrderStateNotifyContext context) {
        OrderVO order = context.getOrder();

        // 更新订单ext
        orderService.updateOrderExt(order);

        //系统自动取消
        orderCancelService.cancel(CancelContext.of(context.getOrder(), buildSystemCancelRequestDTO(context)));
    }

    /**
     * 构建系统取消请求
     * @param context
     * @return
     */
    private static CancelRequestDTO buildSystemCancelRequestDTO(ThirdOrderStateNotifyContext context) {
        CancelRequestDTO requestDTO = new CancelRequestDTO();
        requestDTO.setMemberIds(Lists.newArrayList(context.getOrder().getMemberInfo().getId()));
        requestDTO.setOrderSerialNo(context.getOrder().getOrderSerialNo());
        CancelType cancelType = CancelType.TICKET_ISSUANCE_FAILURE;
        requestDTO.setCancelType(cancelType.getCode());
        requestDTO.setCancelTypeRealCode(cancelType.getRealCode());
        requestDTO.setReason(context.tagName() + ThirdOrderNotifyState.getByCode(context.getRequest().getState() + "").getDesc());
        requestDTO.setCancelReasonType(cancelType.getCancelReasonType());
        requestDTO.setDisclaimer(true);
        requestDTO.setPenaltyConfirmAmount("0");
        requestDTO.setTraceId(context.getOrder().getTraceId());
        requestDTO.setCancelSmsReason(generateSmsReason(context));
        return requestDTO;
    }

    /**
     * 生成特有的短信
     * @param context
     * @return
     */
    private static String generateSmsReason(ThirdOrderStateNotifyContext context) {
        String desc = ThirdOrderNotifyState.getByCode(context.getRequest().getState() + "").getDesc();
        String reason = context.tagName() + desc;
        if (StringUtils.equals(context.tagName(), "未知")) {
            return "由于关联订单出票失败";
        }
        return StringUtils.replace("由于关联的" + reason, "出票失败", "订单出票失败");
    }
}
