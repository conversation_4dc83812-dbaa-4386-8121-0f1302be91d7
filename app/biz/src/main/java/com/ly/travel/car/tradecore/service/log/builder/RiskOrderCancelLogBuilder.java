package com.ly.travel.car.tradecore.service.log.builder;

import java.util.List;
import java.util.Objects;

import com.ly.travel.car.tradecore.service.log.LogExt;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.ly.travel.car.orderservice.facade.model.log.OrderLog;
import com.ly.travel.car.tradecore.service.log.LogBuilder;
import com.ly.travel.car.tradecore.service.log.LogContext;
import com.ly.travel.car.tradecore.service.log.LogModule;

/**
 * LY.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * 订单风控取消日志
 *
 * <AUTHOR>
 * @since 2024/8/1
 */
@Service
public class RiskOrderCancelLogBuilder extends AbstractLogBuilder implements LogBuilder {
    @Override
    public List<OrderLog> build(LogContext context) {
        LogModule logModule = context.getLogModule();
        String message = messageFactory.operateLog(logModule.getCode());
        if (Objects.nonNull(context.getExt())
                && CollectionUtils.isNotEmpty(context.getExt().entrySet())
                && context.getExt().keySet().contains(LogExt.LOG_EXT_CONTENT)
                && StringUtils.isNotBlank(context.getExt().get(LogExt.LOG_EXT_CONTENT).toString())
        ){
            message = context.getExt().get(LogExt.LOG_EXT_CONTENT).toString();
        }
        OrderLog log = createLog(logModule, context.getOperator(), context.getOrder(), message);
        return Lists.newArrayList(log);
    }

    @Override
    public LogModule module() {
        return LogModule.AT_RISK;
    }
}
