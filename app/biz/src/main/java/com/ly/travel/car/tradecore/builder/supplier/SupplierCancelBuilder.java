package com.ly.travel.car.tradecore.builder.supplier;

import org.springframework.stereotype.Service;

import com.ly.travel.car.tradecore.base.Builder;
import com.ly.travel.car.tradecore.business.cancel.model.CancelContext;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.model.enums.CancelType;
import com.ly.travel.shared.mobility.supply.trade.core.facade.trade.request.CancelRequest;

/**
 * 供应链构建器-取消构建
 *
 * <AUTHOR>
 * @version Id: SupplierCancelBuilder, v 0.1 2024/3/4 19:44 icanci Exp $
 */
@Service("dispatchCancelBuilder")
public class SupplierCancelBuilder implements Builder<CancelContext, CancelRequest> {
    /**
     * 构建
     *
     * @param cancelContext 原始输入
     * @return 构建出来目标对象
     * @throws BuilderException 构建异常
     */
    @Override
    public CancelRequest build(CancelContext cancelContext) throws BuilderException {
        CancelRequest request = new CancelRequest();
        request.setOrderSerialNo(cancelContext.getOrderSerialNo());
        request.setTraceId(cancelContext.getTraceId());
        com.ly.travel.car.tradecore.facade.request.trade.CancelRequestDTO cancelRequest = cancelContext.getCancelRequestDTO();
        request.setCancelReason(cancelRequest.getReason());
        request.setCancelType(CancelType.getByRealCodeOrCode(cancelRequest.getCancelTypeRealCode(), cancelRequest.getCancelType()).getSupplierCode());
        request.setFlightChange(cancelContext.isFlightChanged() ? 1 : 0);
        if (cancelContext.getOrder().isDistributionOrder()) {
            //分销订单增加透传
            request.setCancelFeeJudge(cancelContext.getCancelRequestDTO().getCancelFeeJudge());
            request.setDistributionCancelFee(cancelContext.getCancelRequestDTO().getDistributionCancelFee());
        }
        return request;
    }
}
