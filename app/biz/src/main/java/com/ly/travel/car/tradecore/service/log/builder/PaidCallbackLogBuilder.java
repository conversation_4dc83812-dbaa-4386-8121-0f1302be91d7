package com.ly.travel.car.tradecore.service.log.builder;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.ly.travel.car.orderservice.facade.model.log.OrderLog;
import com.ly.travel.car.tradecore.service.log.LogBuilder;
import com.ly.travel.car.tradecore.service.log.LogContext;
import com.ly.travel.car.tradecore.service.log.LogExt;
import com.ly.travel.car.tradecore.service.log.LogModule;

/**
 * LY.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 * 
 * 财务交互日志 
 *
 * <AUTHOR>
 * @since 2025/1/12
 */
@Service
public class PaidCallbackLogBuilder extends AbstractLogBuilder implements LogBuilder {

    @Override
    public List<OrderLog> build(LogContext context) {
        LogModule logModule = context.getLogModule();
        String content = (String) context.getExt(LogExt.LOG_EXT_CONTENT, StringUtils.EMPTY);
        String message = messageFactory.operateLog(logModule.getCode(), content);
        OrderLog log = createLog(logModule, context.getOperator(), context.getOrder(), message);
        return Lists.newArrayList(log);
    }

    @Override
    public LogModule module() {
        return LogModule.PAID_CALLBACK;
    }
}
