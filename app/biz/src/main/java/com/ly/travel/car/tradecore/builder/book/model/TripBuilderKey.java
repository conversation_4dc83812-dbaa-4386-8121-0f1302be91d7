package com.ly.travel.car.tradecore.builder.book.model;

import com.ly.travel.car.tradecore.facade.request.trade.BaseBookRequestDTO;

import com.ly.travel.car.tradecore.facade.request.trade.BookRequestDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 票构建key
 *
 * <AUTHOR>
 * @version Id: TripBuilderKey, v 0.1 2024/2/27 17:12 icanci Exp $
 */
@Data
@AllArgsConstructor
public class TripBuilderKey {

    private BookRequestDTO request;

    private String         orderSerialNo;
}
