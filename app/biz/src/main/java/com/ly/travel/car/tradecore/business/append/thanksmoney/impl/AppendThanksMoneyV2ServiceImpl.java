package com.ly.travel.car.tradecore.business.append.thanksmoney.impl;

import com.google.common.collect.Lists;
import com.ly.sof.api.error.LYError;
import com.ly.sof.utils.common.DateUtil;
import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.common.repo.common.cfg.ThanksFeeCfg;
import com.ly.travel.car.common.repo.common.cfg.ThanksFeeCfgV2;
import com.ly.travel.car.tradecore.builder.ThanksMoneyBuilderProxy;
import com.ly.travel.car.tradecore.business.BaseBusinessService;
import com.ly.travel.car.tradecore.business.append.thanksmoney.AppendThanksMoneyService;
import com.ly.travel.car.tradecore.business.ctx.StandardOrderContext;
import com.ly.travel.car.tradecore.business.strategy.StrategyType;
import com.ly.travel.car.tradecore.business.append.thanksmoney.AppendThanksMoneyAppendStrategy;
import com.ly.travel.car.tradecore.business.thanksmoney.model.AppendThanksMoneyContext;
import com.ly.travel.car.tradecore.enums.strategy.AppendThanksMoneyStrategyEnum;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.error.ValidateException;
import com.ly.travel.car.tradecore.facade.request.append.AppendThanksMoneyRequestDTO;
import com.ly.travel.car.tradecore.facade.response.append.AppendThanksMoneyResponseDTO;
import com.ly.travel.car.tradecore.integration.Symbols;
import com.ly.travel.car.tradecore.integration.client.id.IDGeneratorService;
import com.ly.travel.car.tradecore.integration.client.id.IDHolder;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.*;
import com.ly.travel.car.tradecore.model.goods.ThanksMoneyVO;
import com.ly.travel.car.tradecore.model.pay.UserPendingPayInfoVO;
import com.ly.travel.car.tradecore.order.OrderService;
import com.ly.travel.car.tradecore.order.StandardOrderCtx;
import com.ly.travel.car.tradecore.order.UserPendingPayInfoService;
import com.ly.travel.car.tradecore.producer.impl.ThanksMoneyDelayAppendProducer;
import com.ly.travel.car.tradecore.producer.source.ThanksMoneyDelayAppendSource;
import com.ly.travel.car.tradecore.service.pay.PayService;
import com.ly.travel.car.tradecore.service.rule.ThanksMoneyRuleService;
import com.ly.travel.car.tradecore.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 新版追加加速匹配费，实现
 * @Author: jay.he
 * @Date: 2025-03-03 14:39
 * @Version: 1.0
 **/
@Service
@Slf4j
public class AppendThanksMoneyV2ServiceImpl
        extends BaseBusinessService
        implements AppendThanksMoneyService, AppendThanksMoneyAppendStrategy {

    @Resource
    private OrderService orderService;

    @Resource
    private ThanksMoneyBuilderProxy thanksMoneyBuilderProxy;

    @Resource(name = "idGeneratorService")
    private IDGeneratorService idGeneratorService;

    @Resource(name = "payService")
    private PayService payService;

    @Resource
    private ThanksMoneyDelayAppendProducer delayAppendProducer;

    @Resource
    private ThanksMoneyRuleService thanksMoneyRuleService;

    @Resource
    private UserPendingPayInfoService userPendingPayInfoService;


    @Override
    public AppendThanksMoneyResponseDTO appendThanksMoney(AppendThanksMoneyContext context) {
        return appendThanksMoney(context.getRequest());
    }

    @Override
    public AppendThanksMoneyResponseDTO appendThanksMoney(AppendThanksMoneyRequestDTO request) {
        String traceId = request.getTraceId();
        try {
            StandardOrderContext orderContext = StandardOrderCtx.of(request.getOrderSerialNo());
            // 1、参数校验
            validatePreAppend(orderContext, request);

            AppendThanksMoneyResponseDTO response = AppendThanksMoneyResponseDTO.success(traceId);

            /*
             * 2、是否生成补差单
             *   2.1 前付已付，生成补差单
             *       2.1.1 补差未支付(上份加速费未支付)，生成补差单
             *   2.2 前付未付或后付，不生成补差单，直接保存item项至数据库,并添加延迟追加任务
             */
            if (orderContext.isPaidOrder() || orderContext.getOrder().isBalancePayUnpaid()) {
                //2.1 前付已付，生成补差单
                UserPendingPayInfoVO pendingPayInfoVO = generatePendingPayInfo(orderContext, request);
                //前付补差，返回补差单号和金额
                response.setPendingPaySerialNo(pendingPayInfoVO.getPendingPaySerialNo());
                response.setFeeAmount(pendingPayInfoVO.getAmount().getAmount().toPlainString());
            } else {
                //2.2 前付未付或后付，不生成补差单，直接保存item项至数据库
                ThanksMoneyVO thanksMoneyVO = thanksMoneyBuilderProxy.doBuildThanksMoney(request);
                //新版加速匹配，创建时状态未已预订
                thanksMoneyVO.setState(GoodsState.BOOKED);
                orderService.saveAppendThanksMoney(orderContext.getOrder(), Lists.newArrayList(thanksMoneyVO));

                // 2.3.打tag，用户已添加加速费
                orderService.updateOrderTag(orderContext.getOrder(), OrderTag.APPEND_ACCELERATION_FEE);

                // 3、加速匹配费，延迟任务推送供应链
                addDelayAppendTask(orderContext, traceId, thanksMoneyVO);
            }
            return response;
        } catch (BuilderException e) {
            LoggerUtils.error(log, "顺风车追加加速匹配服务(新版)构建异常：{}", e, e.getMessage());
            return AppendThanksMoneyResponseDTO.fail(traceId, e.getErrorCode(), e.getMessage());
        } catch (ValidateException e) {
            return AppendThanksMoneyResponseDTO.fail(traceId, e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "顺风车追加加速匹配服务(新版)未知异常：{}", e, e.getMessage());
            LYError lyError = ERROR.sysErr("顺风车追加加速匹配服务失败");
            return AppendThanksMoneyResponseDTO.fail(traceId, lyError.getCode(), lyError.getMessage());
        }
    }

    /**
     * 加速匹配费，发起延迟任务推送供应链
     *
     * @param orderContext
     * @param traceId
     * @param thanksMoneyVO
     */
    private void addDelayAppendTask(StandardOrderContext orderContext, String traceId, ThanksMoneyVO thanksMoneyVO) {
        ThanksFeeCfgV2.FeeInfo feeInfo = thanksMoneyRuleService.queryThanksFeeCfgV2(orderContext);
        if (Objects.nonNull(feeInfo) && feeInfo.isSend2SupplierTrade()) {
            delayAppendProducer.send(ThanksMoneyDelayAppendSource
                    .builder()
                    .traceId(traceId)
                    .order(orderContext.getOrder())
                    .itemId(thanksMoneyVO.getItemId())
                    .delay(feeInfo.getDelaySeconds())
                    .build());
        }
    }

    @Override
    public AppendThanksMoneyResponseDTO checkCanAppendThanksMoney(AppendThanksMoneyRequestDTO request) {
        try {
            StandardOrderContext orderContext = StandardOrderCtx.of(request.getOrderSerialNo());
            request.setAmount(String.valueOf(BigDecimal.ZERO));
            validatePreAppend(orderContext, request);
            return AppendThanksMoneyResponseDTO.success(request.getTraceId());
        } catch (BuilderException e) {
            return AppendThanksMoneyResponseDTO.fail(request.getTraceId(), e.getErrorCode(), e.getMessage());
        } catch (ValidateException e) {
            return AppendThanksMoneyResponseDTO.fail(request.getTraceId(), e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "顺风车可否追加加速匹配服务(感谢金)未知异常：{}", e, e.getMessage());
            return AppendThanksMoneyResponseDTO.fail(request.getTraceId(), ERROR.unsupportedOperation(e.getMessage()).getCode(), e.getMessage());
        }
    }

    @Override
    public UserPendingPayInfoVO generatePendingPayInfo(StandardOrderContext context, AppendThanksMoneyRequestDTO request) {
        // 补差流水
        OrderVO order = context.getOrder();
        String pendingPaySerialNo = idGeneratorService.generateNo(Constants.XYC_PRE, order.getMemberInfo().getId());
        Money pendingPayAmount = new Money(request.getAmount());

        // 构建待支付记录
        UserPendingPayInfoVO pendingPayInfoVO = UserPendingPayInfoVO.builder()
                .memberId(order.getMemberInfo().getId())
                .orderSerialNo(order.getOrderSerialNo())
                .pendingPaySerialNo(pendingPaySerialNo)
                .scene(UserPendingPayScene.ACCELERATION)
                .state(UserPendingPayStatus.PENDING)
                .amount(new Money(request.getAmount()))
                .gmtPaid(DateUtil.parseDateNewFormat(Symbols.DEFAULT_DATE_TIME))
                .items(buildPendingPayItems(order, pendingPayAmount))
                .build();

        //保存补差单 -- 创建补差记录
        userPendingPayInfoService.save(order, pendingPayInfoVO);

        return pendingPayInfoVO;
    }

    /**
     * 生成补差单，费用明细
     *
     * @param order
     * @param pendingPayAmount
     * @return
     */
    private List<UserPendingPayInfoVO.PayItem> buildPendingPayItems(OrderVO order, Money pendingPayAmount) {
        List<UserPendingPayInfoVO.PayItem> items = Lists.newArrayList();
        UserPendingPayInfoVO.PayItem payItem = new UserPendingPayInfoVO.PayItem();
        payItem.setName(CarFeeType.THANKS_FEE.name());
        payItem.setCategory(GoodsType.THANKS_MONEY.getSpu());
        payItem.setSubCategory(StringUtils.EMPTY);
        // 前付已付场景：
        // 追加感谢费需要在补差流水中生成itemId，基于支付回调成功后，落item时，将此itemId赋值给创建的Item对象
        payItem.setItemId(IDHolder.generateFormattedRandomUUID());
        payItem.setAmount(pendingPayAmount.getAmount().toPlainString());
        items.add(payItem);
        return items;
    }

    /**
     * 允许追加加速匹配服务(感谢金)校验
     *
     * @param request      请参
     * @param orderContext 订单上下文
     * @throws BuilderException  异常
     * @throws ValidateException 异常
     */
    private void validatePreAppend(StandardOrderContext orderContext, AppendThanksMoneyRequestDTO request) throws BuilderException, ValidateException {
        OrderVO orderVO = orderContext.getOrder();
        // 校验会员id
        String memberId = orderContext.getMemberId();
        if (!request.getMemberIds().contains(memberId)) {
            throw new ValidateException(ERROR.invalidOrder(request.getOrderSerialNo(), memberId));
        }
        // 订单状态只有派车中，才能追加加速匹配服务(感谢金)
        if (!OrderState.DISPATCHING.equals(orderVO.getOrderState())) {
            throw new ValidateException(ERROR.unsupportedOperation("当前订单状态不支持追加加速匹配服务"));
        }

        // 校验追加金额是否已超过配置上限
        if (orderVO.hasThanksMoneys()) {
            // 已追加总金额，加上这次追加值
            BigDecimal appendTotalAmount = orderVO.getTotalThanksAmount().add(new Money(request.getAmount())).getAmount();
            if (orderVO.isSfcNewThanksFeeOrder()) {
                ThanksFeeCfgV2.FeeInfo thanksFeeCfgV2 = thanksMoneyRuleService.queryThanksFeeCfgV2(orderContext);
                //新版最多4档，配置的是全量数据，表示从开始到当前档位的最大值
                if (Objects.nonNull(thanksFeeCfgV2) && appendTotalAmount.compareTo(thanksFeeCfgV2.getFourStep()) > 0) {
                    throw new ValidateException(ERROR.unsupportedOperation("追加加速匹配服务金额超过配置上限，无法继续追加"));
                }
            } else {
                ThanksFeeCfg.FeeInfo thanksFeeCfg = thanksMoneyRuleService.queryThanksFeeCfg(orderContext);
                if (thanksFeeCfg != null && appendTotalAmount.compareTo(thanksFeeCfg.getThirdStep()) > 0) {
                    throw new ValidateException(ERROR.unsupportedOperation("追加加速匹配服务金额超过配置上限，无法继续追加"));
                }
            }
        }
    }


    @Override
    public StrategyType getStrategy() {
        return AppendThanksMoneyStrategyEnum.APPEND_THANKS_MONEY_NEW;
    }
}
