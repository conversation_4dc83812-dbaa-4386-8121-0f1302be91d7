package com.ly.travel.car.tradecore.mapper;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueMappingStrategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.common.model.enums.CarTypeEnum;
import com.ly.travel.car.common.model.enums.ChargeTypeEnum;
import com.ly.travel.car.common.model.enums.ServiceType;
import com.ly.travel.car.common.model.enums.SupplierChargeTypeEnum;
import com.ly.travel.car.tradecore.dal.dataobject.ItemInfoDO;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.cvt.DateConverter;
import com.ly.travel.car.tradecore.model.cvt.DefaultLongConverter;
import com.ly.travel.car.tradecore.model.cvt.EnumConverter;
import com.ly.travel.car.tradecore.model.enums.*;
import com.ly.travel.car.tradecore.model.ext.*;
import com.ly.travel.car.tradecore.model.goods.*;
import com.ly.travel.car.tradecore.utils.CfgUtils;

/**
 * ItemDetailMapper
 *
 * <AUTHOR>
 * @version Id: ItemDetailMapper, v 0.1 2024/2/28 11:26 icanci Exp $
 */
@Mapper(componentModel = "spring", uses = { DateConverter.class, DefaultLongConverter.class,
                                            EnumConverter.class }, nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public abstract class ItemDetailMapper {
    /**
     * V2 ticket item info do.
     *
     * @param order the order
     * @param ticket the ticket
     * @return the item info do
     */
    public ItemInfoDO v2Ticket(OrderVO order, TicketVO ticket) {
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(ticket.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(ticket.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(ticket.getCode()));
        itemInfo.setDescription(StringUtils.defaultString(ticket.getName()));
        itemInfo.setBusinessOrderNo(StringUtils.defaultString(ticket.getBusinessNo()));
        itemInfo.setThirdOrderNo(StringUtils.defaultString(ticket.getThirdNo()));
        itemInfo.setSupplierId(StringUtils.defaultString(ticket.getSupplierId()));
        itemInfo.setSupplierCode(StringUtils.defaultString(ticket.getSupplierCode()));
        itemInfo.setSupplierName(StringUtils.defaultString(ticket.getSupplierName()));
        itemInfo.setSupplierNameToClient(StringUtils.defaultString(ticket.getSupplierNameToClient()));
        itemInfo.setBusinessType(BusinessType.TICKET.getCode());
        itemInfo.setSpu(StringUtils.defaultString(ticket.getSpu()));
        itemInfo.setSalePrice(ticket.getSalePriceYuan().doubleValue());
        itemInfo.setSupplierPrice(ticket.getSupplierPrice().getAmount().doubleValue());
        itemInfo.setRealPrice(ticket.getRealPrice().getAmount().doubleValue());
        itemInfo.setExt(buildExt(ticket));
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * Build ext string.
     *
     * @param ticket the ticket
     * @return the string
     */
    private String buildExt(TicketVO ticket) {
        return JSON.toJSONString(ticket.getTicketExtVO(), SerializerFeature.DisableCircularReferenceDetect);
    }

    /**
     * V2 ancillary item info do.
     *
     * @param order order
     * @param ancillary ancillary
     * @return the item info do
     */
    public ItemInfoDO v2Ancillary(OrderVO order, AncillaryVO ancillary) {
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(ancillary.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(ancillary.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(ancillary.getCode()));
        itemInfo.setDescription(StringUtils.defaultString(ancillary.getName()));
        itemInfo.setBusinessOrderNo(StringUtils.defaultString(ancillary.getBusinessNo()));
        itemInfo.setThirdOrderNo(StringUtils.defaultString(ancillary.getThirdNo()));
        itemInfo.setSupplierId(StringUtils.EMPTY);
        itemInfo.setSupplierCode(StringUtils.EMPTY);
        itemInfo.setSupplierName(StringUtils.EMPTY);
        itemInfo.setBusinessType(BusinessType.ANCILLARY.getCode());
        itemInfo.setSpu(StringUtils.defaultString(ancillary.getSpu()));
        itemInfo.setSalePrice(ancillary.getSalePriceYuan().doubleValue());
        itemInfo.setSupplierPrice(ancillary.getSupplierPrice().getAmount().doubleValue());
        itemInfo.setRealPrice(ancillary.getRealPrice().getAmount().doubleValue());
        itemInfo.setExt(buildExt(ancillary));
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * Build ext string.
     *
     * @param ancillary the ancillary
     * @return the string
     */
    private String buildExt(AncillaryVO ancillary) {
        AncillaryExtVO ancillaryExtVO = new AncillaryExtVO();
        ancillaryExtVO.setCategory(ancillary.getCategory());
        ancillaryExtVO.setSubCategory(ancillary.getSubCategory());
        ancillaryExtVO.setDataId(ancillary.getDataId());
        ancillaryExtVO.setShelfCode(ancillary.getShelfCode());
        ancillaryExtVO.setSpuCode(ancillary.getSpuCode());
        ancillaryExtVO.setSkuCode(ancillary.getSkuCode());
        ancillaryExtVO.setSaleMode(ancillary.getSaleMode().getCode());
        ancillaryExtVO.setSingleSale(ancillary.isSingleSale());
        ancillaryExtVO.setQuantity(ancillary.getQuantity());
        ancillaryExtVO.setUseExtJson(ancillary.getUseExtJson());
        ancillaryExtVO.setPackType(ancillary.getPackType());
        ancillaryExtVO.setPackItemId(ancillary.getPackItemId());
        ancillaryExtVO.setPackItemSkuCode(ancillary.getPackItemSkuCode());
        ancillaryExtVO.setPackItemProductName(ancillary.getPackItemProductName());
        ancillaryExtVO.setSupplierAccountCompany(ancillary.getSupplierAccountCompany());
        ancillaryExtVO.setExtJson(ancillary.getExtJson());
        ancillaryExtVO.setWyDiscountPrice(ancillary.getWyDiscountPrice().getAmount().toPlainString());
        //先收后退相关标识
        ancillaryExtVO.setRefundAfterPay(ancillary.isRefundAfterPay());
        if (ancillary.getRefundAmountAfterPay() != null) {
            ancillaryExtVO.setRefundAmountAfterPay(ancillary.getRefundAmountAfterPay().getAmount().toPlainString());
        }
        return FastJsonUtils.toJSONString(ancillaryExtVO);
    }

    /**
     * V2 coupon item info do.
     *
     * @param order order
     * @param coupon coupon
     * @return the item info do
     */
    public ItemInfoDO v2Coupon(OrderVO order, CouponVO coupon) {
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(coupon.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(coupon.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(coupon.getCode()));
        itemInfo.setDescription(StringUtils.defaultString(coupon.getName()));
        itemInfo.setBusinessOrderNo(coupon.getCouponNo());
        itemInfo.setThirdOrderNo(StringUtils.EMPTY);
        itemInfo.setSupplierId(StringUtils.EMPTY);
        itemInfo.setSupplierCode(StringUtils.EMPTY);
        itemInfo.setSupplierName(StringUtils.EMPTY);
        itemInfo.setBusinessType(BusinessType.COUPON.getCode());
        itemInfo.setSpu(coupon.getSpu());
        itemInfo.setSalePrice(coupon.getSalePrice().getAmount().doubleValue());
        itemInfo.setSupplierPrice(coupon.getSalePrice().getAmount().doubleValue());
        itemInfo.setRealPrice(coupon.getRealPrice().getAmount().doubleValue());
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setExt(buildExt(coupon));
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * Build ext string.
     *
     * @param coupon the coupon
     * @return the string
     */
    private String buildExt(CouponVO coupon) {
        CouponExtVO couponExt = new CouponExtVO();
        couponExt.setType(coupon.getCouponType());
        couponExt.setFlashSale(coupon.isFlashSale());
        couponExt.setCategory(coupon.getCategory());
        couponExt.setSubCategory(coupon.getSubCategory());
        couponExt.setBatchNo(coupon.getBatchNo());
        //增加先收后退字段标识，统一增加，无论是否是否为收款项
        couponExt.setRefundAfterPay(coupon.isRefundAfterPay());
        if (coupon.getRefundAmountAfterPay() != null){
            couponExt.setRefundAmountAfterPay(coupon.getRefundAmountAfterPay().getAmount().toPlainString());
        }
        return FastJsonUtils.toJSONString(couponExt);
    }

    /**
     * V2d mileage item info do.
     *
     * @param order the order
     * @param mileage the mileage
     * @return the item info do
     */
    public ItemInfoDO v2dMileage(OrderVO order, MileageVO mileage) {
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(mileage.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(mileage.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(mileage.getCode()));
        itemInfo.setDescription(StringUtils.defaultString(mileage.getName()));
        itemInfo.setBusinessOrderNo(StringUtils.EMPTY);
        itemInfo.setThirdOrderNo(StringUtils.EMPTY);
        itemInfo.setSupplierId(StringUtils.EMPTY);
        itemInfo.setSupplierCode(StringUtils.EMPTY);
        itemInfo.setSupplierName(StringUtils.EMPTY);
        itemInfo.setBusinessType(BusinessType.MILEAGE.getCode());
        itemInfo.setSpu(mileage.getSpu());
        itemInfo.setSalePrice(mileage.getSalePrice().getAmount().doubleValue());
        itemInfo.setSupplierPrice(mileage.getSalePrice().getAmount().doubleValue());
        itemInfo.setRealPrice(mileage.getRealPrice().getAmount().doubleValue());
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setExt(buildExt(mileage));
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * buildExt
     *
     * @param mileage mileage
     * @return String
     */
    private String buildExt(MileageVO mileage) {
        MileageExtVO mileageExtVO = new MileageExtVO();
        mileageExtVO.setCategory(mileage.getCategory());
        mileageExtVO.setSubCategory(mileage.getSubCategory());
        mileageExtVO.setMileage(mileage.getMileage());
        mileageExtVO.setRate(mileage.getRate());
        mileageExtVO.setRuleCode(mileage.getRuleCode());
        //增加先收后退字段标识，统一增加，无论是否是否为收款项
        mileageExtVO.setRefundAfterPay(mileage.isRefundAfterPay());
        if (mileage.getRefundAmountAfterPay() != null) {
            mileageExtVO.setRefundAmountAfterPay(mileage.getRefundAmountAfterPay().getAmount().toPlainString());
        }
        return FastJsonUtils.toJSONString(mileageExtVO);
    }

    /**
     * 构建虚拟商品
     *
     * @param order order
     * @param virtualItem virtualItem
     * @return ItemInfoDO
     */
    public ItemInfoDO v2dVirtualItem(OrderVO order, VirtualItemVO virtualItem) {
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(virtualItem.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(virtualItem.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(virtualItem.getCode()));
        itemInfo.setDescription(StringUtils.defaultString(virtualItem.getName()));
        itemInfo.setBusinessOrderNo(StringUtils.EMPTY);
        itemInfo.setThirdOrderNo(StringUtils.EMPTY);
        itemInfo.setSupplierId(StringUtils.EMPTY);
        itemInfo.setSupplierCode(StringUtils.EMPTY);
        itemInfo.setSupplierName(StringUtils.EMPTY);
        itemInfo.setBusinessType(BusinessType.VIRTUAL.getCode());
        itemInfo.setSpu(virtualItem.getSpu());
        itemInfo.setSalePrice(virtualItem.getSalePrice().getAmount().doubleValue());
        itemInfo.setSupplierPrice(virtualItem.getSalePrice().getAmount().doubleValue());
        itemInfo.setRealPrice(virtualItem.getRealPrice().getAmount().doubleValue());
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setExt(buildExt(virtualItem));
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * Build ext string.
     *
     * @param virtualItem the virtualItem
     * @return the string
     */
    private String buildExt(VirtualItemVO virtualItem) {
        VirtualItemExtVO virtualItemExt = new VirtualItemExtVO();
        virtualItemExt.setCategory(virtualItem.getCategory());
        virtualItemExt.setSubCategory(virtualItem.getSubCategory());
        virtualItemExt.setMainBusinessType(virtualItem.getMainBusinessType().getCode());
        virtualItemExt.setMainItemId(virtualItem.getMainItemId());
        //增加先收后退字段标识，统一增加，无论是否是否为收款项
        virtualItemExt.setRefundAfterPay(virtualItem.isRefundAfterPay());
        if (virtualItem.getRefundAmountAfterPay() != null){
            virtualItemExt.setRefundAmountAfterPay(virtualItem.getRefundAmountAfterPay().getAmount().toPlainString());
        }
        return FastJsonUtils.toJSONString(virtualItemExt);
    }

    /**
     * 加速匹配服务(感谢金)
     *
     * @param order order
     * @param thanksMoney thanksMoney
     * @return ItemInfoDO
     */
    public ItemInfoDO v2dThanksMoney(OrderVO order, ThanksMoneyVO thanksMoney) {
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(thanksMoney.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(thanksMoney.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(thanksMoney.getCode()));
        itemInfo.setDescription(StringUtils.defaultString(thanksMoney.getName()));
        itemInfo.setBusinessOrderNo(StringUtils.EMPTY);
        itemInfo.setThirdOrderNo(StringUtils.EMPTY);
        itemInfo.setSupplierId(StringUtils.EMPTY);
        itemInfo.setSupplierCode(StringUtils.EMPTY);
        itemInfo.setSupplierName(StringUtils.EMPTY);
        itemInfo.setBusinessType(BusinessType.THANKS_MONEY.getCode());
        itemInfo.setSpu(thanksMoney.getSpu());
        itemInfo.setSalePrice(thanksMoney.getSalePrice().getAmount().doubleValue());
        itemInfo.setSupplierPrice(thanksMoney.getSalePrice().getAmount().doubleValue());
        itemInfo.setRealPrice(thanksMoney.getRealPrice().getAmount().doubleValue());
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setExt(buildExt(thanksMoney));
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * Build ext string.
     *
     * @param thanksMoney the thanksMoney
     * @return the string
     */
    private String buildExt(ThanksMoneyVO thanksMoney) {
        ThanksMoneyExtVO thanksMoneyExt = new ThanksMoneyExtVO();
        thanksMoneyExt.setCategory(thanksMoney.getCategory());
        thanksMoneyExt.setSubCategory(thanksMoney.getSubCategory());
        //增加先收后退字段标识，统一增加，无论是否是否为收款项
        thanksMoneyExt.setRefundAfterPay(thanksMoney.isRefundAfterPay());
        if (thanksMoney.getRefundAmountAfterPay() != null){
            thanksMoneyExt.setRefundAmountAfterPay(thanksMoney.getRefundAmountAfterPay().getAmount().toPlainString());
        }
        thanksMoneyExt.setAppendToSupplier(thanksMoney.getAppendToSupplier());
        thanksMoneyExt.setPendingPaySerialNo(thanksMoney.getPendingPaySerialNo());
        return FastJsonUtils.toJSONString(thanksMoneyExt);
    }

    /**
     * 供应商附加服务
     *
     * @param order order
     * @return ItemInfoDO
     */
    public ItemInfoDO v2dAdditionServer(OrderVO order, AdditionServerItemVO additionServerItemVO) {
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(additionServerItemVO.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(additionServerItemVO.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(additionServerItemVO.getCode()));
        itemInfo.setDescription(StringUtils.defaultString(additionServerItemVO.getName()));
        itemInfo.setBusinessOrderNo(StringUtils.EMPTY);
        itemInfo.setThirdOrderNo(StringUtils.EMPTY);
        itemInfo.setSupplierId(StringUtils.EMPTY);
        itemInfo.setSupplierCode(StringUtils.EMPTY);
        itemInfo.setSupplierName(StringUtils.EMPTY);
        itemInfo.setBusinessType(BusinessType.ADDITION_SERVER.getCode());
        itemInfo.setSpu(additionServerItemVO.getSpu());
        itemInfo.setSalePrice(additionServerItemVO.getSalePrice().getAmount().doubleValue());
        itemInfo.setSupplierPrice(additionServerItemVO.getSalePrice().getAmount().doubleValue());
        itemInfo.setRealPrice(additionServerItemVO.getRealPrice().getAmount().doubleValue());
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setExt(buildExt(additionServerItemVO));
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * Build ext string.
     * @return the string
     */
    private String buildExt(AdditionServerItemVO additionServerItemVO) {
        AdditionServerExtVO additionServerExtVO = new AdditionServerExtVO();
        additionServerExtVO.setCategory(additionServerItemVO.getCategory());
        additionServerExtVO.setSubCategory(additionServerItemVO.getSubCategory());
        additionServerExtVO.setSinglePrice(additionServerItemVO.getFee().getAmount());
        additionServerExtVO.setSalePrice(additionServerItemVO.getSalePrice().getAmount());
        additionServerExtVO.setTotalPrice(additionServerItemVO.getTotalFee().getAmount());
        additionServerExtVO.setFreeCount(additionServerItemVO.getFreeCount());
        additionServerExtVO.setCount(additionServerItemVO.getCount());
        //增加先收后退字段标识，统一增加，无论是否是否为收款项
        additionServerExtVO.setRefundAfterPay(additionServerItemVO.isRefundAfterPay());
        if (additionServerItemVO.getRefundAmountAfterPay() != null){
            additionServerExtVO.setRefundAmountAfterPay(additionServerItemVO.getRefundAmountAfterPay().getAmount().toPlainString());
        }
        return FastJsonUtils.toJSONString(additionServerExtVO);
    }

    /**
     * 构建活动信息
     *
     * @param order order
     * @param activity activity
     * @return ItemInfoDO
     */
    public ItemInfoDO v2dActivity(OrderVO order, ActivityVO activity) {
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(activity.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(activity.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(activity.getCode()));
        itemInfo.setDescription(StringUtils.defaultString(activity.getName()));
        itemInfo.setBusinessOrderNo(StringUtils.EMPTY);
        itemInfo.setThirdOrderNo(StringUtils.EMPTY);
        itemInfo.setSupplierId(StringUtils.EMPTY);
        itemInfo.setSupplierCode(StringUtils.EMPTY);
        itemInfo.setSupplierName(StringUtils.EMPTY);
        itemInfo.setBusinessType(BusinessType.ACTIVITY.getCode());
        itemInfo.setSpu(activity.getSpu());
        itemInfo.setSalePrice(activity.getSalePrice().getAmount().doubleValue());
        itemInfo.setSupplierPrice(activity.getSalePrice().getAmount().doubleValue());
        itemInfo.setRealPrice(activity.getRealPrice().getAmount().doubleValue());
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setExt(buildExt(activity));
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * Build ext string.
     *
     * @param activity the activity
     * @return the string
     */
    private String buildExt(ActivityVO activity) {
        ActivityExtVO activityExt = new ActivityExtVO();
        activityExt.setActivityCode(activity.getActivityCode());
        activityExt.setPromotionSign(activity.getPromotionSign());
        activityExt.setExt(activity.getExt());
        activityExt.setTool(activity.getTool());
        activityExt.setCategory(activity.getCategory());
        activityExt.setSubCategory(activity.getSubCategory());
        activityExt.setGoodsSerialNos(activity.getGoodsSerialNos());
        //增加先收后退字段标识，统一增加，无论是否是否为收款项
        activityExt.setRefundAfterPay(activity.isRefundAfterPay());
        if (activity.getRefundAmountAfterPay() != null){
            activityExt.setRefundAmountAfterPay(activity.getRefundAmountAfterPay().getAmount().toPlainString());
        }
        return FastJsonUtils.toJSONString(activityExt);
    }

    /**
     * 立减项
     *
     * @param order    the order
     * @param discount the discount
     * @return the item info do
     */
    public ItemInfoDO v2Discount(OrderVO order, DiscountVO discount) {
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(discount.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(discount.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(discount.getSpu()));
        itemInfo.setDescription(StringUtils.defaultString(discount.getName()));
        itemInfo.setBusinessOrderNo(StringUtils.EMPTY);
        itemInfo.setThirdOrderNo(StringUtils.EMPTY);
        itemInfo.setSupplierId(StringUtils.EMPTY);
        itemInfo.setSupplierCode(StringUtils.EMPTY);
        itemInfo.setSupplierName(StringUtils.EMPTY);
        itemInfo.setBusinessType(BusinessType.DISCOUNT.getCode());
        itemInfo.setSpu(discount.getSpu());
        itemInfo.setSalePrice(discount.getSalePrice().getAmount().doubleValue());
        itemInfo.setSupplierPrice(discount.getSalePrice().getAmount().doubleValue());
        itemInfo.setRealPrice(discount.getRealPrice().getAmount().doubleValue());
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setExt(buildExt(discount));
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * Build ext string.
     *
     * @param discount the discount
     * @return the string
     */
    private String buildExt(DiscountVO discount) {
        DiscountExtVO discountExt = new DiscountExtVO();
        discountExt.setType(discount.getType().name());
        discountExt.setCategory(discount.getCategory());
        discountExt.setSubCategory(discount.getSubCategory());
        //增加先收后退字段标识，统一增加，无论是否是否为收款项
        discountExt.setRefundAfterPay(discount.isRefundAfterPay());
        if (discount.getRefundAmountAfterPay() != null){
            discountExt.setRefundAmountAfterPay(discount.getRefundAmountAfterPay().getAmount().toPlainString());
        }
        return FastJsonUtils.toJSONString(discountExt);
    }

    /**
     * 构建商品信息
     *
     * @param item item
     * @param state state
     * @param ticketExt ticketExt
     * @return TicketVO
     */
    public TicketVO d2vTicket(ItemInfoDO item, GoodsState state, TicketExtVO ticketExt) {
        return TicketVO.builder()//
            .updateTime(item.getUpdateTime())
            .name(item.getProductName())//
            .code(item.getProductCode())//
            .itemId(item.getItemId())//
            .businessNo(item.getBusinessOrderNo())//
            .thirdNo(item.getThirdOrderNo())//
            .spu(item.getSpu())//
            .state(state)//
            .category(ticketExt.getCategory())//
            .subCategory(ticketExt.getSubCategory())//
            // 供应商信息
            .supplierId(item.getSupplierId())//
            .supplierCode(item.getSupplierCode())//
            .supplierName(item.getSupplierName())//
            .supplierNameToClient(item.getSupplierNameToClient())//
            .supplierTags(ticketExt.getSupplierTags())//
            // 价格信息
            .salePrice(new Money(item.getSalePrice()))//
            .realPrice(new Money(item.getRealPrice()))//
            .supplierPrice(new Money(item.getSupplierPrice()))//
            .realSupplierPrice(new Money(ticketExt.getRealSupplierPrice()))//
            .cancelPrice(new Money(ticketExt.getCancelPrice()))//
            .originPenaltyAmountAfterCompareTicket(StringUtils.isNotBlank(ticketExt.getOriginPenaltyAmountAfterCompareTicket()) ? new Money(ticketExt.getOriginPenaltyAmountAfterCompareTicket())
                : new Money(ticketExt.getCancelPrice()))
            .totalVirtualPenaltyAmount(StringUtils.isNotBlank(ticketExt.getTotalVirtualPenaltyAmount()) ? new Money(ticketExt.getTotalVirtualPenaltyAmount()) : new Money())
            .realCancelPrice(Optional.ofNullable(ticketExt.getRealCancelPrice()).map(Money::new).orElse(null))//
            .disclaimer(ticketExt.getDisclaimer())//
            .cancelPriceAfterJudge(new Money(StringUtils.defaultIfBlank(ticketExt.getCancelPriceAfterJudge(), "0")))//
            .cancelPriceAfterWyDeduct(new Money(StringUtils.defaultIfBlank(ticketExt.getCancelPriceAfterWyDeduct(), "0")))//
            .wyDiscountPrice(new Money(StringUtils.defaultIfBlank(ticketExt.getWyDeductPrice(), "0")))//
            .attachTotalPrice(new Money(ticketExt.getAttachTotalPrice()))//
            .carTotalPrice(new Money(ticketExt.getCarTotalPrice()))//
            .carPrice(new Money(StringUtils.defaultIfBlank(ticketExt.getCarPrice(), "0")))//
            .changePriceCount(ticketExt.getChangePriceCount())//
            .closeOrderCount(ticketExt.getCloseOrderCount())//
            .flashSaleCouponPrice(new Money(StringUtils.defaultIfBlank(ticketExt.getFlashSaleCouponPrice(), "0")))//
            .attachFeeItems(buildFeeItems(ticketExt.getAttachFeeItems()))//
            .feeItems(buildFeeItems(ticketExt.getFeeItems()))//
            .supplierFeeItems(buildSupplierFeeItems(ticketExt.getSupplierFeeItems()))//
            // 车型信息
            .carType(ticketExt.getCarType() != null ? CarTypeEnum.of(ticketExt.getCarType()) : null)//
            .carTags(ticketExt.getCarTags())//
            .carNum(ticketExt.getCarNum())//
            .originalCarNum(ticketExt.getOriginalCarNum())//
            .brand(ticketExt.getBrand())//
            .color(ticketExt.getColor())//
            .vehicleCode(ticketExt.getVehicleCode())

            // 司机信息
            .driverName(ticketExt.getDriverName())//
            .driverPhone(ticketExt.getDriverPhone())//
            .driverCode(ticketExt.getDriverCode())//
            .driverVirtualPhone(ticketExt.getDriverVirtualPhone())//
            .level(ticketExt.getLevel())//
            .wechat(ticketExt.getWechat())//
            .driverChatCode(ticketExt.getDriverChatCode())//
            // 资源信息
            .supplierChargeType(SupplierChargeTypeEnum.of(ticketExt.getSupplierChargeType()))//
            .chargeType(ChargeTypeEnum.of(ticketExt.getChargeType()))//
            .resourceId(ticketExt.getResourceId())//
            .resourceType(ticketExt.getResourceType())//
            .runTime(ticketExt.getRunTime())//
            .runDistance(ticketExt.getRunDistance())//
            .realRunTime(ticketExt.getRealRunTime())//
            .realRunDistance(ticketExt.getRealRunDistance())//
            .isInterlinkOrder(ticketExt.isInterlinkOrder())//
            .interlinkOrderInfo(ticketExt.getInterlinkOrderInfo())//
            .priceInfo(buildPriceInfo(ticketExt.getPriceInfo()))//
            .serviceType(ticketExt.getServiceType() != null ? ServiceType.of(ticketExt.getServiceType()) : null)//
            .iconInfo(ticketExt.getIconInfo())//
            .depositPrice(StringUtils.isBlank(ticketExt.getDepositPrice()) ? new Money() : new Money(ticketExt.getDepositPrice()))//
            .rate(ticketExt.getRate())//
            .salePoolPrice(StringUtils.isBlank(ticketExt.getSalePoolPrice()) ? new Money() : new Money(ticketExt.getSalePoolPrice()))//
            .promotionCode(ticketExt.getPromotionCode())
            .maxReturn(ticketExt.getMaxReturn())
            .driverArrTime(ticketExt.getDriverArrTime())
            .penaltyAmount(ticketExt.getPenaltyAmount())
            .originPenaltyAmount(ticketExt.getOriginPenaltyAmount())
            .judgmentResult(ticketExt.getJudgmentResult())
            .noLiabilityScene(ticketExt.getNoLiabilityScene())
            .driverDistance(ticketExt.getDriverDistance())
            .promotionCode(ticketExt.getPromotionCode())//
            .maxReturn(ticketExt.getMaxReturn())//
            .mesosphereSerialNo(ticketExt.getMesosphereSerialNo())//
            .configCode(ticketExt.getConfigCode())//
            .cashBackPrice(ticketExt.getCashBackPrice())//
            .donateActivityAmount(StringUtils.isBlank(ticketExt.getDonateActivityAmount()) ? new Money() : new Money(ticketExt.getDonateActivityAmount()))//
            .payFlag(item.getPayFlag())
            .priceAdjust(ticketExt.getPriceAdjust())
            .subProductName(ticketExt.getSubProductName())
            .subCarModel(ticketExt.getSubCarModel())
            .refundBuyAncillaries(ticketExt.getRefundBuyAncillaries())
            .refundAfterPay(ticketExt.isRefundAfterPay())
            .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(ticketExt.getRefundAmountAfterPay(), "0")))
            .estimatedReceivingTime(ticketExt.getEstimatedReceivingTime())
            .accelerationRecordNo(ticketExt.getAccelerationRecordNo())
            .actualResourceId(ticketExt.getActualResourceId())
            .bookResourceId(ticketExt.getBookResourceId())
            .hitRiskType(ticketExt.getHitRiskType())
            .build();
    }

    /**
     * Build price info sfc price info vo.
     *
     * @param priceInfo the price info
     * @return the sfc price info vo
     */
    private SfcPriceInfoVO buildPriceInfo(SfcPriceInfoExtVO priceInfo) {
        SfcPriceInfoVO priceInfoVO = new SfcPriceInfoVO();
        priceInfoVO.setExclusivePrice(new Money(priceInfo.getExclusivePrice()));
        priceInfoVO.setCarpoolPrice(new Money(priceInfo.getCarpoolPrice()));
        priceInfoVO.setWillingCarpoolPrice(new Money(priceInfo.getWillingCarpoolPrice()));
        return priceInfoVO;
    }

    /**
     * Build fee items list.
     *
     * @param attachFeeItems the attach fee items
     * @return the list
     */
    private List<TicketVO.FeeItem> buildFeeItems(List<TicketExtVO.FeeItem> attachFeeItems) {
        List<TicketVO.FeeItem> feeItems = Lists.newArrayList();
        if (CollectionUtils.isEmpty(attachFeeItems)) {
            return feeItems;
        }
        for (TicketExtVO.FeeItem item : attachFeeItems) {
            TicketVO.FeeItem feeItem = new TicketVO.FeeItem();
            feeItem.setPrice(new Money(item.getPrice()));
            feeItem.setType(CarFeeType.getByCode(item.getType()));
            feeItem.setOriginalType(item.getOriginalType());
            feeItems.add(feeItem);
        }
        return feeItems;
    }

    /**
     * Build fee items list.
     *
     * @param attachFeeItems the attach fee items
     * @return the list
     */
    private List<TicketVO.SupplierFeeItem> buildSupplierFeeItems(List<TicketExtVO.SupplierFeeItem> attachFeeItems) {
        List<TicketVO.SupplierFeeItem> feeItems = Lists.newArrayList();
        if (CollectionUtils.isEmpty(attachFeeItems)) {
            return feeItems;
        }
        for (TicketExtVO.SupplierFeeItem item : attachFeeItems) {
            TicketVO.SupplierFeeItem feeItem = new TicketVO.SupplierFeeItem();
            feeItem.setPrice(new Money(item.getPrice()));
            feeItem.setType(SupplierCarFeeType.getByCode(item.getType()));
            feeItems.add(feeItem);
        }
        return feeItems;
    }

    /**
     * 拷贝车票对象
     *
     * @param originTicketVO 原车票对象
     * @return {@link TicketVO}
     */
    public abstract TicketVO cloneTicketInfo(TicketVO originTicketVO);

    /**
     * 拷贝辅营对象
     *
     * @param originAncillaryVO 原辅营对象
     * @return {@link AncillaryVO}
     */
    public abstract AncillaryVO cloneAncillaryInfo(AncillaryVO originAncillaryVO);

    /**
     * 构建商品信息
     *
     * @param item item
     * @param state state
     * @param ext ext
     * @return CouponVO
     */
    public CouponVO d2vCoupon(ItemInfoDO item, GoodsState state, CouponExtVO ext) {
        return CouponVO.builder()//
            .name(item.getProductName())//
            .code(item.getProductCode())//
            .itemId(item.getItemId())//
            .businessNo(item.getBusinessOrderNo())//
            .thirdNo(item.getThirdOrderNo())//
            .spu(item.getSpu())//
            .state(state)//
            .category(ext.getCategory())//
            .subCategory(ext.getSubCategory())//
            .salePrice(new Money(item.getSalePrice()))//
            .realPrice(new Money(item.getRealPrice()))//
            .couponNo(item.getBusinessOrderNo())//
            .isFlashSale(ext.isFlashSale())//
            .batchNo(ext.getBatchNo())//
            .couponType(ext.getType())//
            .refundAfterPay(ext.isRefundAfterPay())
            .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(ext.getRefundAmountAfterPay(), "0")))
            .build();
    }

    /**
     * 构建商品信息
     *
     * @param item item
     * @param state state
     * @param mileageExt mileageExt
     * @return MileageVO
     */
    public MileageVO d2vMileage(ItemInfoDO item, GoodsState state, MileageExtVO mileageExt) {
        return MileageVO.builder()//
            .name(item.getProductName())//
            .code(item.getProductCode())//
            .itemId(item.getItemId())//
            .businessNo(item.getBusinessOrderNo())//
            .thirdNo(item.getThirdOrderNo())//
            .spu(item.getSpu())//
            .state(state)//
            .category(mileageExt.getCategory())//
            .subCategory(mileageExt.getSubCategory())//
            .salePrice(new Money(item.getSalePrice()))//
            .realPrice(new Money(item.getRealPrice()))//
            .rate(mileageExt.getRate())//
            .mileage(mileageExt.getMileage())//
            .ruleCode(mileageExt.getRuleCode())//
            .refundAfterPay(mileageExt.isRefundAfterPay())
            .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(mileageExt.getRefundAmountAfterPay(), "0")))
            .build();
    }

    /**
     * 构建商品信息
     *
     * @param item item
     * @param state state
     * @param ext ext
     * @return AncillaryVO
     */
    public AncillaryVO d2vAncillary(ItemInfoDO item, GoodsState state, AncillaryExtVO ext) {
        return AncillaryVO.builder()//
            .name(item.getProductName())//
            .code(item.getProductCode())//
            .itemId(item.getItemId())//
            .businessNo(item.getBusinessOrderNo())//
            .thirdNo(item.getThirdOrderNo())//
            .spu(item.getSpu())//
            .state(state)//
            .category(ext.getCategory())//
            .subCategory(ext.getSubCategory())//
            .salePrice(new Money(item.getSalePrice()))//
            .realPrice(new Money(item.getRealPrice()))//
            .supplierPrice(new Money(item.getSupplierPrice()))//
            .dataId(ext.getDataId())//
            .shelfCode(ext.getShelfCode())//
            .spuCode(ext.getSpuCode())//
            .skuCode(ext.getSkuCode())//
            .saleMode(AncillarySaleMode.getByCode(ext.getSaleMode()))//
            .singleSale(ext.isSingleSale())//
            .quantity(ext.getQuantity())//
            .useExtJson(ext.getUseExtJson())//
            .packType(ext.getPackType())//
            .packItemId(ext.getPackItemId())//
            .packItemProductName(ext.getPackItemProductName())//
            .packItemSkuCode(ext.getPackItemSkuCode())//
            .supplierAccountCompany(ext.getSupplierAccountCompany())//
            .extJson(ext.getExtJson())//
            .payFlag(item.getPayFlag())
            .wyDiscountPrice(new Money(StringUtils.defaultIfBlank(ext.getWyDiscountPrice(),"0")))//
            .refundAfterPay(ext.isRefundAfterPay())
            .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(ext.getRefundAmountAfterPay(), "0")))
            .build();
    }

    /**
     * 构建商品信息
     *
     * @param item item
     * @param state state
     * @param ext ext
     * @return ThanksMoneyVO
     */
    public VirtualItemVO d2vVirtualItem(ItemInfoDO item, GoodsState state, VirtualItemExtVO ext) {
        return VirtualItemVO.builder()//
            .name(item.getProductName())//
            .code(item.getProductCode())//
            .itemId(item.getItemId())//
            .businessNo(item.getBusinessOrderNo())//
            .thirdNo(item.getThirdOrderNo())//
            .spu(item.getSpu())//
            .state(state)//
            .category(ext.getCategory())//
            .subCategory(ext.getSubCategory())//
            .salePrice(new Money(item.getSalePrice()))//
            .realPrice(new Money(item.getRealPrice()))//
            .mainBusinessType(ext.getMainBusinessType() != null ? BusinessType.getByCode(ext.getMainBusinessType()) : BusinessType.UNKNOWN)//
            .mainItemId(ext.getMainItemId())//
            .payFlag(item.getPayFlag())
            .refundAfterPay(ext.isRefundAfterPay())
            .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(ext.getRefundAmountAfterPay(), "0")))
            .build();
    }

    /**
     * 构建商品信息
     *
     * @param item item
     * @param state state
     * @param ext ext
     * @return ThanksMoneyVO
     */
    public ActivityVO d2vActivity(ItemInfoDO item, GoodsState state, ActivityExtVO ext) {
        return ActivityVO.builder()//
            .name(item.getProductName())//
            .code(item.getProductCode())//
            .itemId(item.getItemId())//
            .businessNo(item.getBusinessOrderNo())//
            .thirdNo(item.getThirdOrderNo())//
            .spu(item.getSpu())//
            .state(state)//
            .category(ext.getCategory())//
            .subCategory(ext.getSubCategory())//
            .salePrice(new Money(item.getSalePrice()))//
            .realPrice(new Money(item.getRealPrice()))//
            .activityCode(ext.getActivityCode())//
            .promotionSign(ext.getPromotionSign())//
            .tool(ext.getTool())//
            .ext(ext.getExt())//
            .payFlag(item.getPayFlag())
            .refundAfterPay(ext.isRefundAfterPay())
            .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(ext.getRefundAmountAfterPay(), "0")))
            .build();
    }

    /**
     * 构建商品信息
     *
     * @param item item
     * @param state state
     * @param ext ext
     * @return ThanksMoneyVO
     */
    public ThanksMoneyVO d2vThanksMoney(ItemInfoDO item, GoodsState state, ThanksMoneyExtVO ext) {
        return ThanksMoneyVO.builder()//
            .name(item.getProductName())//
            .code(item.getProductCode())//
            .itemId(item.getItemId())//
            .businessNo(item.getBusinessOrderNo())//
            .thirdNo(item.getThirdOrderNo())//
            .spu(item.getSpu())//
            .state(state)//
            .category(ext.getCategory())//
            .subCategory(ext.getSubCategory())//
            .salePrice(new Money(item.getSalePrice()))//
            .realPrice(new Money(item.getRealPrice()))//
            .payFlag(item.getPayFlag())
            .refundAfterPay(ext.isRefundAfterPay())
            .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(ext.getRefundAmountAfterPay(), "0")))
            .appendToSupplier(ext.getAppendToSupplier())
            .pendingPaySerialNo(ext.getPendingPaySerialNo())
            .build();
    }

    /**
     * 构建商品信息
     *
     * @param item item
     * @param state state
     * @param ext ext
     * @return ThanksMoneyVO
     */
    public DiscountVO d2vDiscount(ItemInfoDO item, GoodsState state, DiscountExtVO ext) {
        return DiscountVO.builder()//
            .name(item.getProductName())//
            .code(item.getProductCode())//
            .itemId(item.getItemId())//
            .businessNo(item.getBusinessOrderNo())//
            .thirdNo(item.getThirdOrderNo())//
            .spu(item.getSpu())//
            .state(state)//
            .category(ext.getCategory())//
            .subCategory(ext.getSubCategory())//
            .salePrice(new Money(item.getSalePrice()))//
            .realPrice(new Money(item.getRealPrice()))//
            .type(DiscountType.getByName(ext.getType()))//
            .payFlag(item.getPayFlag())
            .refundAfterPay(ext.isRefundAfterPay())
            .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(ext.getRefundAmountAfterPay(), "0")))
            .build();
    }

    /**
     * 权益项
     *
     * @param order    the order
     * @param discount the discount
     * @return the item info do
     */
    public ItemInfoDO v2RightDiscount(OrderVO order, RightsVO discount) {
        //下单&询价时优惠金额
        double salePrice = discount.getRightSalePrice().getAmount().doubleValue();
        ItemInfoDO itemInfo = new ItemInfoDO();
        itemInfo.setItemId(discount.getItemId());
        itemInfo.setOrderSerialNo(order.getOrderSerialNo());
        itemInfo.setProductName(StringUtils.defaultString(discount.getName()));
        itemInfo.setProductCode(StringUtils.defaultString(discount.getCategory()));
        itemInfo.setDescription(discount.getType().getDesc());
        itemInfo.setBusinessOrderNo(discount.getRightCode());
        itemInfo.setThirdOrderNo(StringUtils.EMPTY);
        itemInfo.setSupplierId(StringUtils.EMPTY);
        itemInfo.setSupplierCode(StringUtils.EMPTY);
        itemInfo.setSupplierName(StringUtils.EMPTY);
        itemInfo.setBusinessType(BusinessType.RIGHT_DISCOUNT.getCode());
        itemInfo.setSpu(discount.getCode());
        itemInfo.setSalePrice(salePrice);
        itemInfo.setSupplierPrice(salePrice);
        //实际优惠金额
        itemInfo.setRealPrice(discount.getRealPrice().getAmount().doubleValue());
        itemInfo.setTraceId(order.getTraceId());
        itemInfo.setMemberId(order.getMemberInfo().getId());
        itemInfo.setIsDelete(BooleanInt.N.getCode());
        itemInfo.setExt(buildExt(discount));
        itemInfo.setCreateTime(new Date());
        itemInfo.setUpdateTime(new Date());
        itemInfo.setEnv(CfgUtils.getEnv());
        return itemInfo;
    }

    /**
     * Build ext string.
     *
     * @param discount the discount
     * @return the string
     */
    private String buildExt(RightsVO discount) {
        RightDiscountExtVO discountExt = new RightDiscountExtVO();
        discountExt.setRightPrice(discount.getRightPrice().getAmount().doubleValue());
        discountExt.setData(discount.getData());
        discountExt.setConfigTextList(discount.getConfigTextList());
        discountExt.setCategory(discount.getCategory());
        discountExt.setSubCategory(discount.getSubCategory());
        discountExt.setRightCode(discount.getRightCode());
        discountExt.setOccupyDiscount(discount.getOccupyDiscount());
        discountExt.setRealRightsDiscount(discount.getRealRightsDiscount());
        discountExt.setRightsStatus(discount.getRightsStatus());
        discountExt.setExceedDuration(discount.getExceedDuration());
        discountExt.setExceedTotalDuration(discount.getExceedTotalDuration());
        discountExt.setExceedMileage(discount.getExceedMileage());
        discountExt.setExceedTotalExceedMileage(discount.getExceedTotalExceedMileage());
        discountExt.setTotalDeduction(discount.getTotalDeduction());

        return FastJsonUtils.toJSONString(discountExt);
    }

    /**
     * 构建商品信息
     *
     * @param item item
     * @param state state
     * @param ext ext
     * @return ThanksMoneyVO
     */
    public RightsVO d2vRightDiscount(ItemInfoDO item, GoodsState state, RightDiscountExtVO ext) {
        return RightsVO.builder()//
            .name(item.getProductName())//
            .code(item.getProductCode())//
            .itemId(item.getItemId())//
            .businessNo(item.getBusinessOrderNo())//
            .thirdNo(item.getThirdOrderNo())//
            .spu(item.getSpu())//
            .state(state)//
            .category(ext.getCategory())//
            .subCategory(ext.getSubCategory())//
            .salePrice(new Money(item.getSalePrice()))//
            .rightSalePrice(new Money(item.getSalePrice()))//
            .realPrice(new Money(item.getRealPrice()))//
            .type(RightDiscountType.getByType(Integer.valueOf(ext.getCategory())))//
            .configTextList(ext.getConfigTextList())//
            .data(ext.getData())//
            .rightPrice(new Money(ext.getRightPrice()))//
            .limitMileage(ext.getLimitMileage())//
            .rightCode(ext.getRightCode())//
            .occupyDiscount(ext.getOccupyDiscount())//
            .realRightsDiscount(ext.getRealRightsDiscount())//
            .rightsStatus(ext.getRightsStatus())//
            .exceedDuration(ext.getExceedDuration())//
            .exceedTotalDuration(ext.getExceedTotalDuration())//
            .exceedMileage(ext.getExceedMileage()) //
            .exceedTotalExceedMileage(ext.getExceedTotalExceedMileage()) //
            .totalDeduction(ext.getTotalDeduction())
            .payFlag(item.getPayFlag())
            .refundAfterPay(ext.isRefundAfterPay())
            .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(ext.getRefundAmountAfterPay(), "0")))
            .build();
    }

    /**
     * 构建附加服务信息
     *
     * @param item item
     * @param state state
     * @param ext ext
     * @return ThanksMoneyVO
     */
    public AdditionServerItemVO d2vAdditionServer(ItemInfoDO item, GoodsState state, AdditionServerExtVO ext) {
        return AdditionServerItemVO.builder()//
                .name(item.getProductName())//
                .code(item.getProductCode())//
                .itemId(item.getItemId())//
                .businessNo(item.getBusinessOrderNo())//
                .thirdNo(item.getThirdOrderNo())//
                .spu(item.getSpu())//
                .state(state)//
                .category(ext.getCategory())//
                .subCategory(ext.getSubCategory())//
                .salePrice(new Money(item.getSalePrice()))//
                .realPrice(new Money(item.getRealPrice()))//
                .payFlag(item.getPayFlag())
                .fee(new Money(ext.getSinglePrice()))
                .count(ext.getCount())
                .freeCount(ext.getFreeCount())
                .totalFee(new Money(ext.getTotalPrice()))
                .refundAfterPay(ext.isRefundAfterPay())
                .refundAmountAfterPay(new Money(StringUtils.defaultIfBlank(ext.getRefundAmountAfterPay(), "0")))
                .build();
    }

}
