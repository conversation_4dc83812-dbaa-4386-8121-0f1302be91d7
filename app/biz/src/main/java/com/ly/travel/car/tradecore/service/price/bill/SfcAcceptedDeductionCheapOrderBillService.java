package com.ly.travel.car.tradecore.service.price.bill;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.tradecore.base.ctx.Context;
import com.ly.travel.car.tradecore.business.finished.model.OrderFinishedSupplierBill;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.BusinessType;
import com.ly.travel.car.tradecore.model.enums.OrderTag;
import com.ly.travel.car.tradecore.model.goods.BaseGoodsVO;
import com.ly.travel.car.tradecore.model.goods.CouponVO;
import com.ly.travel.car.tradecore.model.price.BaseGoodsWrapper;
import com.ly.travel.car.tradecore.model.trip.ResourceVO;
import com.ly.travel.car.tradecore.service.price.PriceUtils;
import com.ly.travel.car.tradecore.service.price.model.OrderBillContext;
import com.ly.travel.car.tradecore.service.price.model.OrderBillWrapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 顺风车特惠供应商完单 &已支付后的账单
 */
@Slf4j
@Service
public class SfcAcceptedDeductionCheapOrderBillService extends BaseSfcOrderBillService {
    /**
     * 行程结束账单计算服务
     *
     * @param context the context
     * @param details the details
     * @return the car bill wrapper
     */
    @Override
    public OrderBillWrapper bill(Context context, OrderFinishedSupplierBill details) {
        LoggerUtils.info(log, "特惠车主:账单:开始");
        return super.bill(context, details);
    }

    @Override
    public ResourceVO getTicketResource(OrderBillContext orderBillContext) {
        return orderBillContext.getOrder().getActualResource();
    }

    @Override
    public Money getNewRealPrice(OrderBillContext orderBillContext) {
        OrderVO order = orderBillContext.getOrder();
        ResourceVO actualResource = order.getActualResource();
        Money salePrice = actualResource.getSalePrice();
        LoggerUtils.info(log, "特惠车主:账单:新价: 真实resourceId: {} 真实价格: {} ", actualResource.getResourceId(), salePrice.getAmount().toPlainString());
        return salePrice;
    }

    @Override
    public Money getOldTicketTotalSalePrice(OrderBillContext orderBillContext) {
        return defaultGetOldTicketTotalSalePrice(orderBillContext.getOrder());
    }

    @Override
    public Money getNewTicketTotalSalePrice(OrderBillContext orderBillContext) {
        OrderVO order = orderBillContext.getOrder();
        Money newRealPrice = orderBillContext.getNewRealPrice();
        List<BaseGoodsVO> allNegativeGoods = getCheapNegativeGoods(order);
        // 新总价(新的用车费+保险+新的全部抵扣项实抵金额)
        List<BaseGoodsWrapper> negativeGoodsWrappers = PriceUtils.getNegativeGoodsWrapper(allNegativeGoods);
        Money deductionPrice = PriceUtils.calcDeductionPricWithNewCarPrice(negativeGoodsWrappers, newRealPrice, order.getAllInsurePrice(), order);
        return newRealPrice.add(order.getAllInsurePrice()).add(deductionPrice);
    }

    /**
     * 获取特惠 负的金额&修正劵金额
     */
    private static List<BaseGoodsVO> getCheapNegativeGoods(OrderVO order) {
        ResourceVO actualResource = order.getActualResource();
        //取出 实际接单供应商的优惠信息
        List<ResourceVO.ResourceCouponVO> coupons = actualResource.getCoupons();
        Map<String, ResourceVO.ResourceCouponVO> couponMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(coupons)) {
            for (ResourceVO.ResourceCouponVO coupon : coupons) {
                couponMap.put(coupon.getCouponNo(), coupon);
            }
        }
        //取出order里的劵(保存用的这个)
        List<CouponVO> orderCoupons = order.getCoupons();
        Map<String,CouponVO> orderCouponsMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(orderCoupons)){
            for(CouponVO couponVO:orderCoupons){
                orderCouponsMap.put(couponVO.getItemId(),couponVO);
            }
        }
        //修正 特惠车主 优惠金额
        List<BaseGoodsVO> allNegativeGoods = order.getAllNegativeGoods();
        for (BaseGoodsVO goods : allNegativeGoods) {
            String businessNo = goods.getBusinessNo();
            if (!Objects.equals(BusinessType.COUPON, goods.businessType())) {
                continue;
            }
            if (!couponMap.containsKey(businessNo)) {
                LoggerUtils.error(log, "特惠车主:账单:新车价:找不到对应的优惠信息，businessNo:{}", businessNo);
                continue;
            }
            //开始修正
            ResourceVO.ResourceCouponVO resourceCouponVO = couponMap.get(businessNo);
            Money oldSalePrice = goods.getSalePrice();
            Money oldCouponAmount = goods.getRealPrice();
            Money newCouponAmount = resourceCouponVO.getAmount();
            goods.setSalePrice(newCouponAmount);
            goods.setRealPrice(newCouponAmount);
            LoggerUtils.info(log, "特惠车主:账单:新车价:businessNo:{} 金额替换为:{} 老金额real:{} 老金额 sale:{}", businessNo, newCouponAmount.getAmount().toPlainString(),
                    oldCouponAmount.getAmount().toPlainString(), oldSalePrice.getAmount().toPlainString());
            //更新orderVo里的劵金额.保存用
            String itemId = goods.getItemId();
            if(orderCouponsMap.containsKey(itemId)){
                CouponVO couponVO = orderCouponsMap.get(itemId);
                couponVO.setRealPrice(newCouponAmount);
                couponVO.setSalePrice(newCouponAmount);
                LoggerUtils.info(log, "特惠车主:账单:新车价:itemId:{} 金额替换为:{} 老金额:{}", itemId, newCouponAmount.getAmount().toPlainString(),
                        oldCouponAmount.getAmount().toPlainString());
            }

        }
        return allNegativeGoods;
    }

    @Override
    public void billChange(OrderBillContext orderBillContext) {
        orderBillContext.getUserBill().addNewBillTag(OrderTag.SFC_FUT);
    }
}
