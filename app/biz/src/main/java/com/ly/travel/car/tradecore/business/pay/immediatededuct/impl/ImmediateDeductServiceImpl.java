package com.ly.travel.car.tradecore.business.pay.immediatededuct.impl;

import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.tradecore.business.BaseBusinessService;
import com.ly.travel.car.tradecore.business.pay.immediatededuct.ImmediateDeductService;
import com.ly.travel.car.tradecore.business.pay.model.ImmediateDeductContext;
import com.ly.travel.car.tradecore.business.pay.model.PaymentPointDelayDeductContext;
import com.ly.travel.car.tradecore.dal.daointerface.OrderMqMonitorDAO;
import com.ly.travel.car.tradecore.error.BizErrorFactory;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.facade.request.pay.ImmediateDeductRequestDTO;
import com.ly.travel.car.tradecore.facade.response.pay.ImmediateDeductResponseDTO;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.OrderTag;
import com.ly.travel.car.tradecore.model.order.DelayDeductInfoVO;
import com.ly.travel.car.tradecore.producer.payload.PaymentPointDeductPayload;
import com.ly.travel.car.tradecore.service.pay.PayService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ImmediateDeductServiceImpl extends BaseBusinessService implements ImmediateDeductService {

    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();

    @Resource
    private OrderMqMonitorDAO            orderMqMonitorDAO;

    @Resource
    private PayService                   payService;

    /**
     * 立即扣款
     *
     * @param context
     */
    @Override
    public ImmediateDeductResponseDTO immediateDeduct(ImmediateDeductContext context) throws BuilderException {
        OrderVO order = context.getOrder();
        ImmediateDeductRequestDTO requestDTO = context.getRequestDTO();
        String traceId = requestDTO.getTraceId();
        if (!(order.containsAnyTag(Lists.newArrayList(OrderTag.HAS_DELAY_DEDUCT_PENALTY, OrderTag.HAS_DELAY_DEDUCT_NORMAL)))) {
            throw new BuilderException(BIZ_ERROR_FACTORY.immediateDeductError("非延时扣款订单"));
        }
        if (order.isPaid()) {
            throw new BuilderException(BIZ_ERROR_FACTORY.immediateDeductError("已支付"));
        }
        if (order.isPaymentPointDeductAll()) {
            throw new BuilderException(BIZ_ERROR_FACTORY.immediateDeductError("已发起扣款"));
        }
        // 延迟扣款信息
        DelayDeductInfoVO delayDeduct = order.getDelayDeduct();
        LoggerUtils.info(log, "延迟扣款:立即扣款:delayDeduct{}", FastJsonUtils.toJSONString(delayDeduct));
        if (Objects.isNull(delayDeduct) || StringUtils.isBlank(delayDeduct.getPaymentPointDeductPayload())) {
            throw new BuilderException(BIZ_ERROR_FACTORY.immediateDeductError("扣款信息不存在"));
        }
        PaymentPointDeductPayload data = FastJsonUtils.fromJSONString(delayDeduct.getPaymentPointDeductPayload(), PaymentPointDeductPayload.class);
        PaymentPointDelayDeductContext paymentPointDelayDeductContext = PaymentPointDelayDeductContext.of(order, data);
        boolean result = payService.delayDeductPayOrderForPayPoint(paymentPointDelayDeductContext);
        if (result) {
            return ImmediateDeductResponseDTO.success(traceId);
        }
        throw new BuilderException(BIZ_ERROR_FACTORY.immediateDeductError("扣款发起失败"));
    }
}
