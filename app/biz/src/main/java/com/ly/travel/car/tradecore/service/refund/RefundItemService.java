package com.ly.travel.car.tradecore.service.refund;

import java.util.List;

import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.refund.RefundItemInfoVO;

/**
 * 退款明细服务
 *
 * <AUTHOR>
 * @version Id: RefundItemService, v 0.1 2024/3/4 20:37 wuyayuan Exp $
 */
public interface RefundItemService {

    /**
     * 插入退款明细
     *
     * @param order order
     * @param refundItemInfoVO the refund item info vo
     */
    void insert(OrderVO order, RefundItemInfoVO refundItemInfoVO);

    /**
     * 根据订单号查询退款明细
     *
     * @param orderSerialNo the refund serial no
     * @return list
     */
    List<RefundItemInfoVO> queryByOrderSerialNo(String orderSerialNo);

    /**
     * 根据退订单号查询退款明细
     *
     * @param refundSerialNo the refund serial no
     * @return list
     */
    List<RefundItemInfoVO> queryByRefundSerialNo(String orderSerialNo,String refundSerialNo);

    /**
     *  根据订单号和商品ID查询退款明细
     * @param orderSerialNo
     * @param itemIds
     * @return
     */
    List<RefundItemInfoVO> queryByItemIds(String orderSerialNo, List<String> itemIds);

    /**
     * 删除refund_item
     * @param orderSerialNo the order serial no
     * @param refundSerialNo the refund serial no
     */
    boolean delete(String orderSerialNo, String refundSerialNo);
}
