package com.ly.travel.car.tradecore.business.cancel.processor.paycancel;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.tradecore.business.cancel.model.CancelContext;
import com.ly.travel.car.tradecore.error.OrderCancelException;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.HistoryScene;
import com.ly.travel.car.tradecore.model.enums.PayState;
import com.ly.travel.car.tradecore.model.enums.RefundScene;
import com.ly.travel.car.tradecore.order.GoodsCmdService;
import com.ly.travel.car.tradecore.service.config.ConfigCenter;
import com.ly.travel.car.tradecore.service.price.model.DeductAmountVO;
import com.ly.travel.car.tradecore.service.refund.model.RefundContext;
import com.ly.travel.car.tradecore.utils.CfgUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 在线支付取消处理器
 * <AUTHOR>
 * @version Id: OnlinePayCancelProcessor, v 0.1 2024/2/29 14:19 wuyayuan Exp $
 */
@Service("onlinePayCancelProcessor")
@Slf4j
public class OnlinePayCancelProcessor extends DefaultPayCancelProcessor {

    @Resource
    private GoodsCmdService goodsCmdService;


    /**
     * 未支付下,取消支付
     */
    protected void doCancelPay(CancelContext context) {
        OrderVO order = context.getOrder();
        // 顺风车场景：下单支付分支付，扣款失败/超时被转前付
        if (isPayScoreFailureSwitchToOnline(order)) {
            if (context.hasCancelFee()) {
                // 有损
                DeductAmountVO deductAmount = context.getDeductAmount();
                LoggerUtils.info(log, "[OnlinePayCancelProcessor][支付分转在线支付取消] 未支付，有取消费，支付状态：{} ,需扣款金额：{}", order.getPayState(), deductAmount.getDeductAmount().getAmount());

                //更新票价
                Money realPrice = addAdditionalAncillaryAfterPaid(deductAmount);
                goodsCmdService.updateTicketPrice(context.getOrderSerialNo(), context.getOrder().getTicket().getItemId(), realPrice);
                context.getOrder().getTicket().setRealPrice(realPrice);

                // 更新支付状态
                orderService.updatePayStatus(context.getOrder(), PayState.UNPAID, PayState.CANCEL_FEE_UNPAID);

                // 更新订单总金额
                orderService.updateOrderAmount(context.getOrder(), HistoryScene.CANCEL_PRICE);

                // 关闭支付分订单
                payService.cancelPayOrderForPayScore(context, "顺风车后付失败转前付，有损取消支付违约金");

                // 构建先收后退款项增加标识
                updateRefundAfterPayGoods(context);
                return;
            }
            LoggerUtils.info(log, "[OnlinePayCancelProcessor][支付分转在线支付取消] 未支付，无损取消，订单状态：{}", order.getPayState());
            // 取消支付单
            payService.payCancel(context);
            return;
        }

        // 纯在线支付

        // 有取消费,理论上不存在此场景
        if (context.hasCancelFee()) {
            DeductAmountVO deductAmount = context.getDeductAmount();
            LoggerUtils.warn(log, "[OnlinePayCancelProcessor][在线支付取消]未支付，有取消费，请检查!! 支付状态：{} ,需扣款金额：{}", order.getPayState(), deductAmount.getDeductAmount().getAmount());
            // 取消支付单
            payService.payCancel(context);
//            //更新票价
//            Money realPrice = addAdditionalAncillaryAfterPaid(deductAmount);
//            goodsCmdService.updateTicketPrice(context.getOrderSerialNo(), context.getOrder().getTicket().getItemId(), realPrice);
//            context.getOrder().getTicket().setRealPrice(realPrice);
//            // 更新支付状态
//            orderService.updatePayStatus(context.getOrder(), PayState.UNPAID, PayState.CANCEL_FEE_UNPAID);
//            // 更新订单总金额
//            orderService.updateOrderAmount(context.getOrder(), HistoryScene.CANCEL_PRICE);
            return;
        }

        // 无取消费
        LoggerUtils.info(log, "[OnlinePayCancelProcessor][在线支付取消]未支付，无需退款，订单状态：{}", order.getPayState());
        // 取消支付单
        payService.payCancel(context);
    }

    /**
     * realCancelPrice加上取消时购买的商品(取消返现)
     */
    private Money addAdditionalAncillaryAfterPaid(DeductAmountVO deductAmount) {
        // 由于在线支付未支付下,不允许购买取消返现,因此additionalAncillaryAfterPaidAmount值为0
        return deductAmount.getPenaltyAmount().add(deductAmount.getAdditionalAncillaryAfterPaidAmount());
    }

    /**
     * 支付分扣款失败 转前付
     * @param order
     * @return
     */
    private boolean isPayScoreFailureSwitchToOnline(OrderVO order) {
        return CfgUtils.payScoreToOnlineSwitch() && order.isSfc() && order.isPayScoreDeductFail();
    }

    @Override
    public void doProcess(CancelContext context) throws OrderCancelException {
        super.doProcess(context);
    }

}
