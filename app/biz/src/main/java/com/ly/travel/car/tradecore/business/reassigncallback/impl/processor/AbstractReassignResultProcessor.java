package com.ly.travel.car.tradecore.business.reassigncallback.impl.processor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.tradecore.business.ctx.StandardOrderContext;
import com.ly.travel.car.tradecore.business.reassigncallback.impl.ReassignResultProcessor;
import com.ly.travel.car.tradecore.business.reassigncallback.model.ReassignCallbackContext;
import com.ly.travel.car.tradecore.business.traveling.model.MessageType;
import com.ly.travel.car.tradecore.enums.PaymentPointTypeEnum;
import com.ly.travel.car.tradecore.integration.Symbols;
import com.ly.travel.car.tradecore.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.*;
import com.ly.travel.car.tradecore.model.trip.ResourceVO;
import com.ly.travel.car.tradecore.order.OrderService;
import com.ly.travel.car.tradecore.producer.TradeProducer;
import com.ly.travel.car.tradecore.producer.source.OrderPriceTraceLogSource;
import com.ly.travel.car.tradecore.service.activity.ActivityService;
import com.ly.travel.car.tradecore.service.ancillary.AncillaryService;
import com.ly.travel.car.tradecore.service.distribution.DistributionService;
import com.ly.travel.car.tradecore.service.free.ActivityFreeOrderService;
import com.ly.travel.car.tradecore.service.log.LogContext;
import com.ly.travel.car.tradecore.service.log.LogModule;
import com.ly.travel.car.tradecore.service.log.OperateLogService;
import com.ly.travel.car.tradecore.service.pay.PayService;
import com.ly.travel.car.tradecore.service.push.PushService;
import com.ly.travel.car.tradecore.service.rule.CancelRuleService;
import com.ly.travel.car.tradecore.service.tracer.TracerService;
import com.ly.travel.car.tradecore.utils.CfgUtils;
import com.ly.travel.car.tradecore.utils.PayUtils;
import com.ly.travel.car.tradecore.utils.PushUtils;
import com.ly.travel.car.tradecore.utils.RedisKeyBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * 改派结果处理器抽象类
 *
 * <AUTHOR>
 * @version Id:AbstractReassignResultProcessor.java,v 0.1 2024/3/16 14:25 chengweiwen Exp $$
 */
@Slf4j
public abstract class AbstractReassignResultProcessor implements ReassignResultProcessor {

    @Resource(name = "orderService")
    protected OrderService                          orderService;

    @Resource(name = "ancillaryService")
    protected AncillaryService                      ancillaryService;

    @Resource
    protected CancelRuleService                     cancelRuleService;

    @Resource
    protected TracerService                         frontTracerService;

    @Resource
    protected PushService                           pushService;

    @Resource
    protected OperateLogService                     operateLogService;

    @Resource
    protected ActivityFreeOrderService              activityFreeOrderService;

    @Resource
    private DistributionChangeProcessor             distributionChangeProcessor;

    @Resource
    protected ActivityService                       activityService;

    @Resource
    private TradeProducer<OrderPriceTraceLogSource> orderPriceTraceLogProducer;

    @Resource
    protected RedisClientProxy                      redisClientProxy;

    @Resource
    private DistributionService                     distributionService;
    
    @Resource
    private PayService                              payService;

    public void process(StandardOrderContext orderContext, ReassignCallbackContext reassignContext) {

        // 1、更新订单数据
        buildPushSmsFlag(orderContext, reassignContext);
        updateOrder(orderContext, reassignContext);

        // 2、更新取消订单违约金计算规则快照
        updateRuleSnapshot(orderContext);

        // 3、通知辅营商品换购
        applyAncillaryReassign(orderContext, reassignContext);

        // 4、通知用户订单改派结果
        notifyUserResult(orderContext, reassignContext);

        // 5.订单搜索报价日志链路追踪
        if (orderContext.getOrder().isSendTracePriceLog()) {
            if (reassignContext.getScene().equals(ReassignResultCallbackScene.REASSIGN_SUCCESS)) {
                int reassignType = Optional.ofNullable(orderContext.getOrder().getOrderExtVO().getReassignType()).orElse(0);
                //  1-供应链触发改派，2-供应商触发改派
                if (reassignType == 1) {
                    orderPriceTraceLogProducer.send(new OrderPriceTraceLogSource(orderContext.getOrder(), OrderNotifyPriceMessageTypeEnum.ORDER_REASSIGN_ACCEPTED_SUPPLY_SYSTEM));
                } else {
                    orderPriceTraceLogProducer.send(new OrderPriceTraceLogSource(orderContext.getOrder(), OrderNotifyPriceMessageTypeEnum.ORDER_REASSIGN_ACCEPTED_DRIVER));
                }

            } else if (reassignContext.getScene().equals(ReassignResultCallbackScene.DRIVER_CHANGE)) {
                // 6.订单搜索报价日志链路追踪
                orderPriceTraceLogProducer.send(new OrderPriceTraceLogSource(orderContext.getOrder(), OrderNotifyPriceMessageTypeEnum.ORDER_REASSIGN_ACCEPTED_DRIVER));
            }
        }

        // 页面分销通知订单状态
        distributionService.notifyOrderStateForPageDistribution(orderContext.getOrder(), OrderState.RECEIVING_ORDER, true);

        // 推送灵动岛
        pushService.pushIsland(orderContext.getOrder(), orderContext.getOrder().getOrderType().isTiming() ? MessageType.DRIVER_DEPARTURE_NOTICE : null, PushIslandScene.RE_RECEIVING_ORDER);

        //顺风车司机接单扣款需要重新发起扣款
        delayDeduct(orderContext);
        
    }

    /**
     * 构建是否推送短信标识
     * @param reassignContext
     */
    public void buildPushSmsFlag(StandardOrderContext orderContext, ReassignCallbackContext reassignContext) {
        boolean pushSms = true;
        ReassignResultCallbackScene scene = reassignContext.getScene();
        LoggerUtils.info(log, "改派优化:是否发短信:场景:{}", scene.getDesc());
        if (Objects.equals(scene, ReassignResultCallbackScene.DRIVER_CHANGE)) {
            pushSms = !reassignContext.isSameDriver(orderContext.getOrder());
            LoggerUtils.info(log, "改派优化:是否发短信:司机改派:是否要发短信:{}", pushSms);
        }
        LoggerUtils.info(log, "改派优化:是否发短信:最终结果{}", pushSms);
        reassignContext.setPushSms(pushSms);
    }

    /**
     * 更新订单数据
     *
     * @param orderContext 订单上下文
     * @param reassignContext 改派结果回调上下文
     */
    protected void updateOrder(StandardOrderContext orderContext, ReassignCallbackContext reassignContext) {
        OrderVO orderVO = orderContext.getOrder();
        orderService.updateOrderForReassignResult(orderVO, reassignContext.getResource(), reassignContext.getDispatchType());
        // 顺风车 -> 更新资源状态
        if (orderVO.isSfc()) {
            //原来的Resource
            List<ResourceVO> updateResource = Lists.newArrayList(reassignContext.getResource());
            //实际接单的Resource 和 下单的不一样 就一起扔进去
            if (!StringUtils.isBlank(orderVO.getTicket().getActualResourceId()) && !StringUtils.equals(orderVO.getTicket().getActualResourceId(), reassignContext.getResource().getResourceId())) {
                updateResource.add(orderVO.getActualResource());
            }
            // 更新资源状态
            orderService.updateResourceInfo(orderVO, updateResource, ResourceState.SUCCESS);
        }
    }

    /**
     * 更新取消订单违约金计算规则快照
     *
     * @param orderContext 订单上下文
     */
    protected void updateRuleSnapshot(StandardOrderContext orderContext) {
        // 更新取消订单违约金计算规则快照
        cancelRuleService.update(orderContext);
    }

    /**
     * 通知辅营商品换购
     * 
     * @param orderContext 订单上下文
     * @param reassignContext 改派结果回调上下文
     */
    protected void applyAncillaryReassign(StandardOrderContext orderContext, ReassignCallbackContext reassignContext) {
        // 如果已支付,再尝试发送辅营正式单,因为辅营的换购基于正式单
        if (orderContext.getOrder().isPaid()) {
            // 辅营正式单
            ancillaryService.book(orderContext, null);
        }

        redisClientProxy.setnx(RedisKeyBuilder.orderProcessing(orderContext.getOrder().getOrderSerialNo()), RedisKeyBuilder.ORDER_PROCESSING_VALUE, 2);
        // 辅营换购
        ancillaryService.applyReassign(orderContext, reassignContext.getResource(), ApplyReassignScene.REASSIGN);
    }

    /**
     * 通知用户订单改派结果
     *
     * @param orderContext    订单上下文
     * @param reassignContext the reassign context
     */
    protected void notifyUserResult(StandardOrderContext orderContext, ReassignCallbackContext reassignContext) {
        // 删除改派中的轨迹
        frontTracerService.clear(orderContext.getOrder(), UserSceneEnum.SUPPLIER_REASSIGN_NOTICE);
        // 改派完成轨迹通知
        frontTracerService.notify(orderContext.getOrder(), UserSceneEnum.SUPPLIER_REASSIGN_RESULT_NOTICE);
        OrderVO order = orderContext.getOrder();
        // 短信推送 网约车：SCENE_008，顺风车：SCENE_005
        if (reassignContext.isPushSms()) {
            if (order.isCar() || order.isXCar()) {
                pushService.push(order, PushUtils.getCarBlackSmsScene(PushSmsScene.SCENE_008, order));
            } else {
                pushService.push(order, PushSmsScene.SCENE_005);
            }
        }

        // 操作日志
        operatorLog(orderContext, reassignContext);
    }

    /**
     * 改派后面单逻辑重新判断
     * @param orderContext
     */
    protected void resetFreeOrderTag(StandardOrderContext orderContext){
        // 更新免单判断
        activityFreeOrderService.resetOrderTag(orderContext.getOrder());
    }

    /**
     * 操作日志
     *
     * @param context         the context
     * @param reassignContext the reassign context
     */
    protected void operatorLog(StandardOrderContext context, ReassignCallbackContext reassignContext) {
        Map<String, Object> ext = Maps.newHashMap();
        ext.put("acceptedResource", reassignContext.getResource());
        ext.put("reassignType", reassignContext.getScene());
        LogContext logContext = LogContext.builder().logModule(LogModule.REASSIGN).order(context.getOrder()).ext(ext).build();
        operateLogService.log(logContext);
    }

    /**
     * 司机接单发起扣款订单，改派时需要重新发起扣款
     */
    protected void delayDeduct(StandardOrderContext context) {
        OrderVO order = context.getOrder();
        if (!order.isUnPaid()) {
            return;
        }
        if (order.hasDeducted()) {
            return;
        }
        if (context.getOrder().hasNormDelayDeduct()) {
            PaymentPointTypeEnum paymentPointType = PayUtils.getPaymentPointType(order.getPayCategory(), order.getOrderTags());
            List<OrderTag> orderTags = PayUtils.initDeductCarFeeOrderTags(paymentPointType);
            boolean success = payService.deductPayOrderForPayScore(context);
            // 支付分扣款失败，更新支付方式
            if (!success) {
                orderService.switchPayCategory(context.getOrder(), PayCategory.ONLINE, "顺风车接单时支付，支付分扣款失败", Symbols.SYSTEM_NAME);
                // 添加支付分扣款失败的tag
                PayUtils.addDeductFailTag(orderTags, paymentPointType);
                if (CfgUtils.payScoreToOnlineSwitch()) {
                    // 更新支付分扣款失败时间
                    orderService.updatePayScoreDeductFailTime(order);
                }
            }
            if (CfgUtils.payScoreToOnlineSwitch()) {
                // 支付分扣款用车费用
                orderService.updateOrderTags(order, orderTags);
            }
        }
    }

}
