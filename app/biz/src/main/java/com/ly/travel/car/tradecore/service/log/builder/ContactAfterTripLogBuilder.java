package com.ly.travel.car.tradecore.service.log.builder;

import com.google.common.collect.Lists;
import com.ly.travel.car.orderservice.facade.model.log.OrderLog;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.service.log.LogBuilder;
import com.ly.travel.car.tradecore.service.log.LogContext;
import com.ly.travel.car.tradecore.service.log.LogExt;
import com.ly.travel.car.tradecore.service.log.LogModule;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ContactAfterTripLogBuilder extends AbstractLogBuilder implements LogBuilder {
    /**
     * 构建日志
     *
     * @param context 日志上下文
     * @return 日志
     */
    @Override
    public List<OrderLog> build(LogContext context) {
        LogModule logModule = context.getLogModule();
        OrderVO order = context.getOrder();
        String content = (String) context.getExt(LogExt.LOG_EXT_CONTENT, false);
        OrderLog log = createLog(logModule, context.getOperator(), order, content);
        return Lists.newArrayList(log);
    }

    /**
     * 获取模块
     *
     * @return 模块
     */
    @Override
    public LogModule module() {
        return LogModule.CONTACT_AFTER_TRIP;
    }
}
