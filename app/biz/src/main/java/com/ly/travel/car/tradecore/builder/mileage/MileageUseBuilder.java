package com.ly.travel.car.tradecore.builder.mileage;

import java.util.Date;
import java.util.Optional;

import com.ly.flight.marketing.mileage.facade.request.mileage.CarOrderInfoDTO;
import com.ly.travel.car.tradecore.model.ext.ResourceExtVO;
import com.ly.travel.car.tradecore.model.trip.ResourceVO;
import org.springframework.stereotype.Service;

import com.ly.flight.marketing.mileage.facade.request.MileageUseReqDTO;
import com.ly.travel.car.common.utils.DateUtils;
import com.ly.travel.car.tradecore.base.Builder;
import com.ly.travel.car.tradecore.business.book.model.BaseBookContext;
import com.ly.travel.car.tradecore.error.BuilderException;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.goods.MileageVO;
import com.ly.travel.car.tradecore.model.order.MemberInfoVO;
import com.ly.travel.car.tradecore.utils.Constants;

/**
 * 里程使用构建器
 * 
 * <AUTHOR>
 * @version Id: MileageUseBuilder, v 0.1 2023/12/18 14:41 icanci Exp $
 */
@Service("mileageUseBuilder")
public class MileageUseBuilder implements Builder<BaseBookContext, MileageUseReqDTO> {

    /**
     * 构建
     *
     * @param bookContext 原始输入
     * @return 构建出来目标对象
     * @throws BuilderException 构建异常
     */
    @Override
    public MileageUseReqDTO build(BaseBookContext bookContext) throws BuilderException {
        OrderVO order = bookContext.getOrder();
        MemberInfoVO memberInfo = order.getMemberInfo();
        MileageVO mileage = order.getMileage();
        MileageUseReqDTO mileageUseReqDTO = new MileageUseReqDTO();

        mileageUseReqDTO.setScenes(Constants.MILEAGE_SCENE);
        mileageUseReqDTO.setOrderNo(order.getOrderSerialNo());
        mileageUseReqDTO.setRuleCode(mileage.getRuleCode());
        mileageUseReqDTO.setPrice(order.getTicketsAmount());
        mileageUseReqDTO.setDePrice(mileage.getSalePrice().getAmount().abs());
        mileageUseReqDTO.setDeMiles(mileage.getMileage());
        mileageUseReqDTO.setDeRate(mileage.getRate());
        mileageUseReqDTO.setUnionId(memberInfo.getUnionId());
        mileageUseReqDTO.setOpenId(memberInfo.getOpenId());
        mileageUseReqDTO.setMemberId(memberInfo.getId());
        mileageUseReqDTO.setRequestTime(DateUtils.getY4M2d2H2m2s2WebString(new Date()));
        mileageUseReqDTO.setChannel(order.getBookInfo().getOrderChannel());
        mileageUseReqDTO.setSource(Constants.MILEAGE_SOURCE);
        mileageUseReqDTO.setTraceId(bookContext.getTraceId());
        mileageUseReqDTO.setJumpMileagesValid(false);
        mileageUseReqDTO.setCarOrderInfo(buildCarOrderInfoDTO(order));
        return mileageUseReqDTO;
    }

    /**
     *
     * @param order
     * @return
     */
    public CarOrderInfoDTO buildCarOrderInfoDTO(OrderVO order){
        CarOrderInfoDTO carOrderInfoDTO = new CarOrderInfoDTO();
        carOrderInfoDTO.setOrderType(String.valueOf(order.getOrderType().getCrmType()));
        //获取公里数传给营销
        Integer distance = Optional.ofNullable(order.getTicketResource())
                .map(ResourceVO::getResourceExtVO)
                .map(ResourceExtVO::getBasePriceRule)
                .map(ResourceExtVO.BasePriceRuleExt::getDistance)
                .orElse(null);
        if (distance == null || distance == 0) {
            Double runDistance = Optional.ofNullable(order.getTicketResource())
                    .map(ResourceVO::getResourceExtVO)
                    .map(ResourceExtVO::getRunDistance)
                    .orElse(null);
            distance = Optional.ofNullable(runDistance).map(Double::intValue).orElse(null);
        }
        if (distance != null) {
            //将米转为公里，保留2位小数
            double distanceKilometer = distance / 1000.0;
            carOrderInfoDTO.setDistance(String.format("%.2f", distanceKilometer));
        }
        return carOrderInfoDTO;
    }
}