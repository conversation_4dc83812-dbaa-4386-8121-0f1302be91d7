package com.ly.travel.car.tradecore.business.cancel.impl;

import java.math.BigDecimal;

import javax.annotation.Resource;

import com.ly.travel.car.tradecore.enums.PaySceneTypeEnum;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.ly.sof.api.error.LYError;
import com.ly.sof.utils.common.Money;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.tradecore.business.cancel.model.CancelContext;
import com.ly.travel.car.tradecore.error.BizErrorFactory;
import com.ly.travel.car.tradecore.error.OrderCancelException;
import com.ly.travel.car.tradecore.facade.request.trade.CancelRequestDTO;
import com.ly.travel.car.tradecore.facade.response.trade.CancelResponseDTO;
import com.ly.travel.car.tradecore.facade.response.trade.PenaltyInquiryResponseDTO;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.*;
import com.ly.travel.car.tradecore.model.goods.TicketVO;
import com.ly.travel.car.tradecore.order.GoodsCmdService;
import com.ly.travel.car.tradecore.service.distribution.DistributionService;
import com.ly.travel.car.tradecore.utils.RedisKeyBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * 分销订单取消实现类
 *
 * <AUTHOR>
 * @version Id: DistributionCancelService  2024/5/14
 */
@Service("distributionCancelService")
@Slf4j
public class DistributionCancelService extends DefaultOrderCancelService {

    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();

    @Resource
    protected GoodsCmdService            goodsCmdService;

    @Resource
    private DistributionService          distributionService;

    @Override
    public CancelResponseDTO cancel(CancelContext context) {
        // 是否持有锁
        boolean holdLock = false;
        try {
            holdLock = redisClientProxy.setnx(RedisKeyBuilder.cancelLock(context.getOrderSerialNo()), context.getOrderSerialNo(), 60);
            if (!holdLock) {
                throw new OrderCancelException(BIZ_ERROR_FACTORY.repeatedCancel(context.getOrderSerialNo()));
            }
            // 0.订单状态校验
            validateOrderState(context);

            // 1.计算违约金  改成私有方法
            if (penaltyValidateProcessor.needCancel(context) || context.getCancelRequestDTO().getCancelType() == CancelType.CUSTOMER_SERVICE_CANCEL.getCode()) {
                updateCancelFee(context);
            }
            // 2.供应链取消订单
            supplierCancelProcessor.doProcess(context);

            // 3.db操作 更新订单状态&取消原因
            orderService.updateOrderState(context.getOrder(), UserState.CANCELED, context.getOrder().getOrderState(), OrderState.CANCELED);
            orderService.updateCancelReason(context.getOrder(), context.getCancelRequestDTO().getCancelType(), context.getCancelRequestDTO().getReason(),
                context.getOrderSerialNo());
            //兜底取消原因类别
            CancelRequestDTO cancelRequestDTO = context.getCancelRequestDTO();
            CancelReasonType cancelReasonType = CancelReasonType.getByCode(cancelRequestDTO.getCancelReasonType(), cancelRequestDTO.getCancelType(), cancelRequestDTO.getReason());
            context.getOrder().setCancelReasonType(cancelReasonType);
            orderService.updateOrderExt(context.getOrder());

            // 4.取消商品(优惠券、里程、辅营等)
            goodsItemCancelProcessor.doProcess(context);

            //判断如果是系统
            if (!context.isDistributionCancel()) {
                distributionService.notifyOrderStateForApiDistribution(context.getOrder(), OrderState.CANCELED, false);
            }
            OrderVO order = context.getOrder();
            if (hasCancelPrice(order.getTicket().getCancelPrice())) {
                if (order.isUnPaid()) {
                    orderService.updatePayStatus(order, order.getPayState(), PayState.CANCEL_FEE_UNPAID);
                } else if (order.isPaid() && !order.needPayNotify()) {
                    // 兼容旧逻辑
                    // 分销订单 违约金 > 0 通知供应链违约金支付成功
                    supplierService.syncPaid(context.getOrder(), PaySceneTypeEnum.CANCEL_FEE);
                }
            }
            //记录取消日志
            operatorLog(context);
        } catch (OrderCancelException e) {
            LoggerUtils.warn(log, "订单取消失败：{}", e, e.getMessage());
            // 违约金变更取消,返回变更后违约金金额
            LYError lyError = BIZ_ERROR_FACTORY.cancelPenaltyChange(null, null, null);
            if (e.getErrorCode().equals(lyError.getCode())) {
                return CancelResponseDTO.fail4PenaltyAmountChanged(context.getCancelRequestDTO().getTraceId(), e.getErrorCode(), e.getMessage(), e.getPenaltyAmountAfterChange());
            }
            return CancelResponseDTO.fail(context.getCancelRequestDTO().getTraceId(), e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "订单取消异常：{}", e, e.getMessage());
            return CancelResponseDTO.fail(context.getCancelRequestDTO().getTraceId(), "-1", e.getMessage());
        } finally {
            if (holdLock) {
                redisClientProxy.remove(RedisKeyBuilder.cancelLock(context.getOrderSerialNo()));
            }
        }
        return CancelResponseDTO.success(context.getCancelRequestDTO().getTraceId(), context.getCancelRequestDTO().getOrderSerialNo());
    }

    private boolean hasCancelPrice(Money cancelPrice) {
        return cancelPrice.getAmount().compareTo(BigDecimal.ZERO) > 0;
    }

    private void updateCancelFee(CancelContext context) {
        CancelRequestDTO cancelRequestDTO = context.getCancelRequestDTO();
        PenaltyInquiryResponseDTO penaltyInquiry = new PenaltyInquiryResponseDTO();

        penaltyInquiry.setPenaltyAmount(cancelRequestDTO.getPenaltyConfirmAmount());
        penaltyInquiry.setOriginPenaltyAmount(cancelRequestDTO.getPenaltyConfirmAmount());
        penaltyInquiry.setOriginPenaltyAmountAfterCompareTicket(cancelRequestDTO.getPenaltyConfirmAmount());
        penaltyInquiry.setUseWyDeductFlag(Boolean.FALSE);
        context.getCancelRequestDTO().setPenaltyConfirmAmount(penaltyInquiry.getPenaltyAmount());
        context.setPenaltyInquiry(penaltyInquiry);
        // 更新违约金
        OrderVO order = context.getOrder();
        TicketVO ticket = order.getTicket();
        ticket.setCancelPrice(new Money(penaltyInquiry.getOriginPenaltyAmount()));
        ticket.setOriginPenaltyAmountAfterCompareTicket(new Money(penaltyInquiry.getOriginPenaltyAmountAfterCompareTicketWithDefault()));
        ticket.setRealCancelPrice(new Money(penaltyInquiry.getPenaltyAmount()));
        goodsCmdService.updateTicketExt(order.getOrderSerialNo(), ticket.getItemId(), FastJsonUtils.toJSONString(ticket.getTicketExtVO()));
        log.info("[违约金校验]更新违约金成功，orderSerialNo={}, itemId={}, cancelPrice={}, originPenaltyAmountAfterCompareTicket={}, realCancelPrice={}", order.getOrderSerialNo(), ticket.getItemId(),
            penaltyInquiry.getOriginPenaltyAmount(), penaltyInquiry.getOriginPenaltyAmountAfterCompareTicketWithDefault(), penaltyInquiry.getPenaltyAmount());

        // 更新订单标签，有责取消
        if (!context.getCancelRequestDTO().getDisclaimer()) {
            orderService.updateOrderTags(context.getOrder(), Lists.newArrayList(OrderTag.CANCEL_WITH_PENALTY));
        }
    }
}
