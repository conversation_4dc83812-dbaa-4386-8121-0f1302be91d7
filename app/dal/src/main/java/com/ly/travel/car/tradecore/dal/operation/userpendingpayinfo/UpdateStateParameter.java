
/**
 * LY.com Inc.
 * Copyright (c) 2004-2016 All Rights Reserved.
 */
package com.ly.travel.car.tradecore.dal.operation.userpendingpayinfo;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: car_user_pending_pay_info
 * database table comments: UserPendingPayInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR> <PERSON>)
 * */
public class UpdateStateParameter  implements java.io.Serializable {
	private static final long serialVersionUID = -5216457518046898601L;
	
	/** 支付状态：0：待支付，1：已支付 */
	private Integer state;
	/** 支付完成时间 */
	private java.util.Date gmtPaid;
	/** 待支付记录付单号 */
	private String paySerialNo;
	/** 订单流水号 */
	private String orderSerialNo;
	
	public UpdateStateParameter() {
	}
	
	public UpdateStateParameter(Integer state ,java.util.Date gmtPaid ,String paySerialNo ,String orderSerialNo ) {
		this.state = state;
		this.gmtPaid = gmtPaid;
		this.paySerialNo = paySerialNo;
		this.orderSerialNo = orderSerialNo;
	}
	
	public Integer getState() {
		return state;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	public java.util.Date getGmtPaid() {
		return gmtPaid;
	}
	public void setGmtPaid(java.util.Date gmtPaid) {
		this.gmtPaid = gmtPaid;
	}
	public String getPaySerialNo() {
		return paySerialNo;
	}
	public void setPaySerialNo(String paySerialNo) {
		this.paySerialNo = paySerialNo;
	}
	public String getOrderSerialNo() {
		return orderSerialNo;
	}
	public void setOrderSerialNo(String orderSerialNo) {
		this.orderSerialNo = orderSerialNo;
	}
	
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
