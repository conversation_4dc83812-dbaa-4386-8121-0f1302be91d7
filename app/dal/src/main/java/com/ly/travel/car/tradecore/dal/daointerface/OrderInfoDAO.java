/**
 * LY.com Inc.
 * Copyright (c) 2004-2016 All Rights Reserved.
 */package com.ly.travel.car.tradecore.dal.daointerface;
import com.ly.travel.car.tradecore.dal.dataobject.OrderInfoDO;
import com.ly.travel.car.tradecore.dal.operation.orderinfo.UpdateLinkPhoneParameter;
import com.ly.travel.car.tradecore.dal.operation.orderinfo.UpdateOrderStateParameter;
import org.springframework.dao.DataAccessException;

import java.util.List;
/**
 * OrderInfoDAO
 * database table: car_order_info
 * database table comments: OrderInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 * */
public interface OrderInfoDAO {




	/**
	 *
	 * sql:
	 * <pre>INSERT      INTO         car_order_info         (    order_serial_no ,    pay_state ,    order_state ,    user_state ,    order_channel ,    order_type ,    order_tags ,    contact_name ,    contact_phone ,    area_code ,    union_id ,    open_id ,    member_id ,    client_ip ,    device_id ,    gmt_paid ,    gmt_canceled ,    gmt_usage ,    usage_delay ,    gmt_departure ,    gmt_arrive ,    gmt_pay_out ,    gmt_dispatched ,    gmt_reassigned ,    gmt_driver_arrived ,    gmt_passenger_board ,    gmt_trip_finished ,    gmt_pay_score_deduct ,    cancel_type ,    cancel_reason ,    trace_id ,    amount ,    receive_channel ,    pay_category ,    ref_id ,    sms_scene ,    reassign_num ,    is_delete ,    ext ,    env ,    contact_virtual_phone ,    send_itinerary_count ,    traffic_no ,    gmt_dispatch,    gmt_dispatched_notify ,    CreateTime ,    UpdateTime    )      VALUES         (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now())</pre>
	 */
	public long insert(OrderInfoDO orderInfo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         is_delete = 1,    UpdateTime = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int deleteOrder(String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         gmt_usage = ?,    usage_delay = ?,    UpdateTime = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int updateGmtUsage(java.util.Date gmtUsage ,Integer usageDelay ,String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         UpdateTime = now()     ,       contact_phone = ?         ,       contact_virtual_phone = ?             WHERE         order_serial_no = ?</pre>
	 */
	public int updateLinkPhone(UpdateLinkPhoneParameter param) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         cancel_type = ?,    cancel_reason = ?,    UpdateTime = now(),    gmt_canceled = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int updateCancelReason(Integer cancelType ,String cancelReason ,String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info         SET         reassign_num = ?, UpdateTime = now()         WHERE         order_serial_no = ?          AND env = ?</pre>
	 */
	public int updateReassignNum(Integer reassignNum ,String orderSerialNo ,String env) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         order_state = ?,    user_state = ?,    UpdateTime = now()     ,       gmt_dispatched = ?         ,       gmt_reassigned = ?         ,       gmt_driver_arrived = ?         ,       gmt_passenger_board = ?         ,       gmt_trip_finished = ?         ,       gmt_canceled = ?         ,       gmt_departure = ?         ,       gmt_arrive = ?         ,       gmt_dispatch = ?         ,       gmt_dispatched_notify = ?             WHERE         order_serial_no = ?          and order_state = ?</pre>
	 */
	public int updateOrderState(UpdateOrderStateParameter param) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         user_state = ?,    UpdateTime = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int updateUserState(Integer userState ,String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         order_tags = ?,    UpdateTime = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int updateOrderTags(String orderTags ,String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         ext = ?,    UpdateTime = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int updateExt(String ext ,String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         pay_category = ?,    UpdateTime = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int updatePayCategory(Integer payCategory ,String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         pay_state = ?,    gmt_paid = now(),    UpdateTime = now()         WHERE         order_serial_no = ?          and pay_state = ?</pre>
	 */
	public int updatePayStatus(Integer newPayState ,String orderSerialNo ,Integer oldPayState) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         pay_state = ?,    UpdateTime = now()         WHERE         order_serial_no = ?          and pay_state = ?</pre>
	 */
	public int updatePayStatusOnly(Integer newPayState ,String orderSerialNo ,Integer oldPayState) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         gmt_pay_score_deduct = ?,    UpdateTime = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int updatePayScoreDeductTime(java.util.Date gmtPayScoreDeduct ,String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         amount = ?,     UpdateTime = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int updateOrderAmount(Double amount ,String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_order_info      SET         receive_channel = ?,     UpdateTime = now()         WHERE         order_serial_no = ?</pre>
	 */
	public int updateReceiveChannel(Integer receiveChannel ,String orderSerialNo) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>SELECT         id,   order_serial_no,   pay_state,   order_state,   user_state,   order_channel,   order_type,   order_tags,   contact_name,   contact_phone,   contact_virtual_phone,   area_code,   union_id,   open_id,   member_id,   client_ip,   device_id,   gmt_paid,   gmt_canceled,   gmt_usage,   usage_delay,   gmt_departure,   gmt_arrive,   gmt_pay_out,   gmt_dispatched,   gmt_reassigned,   gmt_driver_arrived,   gmt_passenger_board,   gmt_trip_finished,   gmt_pay_score_deduct,   cancel_type,   cancel_reason,   trace_id,   amount,   receive_channel,   pay_category,   ref_id,   sms_scene,   reassign_num,   send_itinerary_count,   is_delete,   CreateTime,   UpdateTime,   ext,   env,   traffic_no,   gmt_dispatch,   gmt_dispatched_notify           FROM         car_order_info         WHERE         order_serial_no = ?             and env = ?</pre>
	 */
	public OrderInfoDO queryByOrderSerialNo(String orderSerialNo ,String env) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>SELECT         id,   order_serial_no,   pay_state,   order_state,   user_state,   order_channel,   order_type,   order_tags,   contact_name,   contact_phone,   contact_virtual_phone,   area_code,   union_id,   open_id,   member_id,   client_ip,   device_id,   gmt_paid,   gmt_canceled,   gmt_usage,   usage_delay,   gmt_departure,   gmt_arrive,   gmt_pay_out,   gmt_dispatched,   gmt_reassigned,   gmt_driver_arrived,   gmt_passenger_board,   gmt_trip_finished,   gmt_pay_score_deduct,   cancel_type,   cancel_reason,   trace_id,   amount,   receive_channel,   pay_category,   ref_id,   sms_scene,   reassign_num,   send_itinerary_count,   is_delete,   CreateTime,   UpdateTime,   ext,   env,   traffic_no,   gmt_dispatch,   gmt_dispatched_notify           FROM         car_order_info         WHERE         member_id = ?             AND union_id = ?             AND env = ?             AND order_type = ?             AND  order_state in  (             ?             )</pre>
	 */
	public List<OrderInfoDO> queryRiskOrders(String memberId ,String unionId ,String env ,Integer orderType ,java.util.List<Integer> orderStates) throws DataAccessException;

	/**
	 *
	 * @param gmtDispatchedNotify
	 * @param orderSerialNo
	 * @return
	 * @throws DataAccessException
	 */
	public int updateGmtDispatchedNotify(java.util.Date gmtDispatchedNotify ,String orderSerialNo) throws DataAccessException;

}



