/**
 * LY.com Inc.
 * Copyright (c) 2004-2016 All Rights Reserved.
 */package com.ly.travel.car.tradecore.dal.daointerface;

import java.util.List;

import org.springframework.dao.DataAccessException;

import com.ly.travel.car.tradecore.dal.dataobject.RefundItemInfoDO;
/**
 * RefundItemInfoDAO
 * database table: car_refund_item_info
 * database table comments: RefundItemInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR> <PERSON>)
 * */
public interface RefundItemInfoDAO {


	/**
	 * 
	 * sql:
	 * <pre>INSERT      INTO         car_refund_item_info         (             order_serial_no ,member_id ,refund_serial_no ,item_id ,item_price ,refund_price ,env ,is_delete ,CreateTime             ,UpdateTime,item_name,business_type,category,ext             )      VALUES         (?,?,?,?,?,?,?,?,now(),now(),?,?,?,?)</pre> 
	 */
	public long insert(RefundItemInfoDO refundItemInfo) throws DataAccessException;

	/**
	 * 
	 * sql:
	 * <pre>SELECT         id, order_serial_no, member_id, refund_serial_no, item_id, item_price, refund_price, env, is_delete, CreateTime,         UpdateTime ,item_name,business_type,category,ext                       FROM         car_refund_item_info                  WHERE         id = ?</pre> 
	 */
	public RefundItemInfoDO queryById(Long id) throws DataAccessException;

	/**
	 * 
	 * sql:
	 * <pre>SELECT         id, order_serial_no, member_id, refund_serial_no, item_id, item_price, refund_price, env, is_delete, CreateTime,         UpdateTime ,item_name,business_type,category,ext                       FROM         car_refund_item_info                  WHERE         is_delete = 0          and env = ?          and order_serial_no = ?</pre> 
	 */
	public List<RefundItemInfoDO> queryByOrderSerialNo(String env ,String orderSerialNo) throws DataAccessException;

	/**
	 * 
	 * sql:
	 * <pre>SELECT         id, order_serial_no, member_id, refund_serial_no, item_id, item_price, refund_price, env, is_delete, CreateTime,         UpdateTime ,item_name,business_type,category,ext                       FROM         car_refund_item_info                  WHERE         is_delete = 0          and env = ?          and order_serial_no = ?          and refund_serial_no = ?</pre> 
	 */
	public List<RefundItemInfoDO> queryByRefundSerialNo(String env ,String orderSerialNo ,String refundSerialNo) throws DataAccessException;

	/**
	 * 
	 * sql:
	 * <pre>SELECT         id, order_serial_no, member_id, refund_serial_no, item_id, item_price, refund_price, env, is_delete, CreateTime,         UpdateTime ,item_name,business_type,category,ext                       FROM         car_refund_item_info                  WHERE         order_serial_no=?                      and is_delete = 0                      and env = ?                       AND item_id in  (             ?                      )</pre> 
	 */
	public List<RefundItemInfoDO> queryByItemIds(String orderSerialNo ,String env ,java.util.List<String> itemIds) throws DataAccessException;

	/**
	 *
	 * sql:
	 * <pre>UPDATE         car_refund_item_info      SET         `is_delete` = 1,             UpdateTime = now()                  WHERE         order_serial_no = ?          and refund_serial_no = ?    and is_delete = 0      and env= ?</pre>
	 */
	public int delete(String env ,String orderSerialNo ,String refundSerialNo) throws DataAccessException;
}



