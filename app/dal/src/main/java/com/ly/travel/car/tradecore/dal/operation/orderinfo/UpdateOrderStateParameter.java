
/**
 * LY.com Inc.
 * Copyright (c) 2004-2016 All Rights Reserved.
 */
package com.ly.travel.car.tradecore.dal.operation.orderinfo;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: car_order_info
 * database table comments: OrderInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 * */
public class UpdateOrderStateParameter  implements java.io.Serializable {
	private static final long serialVersionUID = -5216457518046898601L;
	
	/** 订单状态（行程状态） */
	private Integer newOrderState;
	/** 订单对客状态，会在订单详情页对客外显 */
	private Integer userState;
	/** 派单成功时间（首次派单成功时间） */
	private java.util.Date gmtDispatched;
	/** 改派完成时间 */
	private java.util.Date gmtReassigned;
	/** 司机到达时间 */
	private java.util.Date gmtDriverArrived;
	/** 乘客上车时间 */
	private java.util.Date gmtPassengerBoard;
	/** 行程结束时间 */
	private java.util.Date gmtTripFinished;
	/** 订单取消时间 */
	private java.util.Date gmtCanceled;
	/** 出发时间（实际出发时间） */
	private java.util.Date gmtDeparture;
	/** 抵达时间（实际抵达时间） */
	private java.util.Date gmtArrive;
	/** 派单成功时间（首次派单成功时间） */
	private java.util.Date gmtDispatch;
	/** 派单成功通知时间（首次） */
	private java.util.Date gmtDispatchedNotify;
	/** 订单流水号 */
	private String orderSerialNo;
	/** 订单状态（行程状态） */
	private Integer oldOrderState;
	
	public UpdateOrderStateParameter() {
	}
	
	public UpdateOrderStateParameter(Integer newOrderState ,Integer userState ,java.util.Date gmtDispatched ,java.util.Date gmtReassigned ,java.util.Date gmtDriverArrived ,java.util.Date gmtPassengerBoard ,java.util.Date gmtTripFinished ,java.util.Date gmtCanceled ,java.util.Date gmtDeparture ,java.util.Date gmtArrive ,java.util.Date gmtDispatch ,java.util.Date gmtDispatchedNotify ,String orderSerialNo ,Integer oldOrderState ) {
		this.newOrderState = newOrderState;
		this.userState = userState;
		this.gmtDispatched = gmtDispatched;
		this.gmtReassigned = gmtReassigned;
		this.gmtDriverArrived = gmtDriverArrived;
		this.gmtPassengerBoard = gmtPassengerBoard;
		this.gmtTripFinished = gmtTripFinished;
		this.gmtCanceled = gmtCanceled;
		this.gmtDeparture = gmtDeparture;
		this.gmtArrive = gmtArrive;
		this.gmtDispatch = gmtDispatch;
		this.gmtDispatchedNotify = gmtDispatchedNotify;
		this.orderSerialNo = orderSerialNo;
		this.oldOrderState = oldOrderState;
	}
	
	public Integer getNewOrderState() {
		return newOrderState;
	}
	public void setNewOrderState(Integer newOrderState) {
		this.newOrderState = newOrderState;
	}
	public Integer getUserState() {
		return userState;
	}
	public void setUserState(Integer userState) {
		this.userState = userState;
	}
	public java.util.Date getGmtDispatched() {
		return gmtDispatched;
	}
	public void setGmtDispatched(java.util.Date gmtDispatched) {
		this.gmtDispatched = gmtDispatched;
	}
	public java.util.Date getGmtReassigned() {
		return gmtReassigned;
	}
	public void setGmtReassigned(java.util.Date gmtReassigned) {
		this.gmtReassigned = gmtReassigned;
	}
	public java.util.Date getGmtDriverArrived() {
		return gmtDriverArrived;
	}
	public void setGmtDriverArrived(java.util.Date gmtDriverArrived) {
		this.gmtDriverArrived = gmtDriverArrived;
	}
	public java.util.Date getGmtPassengerBoard() {
		return gmtPassengerBoard;
	}
	public void setGmtPassengerBoard(java.util.Date gmtPassengerBoard) {
		this.gmtPassengerBoard = gmtPassengerBoard;
	}
	public java.util.Date getGmtTripFinished() {
		return gmtTripFinished;
	}
	public void setGmtTripFinished(java.util.Date gmtTripFinished) {
		this.gmtTripFinished = gmtTripFinished;
	}
	public java.util.Date getGmtCanceled() {
		return gmtCanceled;
	}
	public void setGmtCanceled(java.util.Date gmtCanceled) {
		this.gmtCanceled = gmtCanceled;
	}
	public java.util.Date getGmtDeparture() {
		return gmtDeparture;
	}
	public void setGmtDeparture(java.util.Date gmtDeparture) {
		this.gmtDeparture = gmtDeparture;
	}
	public java.util.Date getGmtArrive() {
		return gmtArrive;
	}
	public void setGmtArrive(java.util.Date gmtArrive) {
		this.gmtArrive = gmtArrive;
	}
	public java.util.Date getGmtDispatch() {
		return gmtDispatch;
	}
	public void setGmtDispatch(java.util.Date gmtDispatch) {
		this.gmtDispatch = gmtDispatch;
	}
	public java.util.Date getGmtDispatchedNotify() {
		return gmtDispatchedNotify;
	}
	public void setGmtDispatchedNotify(java.util.Date gmtDispatchedNotify) {
		this.gmtDispatchedNotify = gmtDispatchedNotify;
	}
	public String getOrderSerialNo() {
		return orderSerialNo;
	}
	public void setOrderSerialNo(String orderSerialNo) {
		this.orderSerialNo = orderSerialNo;
	}
	public Integer getOldOrderState() {
		return oldOrderState;
	}
	public void setOldOrderState(Integer oldOrderState) {
		this.oldOrderState = oldOrderState;
	}
	
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
