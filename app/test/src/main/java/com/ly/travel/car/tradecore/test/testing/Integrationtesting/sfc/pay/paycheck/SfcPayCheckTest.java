package com.ly.travel.car.tradecore.test.testing.Integrationtesting.sfc.pay.paycheck;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import com.alibaba.citrus.util.Assert;
import com.google.common.collect.Lists;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.tradecore.business.ctx.StandardOrderContext;
import com.ly.travel.car.tradecore.facade.request.pay.PayCheckRequestDTO;
import com.ly.travel.car.tradecore.facade.request.trade.BookRequestDTO;
import com.ly.travel.car.tradecore.facade.response.pay.PayCheckResponseDTO;
import com.ly.travel.car.tradecore.facade.response.trade.BookResponseDTO;
import com.ly.travel.car.tradecore.order.StandardOrderCtx;
import com.ly.travel.car.tradecore.test.ob.ObBookThreadLocalUtils;
import com.ly.travel.car.tradecore.test.ob.ObContext;
import com.ly.travel.car.tradecore.test.testing.Integrationtesting.sfc.SfcTradeBookTest;
import com.ly.travel.car.tradecore.test.utils.TradeUtils;

/**
 * 支付校验测试
 *
 * <AUTHOR>
 * @version Id: SfcPayCheckTest, v 0.1 2024/3/8 09:43 icanci Exp $
 */
@SuppressWarnings("all")
public class SfcPayCheckTest extends SfcTradeBookTest {

    /**
     * paycheck
     *
     * @throws Exception Exception
     */
    @Test
    public void doPayCheck() throws Exception {
        ObContext obContext = ObBookThreadLocalUtils.get();
        obContext.enableOnline();
        BookRequestDTO BookRequestDTO = TradeUtils.genBookRequest();
        BookResponseDTO book = tradeFacade.book(BookRequestDTO);
        System.out.println(FastJsonUtils.toJSONString(book));
        Assert.assertTrue(book.isSuccess() && book.getOrderSerialNo() != null);

        StandardOrderContext context = StandardOrderCtx.of(book.getOrderSerialNo());

        doPayCheck(context);

    }

    private void doPayCheck(StandardOrderContext context) {
        // 支付校验
        PayCheckRequestDTO request = new PayCheckRequestDTO();
        request.setMemberIds(Lists.newArrayList(context.getMemberId()));
        request.setOrderSerialNo(context.getOrderSerialNo());
        request.setBalanceSerialNo(StringUtils.EMPTY);
        request.setAmount(context.getOrder().getAmount().getAmount().toPlainString());
        request.setTraceId(context.getTraceId());

        PayCheckResponseDTO payCheckResponseDTO = payFacade.paycheck(request);
        System.out.println(FastJsonUtils.toJSONString(payCheckResponseDTO));
        Assert.assertTrue(payCheckResponseDTO.isSuccess());
    }
}