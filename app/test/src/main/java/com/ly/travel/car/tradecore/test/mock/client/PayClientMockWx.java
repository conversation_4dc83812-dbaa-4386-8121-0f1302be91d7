package com.ly.travel.car.tradecore.test.mock.client;

import com.ly.travel.car.tradecore.integration.client.pay.PayClient;
import com.ly.travel.car.tradecore.integration.client.pay.WxPaymentPointClient;
import com.ly.travel.car.tradecore.integration.client.pay.model.*;
import com.ly.travel.car.tradecore.integration.client.pay.model.wxpay.*;
import com.ly.travel.car.tradecore.integration.client.pay.model.wxpay.WxPayAuthorizationQueryResponseDTO.AuthorizationInfoDTO;
import com.ly.travel.car.tradecore.integration.error.IntegrationException;
import com.ly.travel.car.tradecore.test.ob.ObBookThreadLocalUtils;
import com.ly.travel.car.tradecore.test.ob.ObContext;

/**
 * <AUTHOR>
 * @version Id: PayClientMock, v 0.1 2024/3/8 15:28 icanci Exp $
 */
public class PayClientMockWx implements PayClient, WxPaymentPointClient {

    /**
     * 支付分校验
     *
     * @param request request
     * @return 支付分校验结果
     */
    @Override
    public WxPayAuthorizationQueryResponseDTO paymentPointValidate(PaySourceQueryRequestDTO request) {
        ObContext obContext = ObBookThreadLocalUtils.get();

        WxPayAuthorizationQueryResponseDTO responseDTO = new WxPayAuthorizationQueryResponseDTO();
        responseDTO.setCode(1);
        responseDTO.setMessage("");

        if (obContext.isPaymentPointValidateSuccess()) {
            AuthorizationInfoDTO authorizationInfoDTO = new AuthorizationInfoDTO();
            authorizationInfoDTO.setApplyPermissionsToken("");
            authorizationInfoDTO.setAppId("");
            authorizationInfoDTO.setOpenId("");
            authorizationInfoDTO.setAuthorizationState(1);
            authorizationInfoDTO.setCancelAuthorizationTime("");
            authorizationInfoDTO.setAuthorizationSuccessTime("");

            responseDTO.setData(authorizationInfoDTO);
        }
        return responseDTO;
    }

    /**
     * 支付分创单
     *
     * @param request request
     * @return 支付分创单结果
     */
    @Override
    public BasePayResponse paymentPointCreateOrder(WxCreateOrderPayRequestDTO request) throws IntegrationException {
        ObContext obContext = ObBookThreadLocalUtils.get();

        BasePayResponse basePayResponse = new BasePayResponse();
        if (obContext.isPaymentPointCreateOrderSuccess()) {
            basePayResponse.setCode(1);
        }
        basePayResponse.setMessage("");
        return basePayResponse;
    }

    /**
     * 支付分扣款
     *
     * @param request request
     * @return 支付分扣款结果
     */
    @Override
    public BasePayResponse paymentPointPaid(WxPaidPayRequestDTO request) throws IntegrationException {
        BasePayResponse response = new BasePayResponse();
        response.setCode(1);
        response.setMessage("");
        return response;
    }

    /**
     * 取消支付分
     * @param request request
     * @return
     * @throws IntegrationException
     */
    @Override
    public BasePayResponse cancelPaymentPointPaid(WxCancelScorePayRequestDTO request) throws IntegrationException {
        BasePayResponse response = new BasePayResponse();
        response.setCode(1);
        response.setMessage("");
        return response;
    }

    @Override
    public BasePayResponse paymentPayOff(PaymentPayOffRequestDTO request) throws IntegrationException {
        BasePayResponse response = new BasePayResponse();
        response.setCode(1);
        response.setMessage("");
        return response;
    }

    @Override
    public DeductionVoucherResponse createDeductionVoucher(CreateDeductionRequest request) throws IntegrationException {
        return null;
    }

    @Override public BasePayResponse cancelDeductionVoucher(CancelDeductionRequest request)
        throws IntegrationException {
        return null;
    }

    @Override
    public WxPayAuthorizationQueryResponseDTO paymentPointMidValidate(PaySourceQueryRequestDTO request) throws IntegrationException {
        return null;
    }

    @Override
    public WxPayAuthorizationQueryResponseDTO queryPaymentPointAuthorization(WxAuthorizationPayRequestDTO request) throws IntegrationException {
        return null;
    }
}