package com.ly.travel.car.tradecore.test.testing.integration.sfc.pay.paycheck;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import com.alibaba.citrus.util.Assert;
import com.google.common.collect.Lists;
import com.ly.flight.intl.sof.annotation.config.ServiceTestConfig;
import com.ly.flight.intl.sof.annotation.utils.parameter.util.JsonUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.tradecore.business.ctx.StandardOrderContext;
import com.ly.travel.car.tradecore.facade.PayFacade;
import com.ly.travel.car.tradecore.facade.TradeFacade;
import com.ly.travel.car.tradecore.facade.request.pay.PayCheckRequestDTO;
import com.ly.travel.car.tradecore.facade.request.trade.BookRequestDTO;
import com.ly.travel.car.tradecore.facade.response.pay.PayCheckResponseDTO;
import com.ly.travel.car.tradecore.facade.response.trade.BookResponseDTO;
import com.ly.travel.car.tradecore.order.StandardOrderCtx;
import com.ly.travel.car.tradecore.test.base.BaseTradeServiceTest;
import com.ly.travel.car.tradecore.test.context.DefaultMockContext;
import com.ly.travel.car.tradecore.test.context.MockContextUtils;

import com.ly.travel.car.tradecore.test.mock.client.SupplierClientMock;
import com.ly.travel.car.tradecore.test.mock.validate.CarPriceValidatorMock;

/**
 * 支付校验测试
 *
 * <AUTHOR>
 * @version Id: SfcPayCheckTest, v 0.1 2024/3/8 09:43 icanci Exp $
 */
public class SfcPayCheckTest extends BaseTradeServiceTest {
    @Resource
    private TradeFacade tradeFacade;
    @Resource
    private PayFacade   payFacade;

    /**
     * 此类的Mock，只会走到Client方法中，不会走到Facade方法
     *
     * @param config  config
     */
    @Override
    protected void initConfig(ServiceTestConfig config) {

        // mock client
        config.registerMockType("supplierClient", SupplierClientMock.class);
        // Validate
        config.registerMockType("carPriceValidator", CarPriceValidatorMock.class);

        MockContextUtils.setMockContext(new DefaultMockContext());
        super.initConfig(config);
    }

    /**
     * paycheck
     *
     * @throws Exception Exception
     */
    @Test
    public void doPayCheck() throws Exception {
        String jsonPath = "/json/facade/trade/book/SfcBookRequest.json";
        BookRequestDTO BookRequestDTO = JsonUtils.fromJSONPath(jsonPath, BookRequestDTO.class);
        String str = String.valueOf(System.currentTimeMillis());
        String memberId = str.substring(str.length() - 10);
        BookRequestDTO.getBookInfo().getMember().setId(memberId);
        BookResponseDTO book = tradeFacade.book(BookRequestDTO);
        System.out.println(FastJsonUtils.toJSONString(book));
        Assert.assertTrue(book.isSuccess() && StringUtils.isNotBlank(book.getOrderSerialNo()));
        StandardOrderContext context = StandardOrderCtx.of(book.getOrderSerialNo());

        doPayCheck(context);

    }

    private void doPayCheck(StandardOrderContext context) {
        // 支付校验
        PayCheckRequestDTO request = new PayCheckRequestDTO();
        request.setMemberIds(Lists.newArrayList(context.getMemberId()));
        request.setOrderSerialNo(context.getOrderSerialNo());
        request.setBalanceSerialNo(StringUtils.EMPTY);
        request.setAmount(context.getOrder().getAmount().getAmount().toPlainString());
        request.setTraceId(context.getTraceId());

        PayCheckResponseDTO payCheckResponseDTO = payFacade.paycheck(request);
        System.out.println(FastJsonUtils.toJSONString(payCheckResponseDTO));
        Assert.assertTrue(payCheckResponseDTO.isSuccess());
    }

    @Test
    public void doPayCheck2() throws Exception {
        StandardOrderContext context = StandardOrderCtx.of("YCS20240309144557MMAJ7336");
        // 支付校验
        PayCheckRequestDTO request = new PayCheckRequestDTO();
        request.setMemberIds(Lists.newArrayList(context.getMemberId()));
        request.setOrderSerialNo(context.getOrderSerialNo());
        request.setBalanceSerialNo(StringUtils.EMPTY);
        request.setAmount(context.getOrder().getAmount().getAmount().toPlainString());
        request.setTraceId(context.getTraceId());

        PayCheckResponseDTO payCheckResponseDTO = payFacade.paycheck(request);
        System.out.println(FastJsonUtils.toJSONString(payCheckResponseDTO));
        Assert.assertTrue(payCheckResponseDTO.isSuccess());
    }
}