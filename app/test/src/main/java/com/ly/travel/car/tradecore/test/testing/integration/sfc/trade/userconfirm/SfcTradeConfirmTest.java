package com.ly.travel.car.tradecore.test.testing.integration.sfc.trade.userconfirm;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import com.alibaba.citrus.util.Assert;
import com.google.common.collect.Lists;
import com.ly.flight.intl.sof.annotation.config.ServiceTestConfig;
import com.ly.flight.intl.sof.annotation.utils.parameter.util.JsonUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.tradecore.business.ctx.StandardOrderContext;
import com.ly.travel.car.tradecore.facade.TradeFacade;
import com.ly.travel.car.tradecore.facade.request.trade.BookRequestDTO;
import com.ly.travel.car.tradecore.facade.request.trade.UserConfirmGetOffRequestDTO;
import com.ly.travel.car.tradecore.facade.request.trade.UserConfirmGetOnRequestDTO;
import com.ly.travel.car.tradecore.facade.response.trade.BookResponseDTO;
import com.ly.travel.car.tradecore.facade.response.trade.UserConfirmGetOffResponseDTO;
import com.ly.travel.car.tradecore.facade.response.trade.UserConfirmGetOnResponseDTO;
import com.ly.travel.car.tradecore.order.StandardOrderCtx;
import com.ly.travel.car.tradecore.test.base.BaseTradeServiceTest;
import com.ly.travel.car.tradecore.test.context.DefaultMockContext;
import com.ly.travel.car.tradecore.test.context.MockContextUtils;

import com.ly.travel.car.tradecore.test.mock.client.SupplierClientMock;
import com.ly.travel.car.tradecore.test.mock.validate.CarPriceValidatorMock;

/**
 * 用户确认修改测试
 *
 * <AUTHOR>
 * @version Id: CarTradeConfirmTest, v 0.1 2024/3/7 16:49 icanci Exp $
 */

public class SfcTradeConfirmTest extends BaseTradeServiceTest {
    @Resource
    private TradeFacade tradeFacade;

    /**
     * 此类的Mock，只会走到Client方法中，不会走到Facade方法
     *
     * @param config  config
     */
    @Override
    protected void initConfig(ServiceTestConfig config) {

        // mock client
        config.registerMockType("supplierClient", SupplierClientMock.class);
        // Validate
        config.registerMockType("carPriceValidator", CarPriceValidatorMock.class);

        MockContextUtils.setMockContext(new DefaultMockContext());
        super.initConfig(config);
    }

    // ========================================== 无商品下单 ==========================================
    /**
     * 用户确认上车
     *
     * @throws Exception Exception
     */
    @Test
    public void doConfirmGetOn() throws Exception {
        String jsonPath = "/json/facade/trade/book/SfcBookRequest.json";
        BookRequestDTO BookRequestDTO = JsonUtils.fromJSONPath(jsonPath, BookRequestDTO.class);
        String str = String.valueOf(System.currentTimeMillis());
        String memberId = str.substring(str.length() - 10);
        BookRequestDTO.getBookInfo().getMember().setId(memberId);
        BookResponseDTO book = tradeFacade.book(BookRequestDTO);
        System.out.println(FastJsonUtils.toJSONString(book));
        Assert.assertTrue(book.isSuccess() && StringUtils.isNotBlank(book.getOrderSerialNo()));
        StandardOrderContext context = StandardOrderCtx.of(book.getOrderSerialNo());
        System.out.println(context);

        // 修改订单备注
        doConfirmGetOn(context);

    }

    /**
     * 用户确认下车
     *
     * @throws Exception Exception
     */
    @Test
    public void doConfirmGetOff() throws Exception {
        String jsonPath = "/json/facade/trade/book/SfcBookRequest.json";
        BookRequestDTO BookRequestDTO = JsonUtils.fromJSONPath(jsonPath, BookRequestDTO.class);
        String str = String.valueOf(System.currentTimeMillis());
        String memberId = str.substring(str.length() - 10);
        BookRequestDTO.getBookInfo().getMember().setId(memberId);
        BookResponseDTO book = tradeFacade.book(BookRequestDTO);
        System.out.println(FastJsonUtils.toJSONString(book));
        Assert.assertTrue(book.isSuccess() && StringUtils.isNotBlank(book.getOrderSerialNo()));
        StandardOrderContext context = StandardOrderCtx.of(book.getOrderSerialNo());
        System.out.println(context);

        // 修改订单备注
        doConfirmGetOff(context);

    }

    /**
     * 用户确认上车
     *
     * @param context context
     */
    private void doConfirmGetOn(StandardOrderContext context) {
        UserConfirmGetOnRequestDTO request = new UserConfirmGetOnRequestDTO();
        request.setMemberIds(Lists.newArrayList(context.getMemberId()));
        request.setOrderSerialNo(context.getOrderSerialNo());
        request.setTraceId(context.getTraceId());
        request.setLongitude("116.404");
        request.setLatitude("39.915");

        UserConfirmGetOnResponseDTO response = tradeFacade.confirmGetOn(request);
        System.out.println(FastJsonUtils.toJSONString(response));
        Assert.assertTrue(response.isSuccess());

    }

    /**
     * 用户确认下车
     *
     * @param context context
     */
    private void doConfirmGetOff(StandardOrderContext context) {
        UserConfirmGetOffRequestDTO request = new UserConfirmGetOffRequestDTO();
        request.setLongitude("116.404");
        request.setLatitude("39.915");
        request.setMemberIds(Lists.newArrayList(context.getMemberId()));
        request.setOrderSerialNo(context.getOrderSerialNo());
        request.setTraceId(context.getTraceId());

        UserConfirmGetOffResponseDTO response = tradeFacade.confirmGetOff(request);
        System.out.println(FastJsonUtils.toJSONString(response));
        Assert.assertTrue(response.isSuccess());
    }
}