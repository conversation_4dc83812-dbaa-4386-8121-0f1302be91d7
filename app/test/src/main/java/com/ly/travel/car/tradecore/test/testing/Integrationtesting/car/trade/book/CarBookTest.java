package com.ly.travel.car.tradecore.test.testing.Integrationtesting.car.trade.book;

import org.junit.Assert;
import org.junit.Test;

import com.ly.travel.car.tradecore.base.ctx.Context;
import com.ly.travel.car.tradecore.model.OrderVO;
import com.ly.travel.car.tradecore.model.enums.OrderType;
import com.ly.travel.car.tradecore.model.enums.PayCategory;
import com.ly.travel.car.tradecore.test.testing.Integrationtesting.car.BaseCarTradeForMockTest;

/**
 * CarBookTest
 *
 * <AUTHOR>
 * @version Id: CarBookTest.java, v 0.1 2024-03-14 22:39 ryan Exp $$
 */
public class CarBookTest extends BaseCarTradeForMockTest {

    /**
     * 积分支付下单
     */
    @Test
    public void payment_point_book() throws Exception {
        // 下单
        Context context = doBook(PayCategory.PAYMENT_POINT, OrderType.CAR_TIMING);

        OrderVO order = context.getOrder();
        Assert.assertTrue("payment point order book failure", order.isPaymentPoint());
        Assert.assertTrue("payment point order state is not illegal", order.isPending());
    }

    /**
     * 在线支付下单
     */
    @Test
    public void online_book() throws Exception {
        // 下单
        Context context = doBook(PayCategory.ONLINE, OrderType.CAR_TIMING);

        OrderVO order = context.getOrder();
        Assert.assertTrue("payment point order book failure", order.isOnlinePay());
        Assert.assertTrue("payment point order state is not illegal", order.isPending());
    }
}
