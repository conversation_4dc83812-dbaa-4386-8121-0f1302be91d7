sof.version=1.0.0
app.name=cartradecore
app.version=1.0-SNAPSHOT
app.type=web
dubbo.application.name=cartradecore
dubbo.registry.address=tcdsf://testdsf.tcent.cn
#dubbo.registry.address=zookeeper://localhost:2181
#dubbo.monitor.protocol=registry
dubbo.container=spring,log4j
# database
uniform.env=qa
uniform.skyCode=bus.shared.mobility.trade.core
sof-env=test
# dubbo
dubbo.service.gsname=dsf.car.trade.core
dubbo.service.port=11011
dubbo.service.registry.address=qa.dsf2.17usoft.com
dubbo.service.deploy.container=tomcat
dubbo.service.version=*******

mq.nameSrvAddress=**************\:9876;*************\:9876
# 配置中心mq
travelsystem.java.config.changed.group=travelsystem_trade_group_config_repo
# BROADCASTING/CLUSTERING
travelsystem.java.config.changed.model=CLUSTERING
# 监听方式 MQ/ETCD
travelsystem.java.config.changed.listentype=ETCD

################################## REDIS ################################
# redis
car.trade.core.redis.group=car.trade.core.redis.group
redis.groupNames=bus.shared.mobility.trade.core.redis.group
redis.groupLockNames=bus.shared.mobility.trade.core.redis.group.lock

################################## HTTP URL ################################
# urls
url.short.link=http://sapi.17usoft.com/tcsa-api/services/wsc/create
url.car.order.detail.link.wechat=https://wx.qa.17u.cn/carfe/wechat/orderDetail?orderSerialNo=%s
url.sfc.order.detail.link.wechat=https://wx.qa.17u.cn/carfe/wechat/orderDetailSFC?orderSerialNo=%s
url.car.order.detail.link.app=https://wx.qa.17u.cn/carfe/app/orderDetail?orderSerialNo=%s
url.sfc.order.detail.link.app=https://wx.qa.17u.cn/carfe/app/orderDetailSFC?orderSerialNo=%s
url.wx.pay.point.validate=http://pay.qa.fly.17usoft.com/paymentquery/api/payscore/query
url.wx.pay.point.createOrder=http://car.travel.qa.17usoft.com/payment/api/payscore/createOrder
url.wx.pay.point.paid=http://car.travel.qa.17usoft.com/payment/api/payscore/complete
url.car.mada.order.detail.link=https://wx.qa.17u.cn/carfe/mdapp/orderDetail?orderSerialNo=%s
url.mada.short.link=https://bcxzp2.jgshare.cn/AaRY?orderSerialNo=%s&refId=
url.inter.car.order.detail.link.app=https://wx.qa.17u.cn/carfe/app/intlDetail?orderSerialNo=%s
url.inter.car.order.detail.link.wechat=https://wx.qa.17u.cn/carfe/wechat/intlDetail?orderSerialNo=%s
url.wx.pay.point.cancel=http://car.travel.qa.17usoft.com/payment/api/payscore/cancel

#搜索查询接口
url.search.price.query=http://search.qa.17usoft.com/search/api/v1/search
url.search.price.query.timeout=10000

# 查询风控等级地址
url.risk.level.query=http://tcwireless.qa.17usoft.com/car_risk_process/riskCheck/queryRiskLevel
# 查询预付押金倍率地址
url.risk.prepaid.deposit.rate=http://tcwireless.qa.17usoft.com/car_risk_process/riskCheck/queryCashRate
# 下单风控校验地址
url.risk.validate=http://tcwireless.qa.17usoft.com/car_risk_process/riskCheck/unifyCheck
# 风控：取消判责
url.risk.cancel.judgment.responsibility=http://tcwireless.qa.17usoft.com/car_risk_process/riskCheck/assignDuty

################################## DSF ################################
#搜索查询接口
dsf.car.search.gsName=dsf.shared.mobility.serach.core
dsf.car.search.version=1.1.0.6
dsf.car.search.timeout=3000
# 推送服务
dsf.carpush.gsName=dsf.travelsystem.sms.platform.core
dsf.carpush.version=0.0.0.6
# 订单日志服务
dsf.carorderservice.gsName=dsf.car.order.service
dsf.carorderservice.version=*******
# 供应商服务
dsf.carsupplier.gsName=dsf.car.shared.mobility.supply.trade.core
dsf.carsupplier.version=3.0.0.9
#dispatch_pool
dsf.carmesospheredispatch.gsName=dsf.shared.mobility.mesosphere.dispatch
dsf.carmesospheredispatch.version=0.0.0.3
#travel_x
dsf.travelx.trade.core.gsName=dsf.travelx.trade.core
dsf.travelx.trade.core.version=0.0.3.0
#interl_flight_travel
dsf.intl.flight.trade.core.gsName=dsf.iflight.tradecore
dsf.intl.flight.trade.core.version=3.13.0.0
#interl_flight_travel_reverse
dsf.intl.flight.reverse.core.gsName=dsf.flight.int.reverse.core
dsf.intl.flight.reverse.core.version=3.14.0.1
#supplier.order.core
dsf.carsupplier.order.gsName=dsf.car.shared.mobility.supply.order.core
dsf.carsupplier.order.version=1.0.0.3
# 辅营
dsf.carancillary.binding.gsName=dsf.flight.travel.ancillary.trade
dsf.carancillary.binding.version=3.54.0.25
dsf.carancillary.rights.gsName=dsf.flight.ancillary.rights
dsf.carancillary.rights.version=1.1.0.6
# 里程
dsf.carmileage.gsName=dsf.flight.marketingmileage
dsf.carmileage.version=1.0.0.10
#营销
dsf.flight.marketingitemcore.gsName=dsf.flight.marketingitemcore
dsf.flight.marketingitemcore.version=1.0.0.49
# 活动
dsf.marketingcore.gsName=dsf.flight.marketingcore
dsf.marketingcore.version=0.2.4.4
################################## MQ ################################

# 订单状态机
mq.order.state.topic=car_trade_topic_order_state_change
# 二清冻结
mq.pay.freeze.topic=travelsystem_fund_regulator_callback_topic_car_qa
# 二清解冻
mq.pay.unfreeze.topic=travelsystem_fund_regulator_topic_car_qa
# 里程取消
mq.mileage.cancel.topic=flight_marketing_order_failed_topic_mileage_test
# 里程扣减
mq.mileage.deduction.topic=flight_marketing_order_success_topic_mileage_test
# 辅营改派
mq.ancillary.reassign.topic=car_trade_topic_ancillary_reassign_notify
# 辅营商品换购回写
mq.ancillary.endorse.callback.group=car_ancillary_endorse_callback_group
mq.ancillary.endorse.callback.topic=flight_topic_binding_endorse_order_callback_topic_test
# 辅营取消
mq.ancillary.cancel.topic=flight_binding_order_cancel_topic_test
# 辅营临时单
mq.ancillary.temporary.order.topic=flight_binding_order_v3_topic_ancillary_trade_test
# 辅营预定
mq.ancillary.booked.order.topic=flight_binding_formal_order_topic_ancillary_trade_test
# 辅营预定回调
mq.ancillary.book.callback.topic=car_ancillary_book_callback_topic
mq.ancillary.book.callback.group=car_ancillary_book_callback_group
# 辅营退订
mq.ancillary.refund.topic=flight_binding_refund_order_topic_ancillary_trade_test
# 辅营退订回写
mq.ancillary.refund.callback.topic=car_ancillary_refund_callback_topic_test
mq.ancillary.refund.callback.group=car_ancillary_refund_callback_group
# 辅营取消无忧抵扣完成
mq.ancillary.wy.cancel.deduct.topic=flight_tgwy_notify
# 微信二清冻结回写
mq.pay.wechat.freeze.callback.topic=travelsystem_fund_regulator_topic_car_qa
mq.pay.wechat.freeze.callback.group=car_pay_regulator_callback_group
# 微信二清解冻回写
mq.pay.wechat.unfreeze.callback.topic=travelsystem_fund_regulator_callback_topic_car_qa
mq.pay.wechat.unfreeze.callback.group=car_pay_fund_regulator_callback_group
# 交易监控MQ
mq.car.order.mq.monitor.topic=car_order_mq_monitor_topic
mq.car.order.mq.monitor.group=car_order_mq_monitor_group
# 里程后返
mq.mileage.received.topic=jpyyxtb_jpyxpt_topic_backactivity_qa
# 支付清款相关topic
mq.pay.clear.topic=travel_use_car_finance_topic_clear_qa
# 支付单取消
mq.pay.cancel.topic=travelsystem_car_topic_cancel_payment
# 取消回写
mq.order.pay.timeout.topic=car_order_pay_timeout_topic
mq.order.pay.timeout.group=car_order_pay_timeout_group
# 顺风车触发行程开始
mq.job.trip.start.group=car_trip_start_group
mq.job.trip.start.topic=car_trip_start_topic
# 链路追踪
mq.tracer.topic=mq_shared_mobility_tracer_topic
#支付状态机
mq.order.pay.state.topic=car_trade_topic_order_pay_state_change

mq.order.mada.notify.topic=mdta_orderstate_changed_topic_qa
mq.guarantee.sfc.notice.topic=yc_guarantee_sfc_notice_topic

#财务结算
mq.finance.settle.topic=travel_use_car_finance_topic_clear_qa
mq.finance.settle.for.cancel.topic=travel_use_car_finance_topic_clear_qa
################################## OTHER ################################
# 推送域
push.domain=USECAR_TRADE
# codes
token.activity.code=23K9JDER3S
# 里程
marketing.mileage.received.activityCode=cdbdf6f1076b423fa46b355c1d9bc8be
#芝麻信用分url地址
url.ali.pay.point.createOrder=http://car.travel.qa.17usoft.com/payment/api/alizhima/createOrder
url.ali.pay.point.cancel=http://car.travel.qa.17usoft.com/payment/api/alizhima/cancel
url.ali.pay.point.paid=http://car.travel.qa.17usoft.com/payment/api/alizhima/complete
url.ali.pay.point.query.authorization=http://pay.qa.fly.17usoft.com/paymentquery/api/alizhima/queryandpermissions
url.ali.pay.point.validate=http://pay.qa.fly.17usoft.com/paymentquery/api/alizhima/query
