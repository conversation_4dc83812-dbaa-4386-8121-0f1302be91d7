package com.ly.travel.car.tradecore.integration.client.activity;

import com.ly.flight.marketingcore.facade.dto.ParticipateRequestDTO;
import com.ly.flight.marketingcore.facade.dto.ParticipateResponseDTO;
import com.ly.flight.marketingcore.facade.dto.ValidateRequestDTO;
import com.ly.flight.marketingcore.facade.dto.ValidateResponseDTO;
import com.ly.flight.marketingcore.facade.dto.cancel.ParticipateCancelRequestDTO;
import com.ly.flight.marketingcore.facade.dto.cancel.ParticipateCancelResponseDTO;
import com.ly.travel.car.tradecore.integration.error.IntegrationException;

/**
 * ActivityClient
 *
 * <AUTHOR>
 * @version Id: ActivityClient.java, v 0.1 2024-02-29 17:36 ryan Exp $$
 */
public interface ActivityClient {

    /**
     * 活动参与
     *
     * @param request the request
     * @return Serializable serializable
     */
    ValidateResponseDTO validate(ValidateRequestDTO request) throws IntegrationException;

    /**
     * 活动参与
     *
     * @param request the request
     * @return Serializable serializable
     */
    ParticipateResponseDTO participate(ParticipateRequestDTO request) throws IntegrationException;

    /**
     * 活动取消
     *
     * @param request the request
     * @return Serializable serializable
     */
    ParticipateCancelResponseDTO cancel(ParticipateCancelRequestDTO request) throws IntegrationException;
}
