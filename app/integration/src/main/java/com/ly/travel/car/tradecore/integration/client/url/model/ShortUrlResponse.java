package com.ly.travel.car.tradecore.integration.client.url.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * ShortUrlResponse
 * 
 * <AUTHOR>
 * @version Id: ShortUrlResponse, v 0.1 2024/1/22 18:59 icanci Exp $
 */
@Data
public class ShortUrlResponse implements Serializable {
    /** 序列化id  */
    private static final long  serialVersionUID = 4948888051798287681L;
    /**
     * 地址URL.
     */
    private List<ShortUrlData> datas;
    /**
     * 返回状态.
     */
    private String             status;

}
