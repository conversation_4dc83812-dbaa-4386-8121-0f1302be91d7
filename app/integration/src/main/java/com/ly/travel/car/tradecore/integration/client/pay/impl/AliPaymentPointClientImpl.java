package com.ly.travel.car.tradecore.integration.client.pay.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.ly.mom.netty.util.internal.ThrowableUtil;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.tradecore.integration.client.pay.AliPaymentPointClient;
import com.ly.travel.car.tradecore.integration.client.pay.model.BasePayResponse;
import com.ly.travel.car.tradecore.integration.client.pay.model.alipay.*;
import com.ly.travel.car.tradecore.integration.common.BaseHttpClient;
import com.ly.travel.car.tradecore.integration.error.IntegrationException;

import lombok.extern.slf4j.Slf4j;

/**
 * LY.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 * @since 2024/11/20
 */
@Service("aliPaymentPointClient")
@Slf4j
public class AliPaymentPointClientImpl extends BaseHttpClient implements AliPaymentPointClient {
    /** 支付分校验地址  */
    @Value(value = "${url.ali.pay.point.validate}")
    private String payPointValidateUrl;
    /** 支付分创单地址  */
    @Value(value = "${url.ali.pay.point.createOrder}")
    private String payPointCreateOrderUrl;
    /** 支付分扣款地址  */
    @Value(value = "${url.ali.pay.point.paid}")
    private String payPointPaidUrl;
    /**
     * 取消支付分订单地址
     */
    @Value("${url.ali.pay.point.cancel}")
    private String cancelPayScoreUrl;
    /** 
     * 支付分openId查询授权地址 
     * */
    @Value(value = "${url.ali.pay.point.query.authorization}")
    private String payPointQueryAuthorizationUrl;

    @Override
    public AliPayAuthorizationQueryResponseDTO paymentPointValidate(AliPayAuthorizationRequestDTO request) throws IntegrationException {
        try {
            LoggerUtils.info(log, "[AliPaymentPointClient][paymentPointValidate] 开始阿里芝麻信用分校验 :{}", JSON.toJSONString(request));
            AliPayAuthorizationQueryResponseDTO responseDTO = post(payPointValidateUrl, request, AliPayAuthorizationQueryResponseDTO.class, 5000);
            if (responseDTO == null) {
                throw new IntegrationException(ERROR.responseIsNull("AliPaymentPointClient#paymentPointValidate response is null"));
            }
            return responseDTO;
        } catch (Exception e) {
            LoggerUtils.error(log, "[AliPaymentPointClient][paymentPointValidate] request:{},error msg:{}", FastJsonUtils.toJSONString(request),
                ThrowableUtil.stackTraceToString(e));
            throw new IntegrationException(e);
        }
    }

    @Override
    public BasePayResponse paymentPointCreateOrder(AliPayCreateOrderRequestDTO request) throws IntegrationException {
        try {
            LoggerUtils.info(log, "[AliPaymentPointClient][paymentPointCreateOrder] 开始阿里芝麻信用分创单 :{}", JSON.toJSONString(request));
            return post(payPointCreateOrderUrl, request, BasePayResponse.class, 5000);
        } catch (Exception e) {
            LoggerUtils.error(log, "[AliPaymentPointClient][paymentPointCreateOrder] request:{},error msg:{}", FastJsonUtils.toJSONString(request),
                ThrowableUtil.stackTraceToString(e));
            throw new IntegrationException(e);
        }
    }

    @Override
    public BasePayResponse paymentPointPaid(AliPayPaidRequestDTO request) throws IntegrationException {
        try {
            LoggerUtils.info(log, "[AliPaymentPointClient][paymentPointPaid] 开始阿里芝麻信用分扣款 :{}", JSON.toJSONString(request));
            return post(payPointPaidUrl, request, BasePayResponse.class, 5000);
        } catch (Exception e) {
            LoggerUtils.error(log, "[AliPaymentPointClient][paymentPointPaid] request:{},error msg:{}", FastJsonUtils.toJSONString(request), ThrowableUtil.stackTraceToString(e));
            throw new IntegrationException(e);
        }
    }

    @Override
    public BasePayResponse cancelPaymentPointPaid(AliPayCancelScoreRequestDTO request) throws IntegrationException {
        try {
            LoggerUtils.info(log, "[AliPaymentPointClient][cancelPaymentPointPaid] 开始阿里芝麻信用分取消订单 :{}", JSON.toJSONString(request));
            return post(cancelPayScoreUrl, request, BasePayResponse.class, 5000);
        } catch (Exception e) {
            LoggerUtils.error(log, "[AliPaymentPointClient][paymentPointPaid] 取消支付分订单request:{},error msg:{}", FastJsonUtils.toJSONString(request),
                ThrowableUtil.stackTraceToString(e));
            throw new IntegrationException(e);
        }
    }

    @Override
    public AliPayAuthorizationQueryResponseDTO queryPaymentPointAuthorization(AliPayAuthorizationRequestDTO request) throws IntegrationException {
        try {
            LoggerUtils.info(log, "[AliPaymentPointClient][queryPaymentPointAuthorization] 开始查询芝麻信用分授权状态并授权 :{}", JSON.toJSONString(request));
            AliPayAuthorizationQueryResponseDTO responseDTO = post(payPointQueryAuthorizationUrl, request, AliPayAuthorizationQueryResponseDTO.class, 5000);
            if (responseDTO == null) {
                throw new IntegrationException(ERROR.responseIsNull("AliPaymentPointClient#queryPaymentPointAuthorization response is null"));
            }
            return responseDTO;
        } catch (Exception e) {
            LoggerUtils.error(log, "[AliPaymentPointClient][queryPaymentPointAuthorization] 异常信息{}", e.getMessage(), e);
            throw new IntegrationException(e);
        }
    }
    
}
