package com.ly.travel.car.tradecore.integration.client.hj.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 黑鲸权益领取
 */
@Getter
@Setter
public class HJReceiveWelfareResponseDTO {

    private ExternalWelfareDataVo data;

    @Getter
    @Setter
    public static class ExternalWelfareDataVo {
        private boolean                  isSucceed; // 接口响应状态
        private Integer                  retCode;   // 接口响应代码
        private String                   retMsg;    // 接口响应信息
        private String                   requestId; // 请求requestId
        private ExternalWelfareReceiveVO content;   // 响应内容
    }

    @Getter
    @Setter
    public static class ExternalWelfareReceiveVO {
        private boolean                          callSuccess; // 接口是否请求成功
        private List<MultiTimesReceiveWelfareVO> results;     // 领取权益明细

        // Getters and Setters
        // ... (省略其他getter和setter方法)
    }

    @Getter
    @Setter
    public static class MultiTimesReceiveWelfareVO {
        private boolean callSuccess; // 权益领取是否成功
        private String  message;     // 权益领取信息
        private int     code;        // 权益领取状态码
        private String  recordId;    // 权益领取成功的记录ID
    }
}
