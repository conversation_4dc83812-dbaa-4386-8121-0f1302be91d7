package com.ly.travel.car.tradecore.integration.client.ancillary.model;

import java.io.Serializable;

import lombok.Data;

/**
 * 辅营取消Payload
 *
 * <AUTHOR>
 * @version Id: AncillaryCancelPayload, v 0.1 2024/03/04 17:13 ryan Exp $
 */
@Data
public class AncillaryCancelPayload implements Serializable {
    private static final long serialVersionUID = 1367359145262033160L;

    /** 订单号 */
    private String            serialNo;
    /** 跟踪ID */
    private String            traceId;
    /** 取消时间 yyyy-MM-dd hh:mm:ss */
    private String            operateTime;
    /** 汽车票：QCP 巴士快线：BSP 国内机票：GNJP 国际机票：GJJP */
    private String            trafficSource;
}