package com.ly.travel.car.tradecore.integration.client.time.impl;

import com.ly.sof.utils.common.DateUtil;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.common.utils.DateUtils;
import com.ly.travel.car.tradecore.integration.client.id.UUIDGenerator;
import com.ly.travel.car.tradecore.integration.client.time.model.BaseTimeQueryResponse;
import com.ly.travel.car.tradecore.integration.client.time.model.SupplyInternationaCityRequest;
import com.ly.travel.car.tradecore.integration.client.time.model.SupplyInternationaCityResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.ly.travel.car.tradecore.integration.client.time.TimeClient;
import com.ly.travel.car.tradecore.integration.common.BaseHttpClient;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

@Slf4j
@Service("timeClientImpl")
public class TimeClientImpl extends BaseHttpClient implements TimeClient {

    /** 查询时间差地址  */
    @Value(value = "${url.remote.time.query}")
    private String remoteTimeQueryUrl;

    @Value(value = "${url.supply.internationa.city.query}")
    private String supplyInternationaCityQueryUrl;

    /**
     * 获取北京时间
     *
     * @param cityCode
     * @param remoteTime
     */
    @Override
    public Date queryBeiJingTime(Integer cityCode, String defaultCountryCode, Date remoteTime) {
        try {
            String countryCode = StringUtils.EMPTY;
            SupplyInternationaCityRequest supplyInternationaCityRequest = new SupplyInternationaCityRequest();
            supplyInternationaCityRequest.setTraceId(UUIDGenerator.generateRandomUUID());
            supplyInternationaCityRequest.setCarCityId(cityCode);
            try {
                SupplyInternationaCityResponse supplyInternationaCityResponse = get(supplyInternationaCityQueryUrl,
                    FastJsonUtils.fromJSONString(FastJsonUtils.toJSONString(supplyInternationaCityRequest), HashMap.class), SupplyInternationaCityResponse.class, 1000);
                if (supplyInternationaCityResponse.isSuccess() && CollectionUtils.isNotEmpty(supplyInternationaCityResponse.getData())) {
                    SupplyInternationaCityResponse.SupplyInternationaCityDetail detail = supplyInternationaCityResponse.getData().stream()
                        .filter(info -> info.getCarCityId().intValue() == cityCode.intValue()).findFirst().orElse(null);
                    if (Objects.nonNull(detail)) {
                        countryCode = detail.getCode();
                    }
                }
            } catch (Exception e) {
                LoggerUtils.error(log, "查询用车国家二字码失败", e);
            }
            //兜底
            if (StringUtils.isEmpty(countryCode)) {
                countryCode = defaultCountryCode;
            }
            if (StringUtils.isNotEmpty(countryCode)) {
                BaseTimeQueryResponse baseTimeQueryResponse = get(remoteTimeQueryUrl + countryCode, null, BaseTimeQueryResponse.class, 1000);
                Date beiJingTime = DateUtils.parseY4M2d2H2m2s2WebString(baseTimeQueryResponse.getValue().getBaseTime());
                Date remoteCityTime = DateUtils.parseY4M2d2H2m2s2WebString(baseTimeQueryResponse.getValue().getRemoteTime());
                return DateUtil.addSeconds(remoteTime, (beiJingTime.getTime() - remoteCityTime.getTime()) / 1000);
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "查询用车时间对应的北京时间Error", e);
        }
        return null;
    }
}
