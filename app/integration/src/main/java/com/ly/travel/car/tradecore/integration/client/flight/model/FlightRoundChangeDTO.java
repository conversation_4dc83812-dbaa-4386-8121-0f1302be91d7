package com.ly.travel.car.tradecore.integration.client.flight.model;

import com.ly.sof.utils.common.DateUtil;
import lombok.Data;
import java.io.Serializable;

@Data
public class FlightRoundChangeDTO implements Comparable<FlightRoundChangeDTO>, Serializable {
    /**
     * 订单号
     */
    private String OrderSerialid;
    /**
     * 航段id
     */
    private String SegmentId;
    /**
     *
     */
    private String Sequence;
    /**
     * 航变类型 0：未知航变 1：航班延迟 2：航班提前 3：航班取消 4：航班恢复 5：航班号变动 6：机场变动
     */
    private int    ChangeType;
    /**
     * 原起飞机场三字码
     */
    private String DepAirPortCode;
    /**
     * 原抵达机场三字码
     */
    private String ArrAirPortCode;
    /**
     * 原起飞时间
     */
    private String TakeOffTime;
    /**
     * 原抵达时间
     */
    private String ArrivalTime;
    /**
     * 原航班号
     */
    private String FlightNo;

    /**
     * 原舱位代码
     */
    private String CabinCode;

    /**
     * 新起飞机场三字码
     */
    private String NewDepAirPortCode;

    /**
     * 新抵达机场三字码
     */
    private String NewArrAirPortCode;

    /**
     * 新起飞时间
     */
    private String NewTakeOffTime;

    /**
     * 新抵达时间
     */
    private String NewArrivalTime;

    /**
     * 新航班号
     */
    private String NewFlightNo;

    /**
     * 新舱位代码
     */
    private String NewCabinCode;
    /**
     * 创建时间
     */
    private String CreateDate;
    /**
     * 状态
     */
    private String Status;
    /**
     * 新舱位代码
     */
    private String PostStatus;

    /**
     * 航变原因
     */
    private String ReasonCode;

    @Override
    public int compareTo(FlightRoundChangeDTO o) {
        return DateUtil.parseDateNewFormat(o.getCreateDate()).compareTo(DateUtil.parseDateNewFormat(this.getCreateDate()));
    }
}
