package com.ly.travel.car.tradecore.integration.client.push;

import com.ly.travel.car.tradecore.integration.error.IntegrationException;
import com.ly.travel.pushcore.facade.request.PushRequest;
import com.ly.travel.pushcore.facade.response.PushResponse;

/**
 * 推送客户端
 * 
 * <AUTHOR>
 * @version Id: PushClient, v 0.1 2023/12/12 15:56 icanci Exp $
 */
public interface PushClient {
    /**
     * 推送订单消息
     * 
     * @param request request 请求
     * @return PushResponse 推送结果 or null
     */
    PushResponse sender(PushRequest request);
}