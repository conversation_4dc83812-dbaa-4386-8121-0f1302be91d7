package com.ly.travel.car.tradecore.integration.client.travelx.impl;

import com.ly.travel.car.tradecore.integration.client.travelx.TravelXClient;
import com.ly.travelx.trade.core.facade.CarFacade;
import com.ly.travelx.trade.core.facade.car.modify.CarModifyLinkPhoneRequestDTO;
import com.ly.travelx.trade.core.facade.car.modify.CarModifyLinkPhoneResponseDTO;
import com.ly.travelx.trade.core.facade.car.reassign.CarReassignApplyRequestDTO;
import com.ly.travelx.trade.core.facade.car.reassign.CarReassignApplyResponseDTO;
import com.ly.travelx.trade.core.facade.car.refund.CarRefundApplyRequestDTO;
import com.ly.travelx.trade.core.facade.car.refund.CarRefundApplyResponseDTO;
import com.ly.travelx.trade.core.facade.car.refund.CarRefundValidateRequestDTO;
import com.ly.travelx.trade.core.facade.car.refund.CarRefundValidateResponseDTO;
import org.springframework.stereotype.Service;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.tradecore.integration.client.BaseInvokerClient;
import com.ly.travel.car.tradecore.integration.error.IntegrationException;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
@Service("travelXClient")
public class TravelXClientImpl extends BaseInvokerClient implements TravelXClient {
    @Resource
    private CarFacade travelXTradeFacade;

    /**
     * 取消+退款
     * @param request request
     * @return the CancelResponse
     */
    @Override
    public CarRefundApplyResponseDTO cancel(CarRefundApplyRequestDTO request) throws IntegrationException {
        CarRefundApplyResponseDTO response = super.invoke(request, req -> travelXTradeFacade.refundApply(req), "travelXTradeFacade#refundApply");
        validateResponseDTO(response, "travelXTradeFacade#refundApply");
        return response;
    }

    /**
     * 查询取消费
     * @param request
     * @return
     * @throws IntegrationException
     */
    @Override
    public CarRefundValidateResponseDTO queryCancelFee(CarRefundValidateRequestDTO request) throws IntegrationException {
        CarRefundValidateResponseDTO response = super.invoke(request, req -> travelXTradeFacade.refundValidate(req), "travelXTradeFacade#refundValidate");
        validateResponseDTO(response, "travelXTradeFacade#refundValidate");
        return response;
    }

    /**
     * 改派
     *
     * @param request request
     * @return
     */
    @Override
    public CarReassignApplyResponseDTO reassignment(CarReassignApplyRequestDTO request) throws IntegrationException {
        CarReassignApplyResponseDTO response = super.invoke(request, req -> travelXTradeFacade.reassignApply(req), "travelXTradeFacade#reassignApply");
        validateResponseDTO(response, "travelXTradeFacade#reassignApply");
        return response;
    }

    /**
     * 修改乘客手机号
     *
     * @param request
     * @return
     * @throws IntegrationException
     */
    @Override
    public CarModifyLinkPhoneResponseDTO modifyPassengerPhone(CarModifyLinkPhoneRequestDTO request) throws IntegrationException {
        CarModifyLinkPhoneResponseDTO response = super.invoke(request, req -> travelXTradeFacade.modifyLinkPhone(req), "travelXTradeFacade#modifyPassengerPhone");
        validateResponseDTO(response, "travelXTradeFacade#modifyPassengerPhone");
        return response;
    }

    /**
     * Validate response dto.
     */
    private static void validateResponseDTO(BaseResponseDTO responseDTO, String api) throws IntegrationException {
        if (responseDTO == null) {
            throw new IntegrationException(ERROR.responseIsNull(api + " response is null"));
        }
        if (!responseDTO.isSuccess()) {
            throw new IntegrationException(ERROR.apiInvokeError(api, responseDTO.getErrorCode(), responseDTO.getErrorMessage()));
        }
    }
}
