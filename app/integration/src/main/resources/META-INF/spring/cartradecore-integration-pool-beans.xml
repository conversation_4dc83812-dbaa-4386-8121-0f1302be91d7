<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd"
       default-autowire="byName">

    <alias name="infraEventMulticaster" alias="eventMulticaster"/>
    <!--    sof默认定义了eventThreadPool-->
    <bean id="infraEventMulticaster"
          class="com.ly.travel.car.tradecore.integration.client.event.TicketEventMulticaster">
        <property name="taskExecutor" ref="eventThreadPool"/>
        <property name="executorMaps">
            <map>
                <entry key="LOG" value-ref="logEventThreadPool"/>
                <entry key="SUPPLIER_CREATE" value-ref="bookEventThreadPool"/>
            </map>
        </property>
    </bean>

    <!-- 供应商创单 -->
    <bean id="bookEventThreadPool"
          class="com.ly.travel.car.tradecore.integration.client.event.MDCThreadPoolTaskExecutor">
        <property name="corePoolSize" value="8"/>
        <property name="maxPoolSize" value="32"/>
        <property name="queueCapacity" value="256"/>
        <property name="keepAliveSeconds" value="300"/>
        <property name="threadNamePrefix" value="ticket-log-event"/>
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

    <!-- 日志 -->
    <bean id="logEventThreadPool"
          class="com.ly.travel.car.tradecore.integration.client.event.MDCThreadPoolTaskExecutor">
        <property name="corePoolSize" value="8"/>
        <property name="maxPoolSize" value="32"/>
        <property name="queueCapacity" value="256"/>
        <property name="keepAliveSeconds" value="300"/>
        <property name="threadNamePrefix" value="ticket-log-event"/>
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

</beans>